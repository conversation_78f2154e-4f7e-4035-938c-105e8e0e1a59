import os

# from agents.code_preprocessor import project_root

# Project and Test Roots
# project_root_path = project_root
print(f"paths.py: {__file__}")
project_root_path = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
# project_root_path = os.path.normpath(os.path.join(os.path.abspath(os.getcwd()), ".."))
print(f"project_root_path: {project_root_path}")
tests_path = os.path.join(project_root_path, "tests")
print(f"tests_path: {tests_path}")

# Tests Subdirectories
# Code Sample Files and Paths
code_samples_path = os.path.join(tests_path, "in")
print(f"code_samples_path: {code_samples_path}")
test_resources_path = os.path.join(tests_path, "resources")
print(f"test_resources_path: {test_resources_path}")
test_code_samples_path = os.path.join(test_resources_path, "code_samples")
print(f"test_code_samples_path: {test_code_samples_path}")
cobol_path = os.path.join(test_code_samples_path, "CBACT01C.cbl")
print(f"cobol_path: {cobol_path}")
copybook_path = os.path.join(test_code_samples_path, "CVACT01Y.cpy")
print(f"copybook_path: {copybook_path}")
cobol_app_archive_path = os.path.join(test_code_samples_path, "aws-mainframe-modernization-carddemo-main.zip")
print(f"cobol_app_archive_path: {cobol_app_archive_path}")
cobol_app_AI_generated_cobol_app = os.path.join(test_code_samples_path, "PAYROLL")
print(f"cobol_app_archive_path: {cobol_app_archive_path}")
config_file_path = os.path.join(test_resources_path, "config.yaml")
print(f"config_file_path: {config_file_path}")
neo4j_config_path = os.path.join(test_resources_path, "neo4j_config.yaml")
print(f"neo4j_config_path: {neo4j_config_path}")
module_spec_doc_json_template_file_path = os.path.join(test_resources_path, "prompts", "documentation_gen", "module_spec_doc_template.json")

# Output directory and subdirectories
tests_out_path = os.path.join(tests_path,"out")
if not os.path.exists(tests_out_path):
    os.mkdir(tests_out_path)
print(f"tests_out_path: {tests_out_path}")
# Working Dir And Subdirectories
working_dir_path = os.path.join(tests_out_path,"full_conversion")
if not os.path.exists(working_dir_path):
    os.mkdir(working_dir_path)
print(f"working_dir_path: {working_dir_path}")
cobol_parser_temp_output_path = os.path.join(working_dir_path, "cobol_parser_temp_output")
ir_path = os.path.join(working_dir_path, "ir.json")
print(f"ir_path: {ir_path}")
print(f"cobol_parser_temp_output_path: {cobol_parser_temp_output_path}")
organized_path = os.path.join(working_dir_path,"organized")
print(f"organized_path: {organized_path}")
organized_cobol_dir_path = os.path.join(organized_path, "cobol")
print(f"organized_cobol_dir_path: {organized_cobol_dir_path}")
organized_cobol_file_path = os.path.join(organized_cobol_dir_path, "CBACT01C.cbl")
print(f"organized_cobol_file_path: {organized_cobol_file_path}")
organized_copybook_dir_path = os.path.join(organized_cobol_dir_path, "copybook")
print(f"organized_copybook_dir_path: {organized_copybook_dir_path}")
organized_copybook_file_path = os.path.join(organized_copybook_dir_path, "CVACT01Y.cpy")
print(f"organized_copybook_file_path: {copybook_path}")
