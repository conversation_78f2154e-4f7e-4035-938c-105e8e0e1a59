      *================================================================
      * PROGRAM GCSMSQ00.CBL - GENERAL CONTROL SYSTEM MESSAGE QUEUE
      * Called by: CPBD888 and related programs
      * Purpose: Message queue management
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. GCSMSQ00.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'GCSMSQ00'.
       
       LINKAGE SECTION.
       01  LS-MESSAGE-DATA.
           05  LS-MESSAGE-TYPE         PIC X(04).
           05  LS-MESSAGE-TEXT         PIC X(200).
           05  LS-MESSAGE-PRIORITY     PIC 9(01).
           05  LS-QUEUE-NAME           PIC X(08).
           05  LS-STATUS-CODE          PIC X(02).
       
       PROCEDURE DIVISION USING LS-MESSAGE-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'GCSMSQ00: MESSAGE QUEUE PROCESSING'
           
           EVALUATE LS-MESSAGE-TYPE
               WHEN 'SEND'
                   DISPLAY 'SENDING MESSAGE: ' LS-MESSAGE-TEXT(1:50)
                   MOVE '00' TO LS-STATUS-CODE
               WHEN 'RECV'
                   DISPLAY 'RECEIVING MESSAGE FROM: ' LS-QUEUE-NAME
                   MOVE '00' TO LS-STATUS-CODE
               WHEN OTHER
                   MOVE '08' TO LS-STATUS-CODE
           END-EVALUATE
           
           MOVE 0 TO RETURN-CODE
           GOBACK.
