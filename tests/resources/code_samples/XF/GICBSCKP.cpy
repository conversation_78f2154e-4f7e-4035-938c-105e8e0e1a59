      *================================================================
      * COPYBOOK GICBSCKP.CPY - CHECKPOINT/RESTART PARAMETERS
      * Used by: CPBD888 and related programs
      * Purpose: Define checkpoint restart control block
      * Last Modified: 01/15/2024
      *================================================================
       01  CHECKPOINT-RESTART-PARMS.
           05  CRP-CHECKPOINT-FREQ         PIC 9(06) VALUE 1000.
           05  CRP-RESTART-FLAG            PIC X(01) VALUE 'N'.
               88  CRP-RESTART-REQUIRED    VALUE 'Y'.
               88  CRP-NO-RESTART          VALUE 'N'.
           05  CRP-CHECKPOINT-ID           PIC X(08).
           05  CRP-LAST-CHECKPOINT-TIME    PIC X(08).
           05  CRP-RECORDS-PROCESSED       PIC 9(09) COMP.
           05  CRP-CHECKPOINT-COUNT        PIC 9(06) COMP.
           05  CRP-ERROR-COUNT             PIC 9(06) COMP.
           05  CRP-RESTART-POINT           PIC X(20).
           05  CRP-FILLER                  PIC X(30).
