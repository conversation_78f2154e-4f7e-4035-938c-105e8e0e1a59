      *================================================================
      * COPYBOOK: CUSTOMER-INTERFACE.CPY
      * Purpose: Standard customer data interface for program calls
      * Used by: CCPBS010, CCPBS016, and related programs
      * Last Modified: 01/15/2024
      *================================================================
      
      *================================================================
      * CUSTOMER PROCESSING INTERFACE
      *================================================================
       01  CUSTOMER-INTERFACE.
           05  CI-CUSTOMER-ID          PIC X(09).
           05  CI-ACCOUNT-NUMBER       PIC X(20).
           05  CI-CUSTOMER-NAME        PIC X(30).
           05  CI-ACCOUNT-TYPE         PIC X(02).
           05  CI-ACCOUNT-STATUS       PIC X(01).
           05  CI-VALIDATION-FLAG      PIC X(01).
               88  CI-VALIDATION-OK    VALUE 'Y'.
               88  CI-VALIDATION-FAIL  VALUE 'N'.
           05  CI-PROCESS-FLAG         PIC X(01).
               88  CI-PROCESS-OK       VALUE 'Y'.
               88  CI-PROCESS-FAIL     VALUE 'N'.
           05  CI-ERROR-CODE           PIC X(04).
           05  CI-ERROR-MESSAGE        PIC X(80).
           05  CI-RETURN-CODE          PIC 9(04).
           05  CI-PROGRAM-ID           PIC X(08).
           05  CI-FILLER               PIC X(20).
