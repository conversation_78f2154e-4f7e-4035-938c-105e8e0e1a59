//CPBD888  JOB (ACCT),'CARD PROCESSING',CLASS=A,MSGCLASS=H,
//         MSGLEVEL=(1,1),TIME=(0,30),REGION=4M,
//         NOTIFY=&SYSUID
//*================================================================
//* CARD PROCESSING AND EMBOSSING SYSTEM
//* MAIN CARD PROCESSING JOB
//* AUTHOR: CARD PROCESSING TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//JOBLIB   DD DSN=CARD.LOAD.LIB,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP010: MAIN CARD PROCESSING
//*----------------------------------------------------------------
//STEP010  EXEC PGM=CPBD888,PARM='PROD'
//STEPLIB  DD DSN=CARD.LOAD.LIB,DISP=SHR
//         DD DSN=IMS.V15.RESLIB,DISP=SHR
//         DD DSN=CEE.SCEERUN,DISP=SHR
//PRMFILE  DD DSN=CARD.PARMS.DATA,DISP=SHR
//HEXPRNT  DD DSN=CARD.HEX.PRINT(+1),
//         DISP=(NEW,CATLG,DELETE),
//         SPACE=(TRK,(10,5),RLSE),
//         DCB=(RECFM=FB,LRECL=80,BLKSIZE=0),
//         UNIT=SYSDA
//CARDREQ  DD DSN=CARD.REQUEST.DATA,DISP=SHR
//NEXTNUM  DD DSN=CARD.NEXT.NUMBER,DISP=SHR
//CHIPKEY  DD DSN=CARD.CHIP.KEY.VSAM,DISP=SHR
//DAYIFILE DD DSN=CARD.DAY1.VSAM,DISP=SHR
//SYSOUT   DD SYSOUT=*
//SYSPRINT DD SYSOUT=*
//SYSUDUMP DD SYSOUT=*
//*
//*----------------------------------------------------------------
//* STEP020: ARCHIVE PROCESSING RESULTS
//*----------------------------------------------------------------
//STEP020  EXEC PGM=IEBGENER,COND=(0,NE,STEP010)
//SYSPRINT DD SYSOUT=*
//SYSUT1   DD DSN=CARD.HEX.PRINT(0),DISP=SHR
//SYSUT2   DD DSN=CARD.ARCHIVE.DATA(+1),
//         DISP=(NEW,CATLG,DELETE),
//         LIKE=CARD.HEX.PRINT,
//         UNIT=TAPE
//SYSIN    DD DUMMY
