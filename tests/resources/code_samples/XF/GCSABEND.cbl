      *================================================================
      * PROGRAM GCSABEND.CBL - GENERAL CONTROL SYSTEM ABEND HANDLER
      * Called by: CPBD888 and related programs
      * Purpose: Abnormal termination handling
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. GCSABEND.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'GCSABEND'.
       
       LINKAGE SECTION.
       01  LS-ABEND-DATA.
           05  LS-ABEND-CODE           PIC X(04).
           05  LS-ABEND-REASON         PIC X(80).
           05  LS-DUMP-FLAG            PIC X(01).
           05  LS-CLEANUP-FLAG         PIC X(01).
       
       PROCEDURE DIVISION USING LS-ABEND-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'GCSABEND: ABEND PROCESSING STARTED'
           DISPLAY 'ABEND CODE: ' LS-ABEND-CODE
           DISPLAY 'ABEND REASON: ' LS-ABEND-REASON
           
           MOVE 'Y' TO LS-DUMP-FLAG
           MOVE 'Y' TO LS-CLEANUP-FLAG
           
           DISPLAY 'GCSABEND: SYSTEM TERMINATED ABNORMALLY'
           MOVE 16 TO RETURN-CODE
           GOBACK.
