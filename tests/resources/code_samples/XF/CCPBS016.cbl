      *================================================================
      * PROGRAM CCPBS016.CBL - BUSINESS SERVICE MODULE 016
      * Called by: CCPBS010 and related programs
      * Purpose: Account processing and validation with card processing
      * Dependencies: Calls CCPBS022 for card processing
      * Last Modified: 01/15/2024
      * Enhanced: Complex paragraph dependencies, file I/O, DB2 SQL
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CCPBS016.

       ENVIRONMENT DIVISION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT ACCOUNT-FILE         ASSIGN TO ACCTFILE
                  ACCESS IS RANDOM
                  ORGANIZATION IS INDEXED
                  RECORD KEY IS ACCT-KEY
                  FILE STATUS IS WS-ACCT-FILE-STATUS.

           SELECT TRANSACTION-FILE     ASSIGN TO TRANFILE
                  ACCESS IS SEQUENTIAL
                  ORGANIZATION IS SEQUENTIAL
                  FILE STATUS IS WS-TRAN-FILE-STATUS.

       DATA DIVISION.
       FILE SECTION.
       FD  ACCOUNT-FILE
           RECORD CONTAINS 150 CHARACTERS
           DATA RECORD IS ACCOUNT-RECORD.
       01  ACCOUNT-RECORD.
           05  ACCT-KEY                PIC X(20).
           05  ACCT-CUSTOMER-ID        PIC 9(09).
           05  ACCT-TYPE               PIC X(02).
           05  ACCT-STATUS             PIC X(01).
           05  ACCT-BALANCE            PIC S9(11)V99 COMP-3.
           05  ACCT-OPEN-DATE          PIC X(08).
           05  ACCT-LAST-ACTIVITY      PIC X(08).
           05  ACCT-FILLER             PIC X(100).

       FD  TRANSACTION-FILE
           RECORD CONTAINS 80 CHARACTERS
           DATA RECORD IS TRANSACTION-RECORD.
       01  TRANSACTION-RECORD.
           05  TRAN-TYPE               PIC X(02).
           05  TRAN-ACCOUNT-KEY        PIC X(20).
           05  TRAN-AMOUNT             PIC S9(09)V99 COMP-3.
           05  TRAN-DATE               PIC X(08).
           05  TRAN-TIME               PIC X(06).
           05  TRAN-REFERENCE          PIC X(20).
           05  TRAN-FILLER             PIC X(15).

       WORKING-STORAGE SECTION.
      *================================================================
      * PROGRAM CONTROL VARIABLES
      *================================================================
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CCPBS016'.
       01  WS-RETURN-CODE              PIC 9(04) VALUE 0.
       01  WS-ACCT-FILE-STATUS         PIC X(02) VALUE '00'.
           88  WS-ACCT-FILE-OK         VALUE '00'.
           88  WS-ACCT-FILE-NOT-FOUND  VALUE '23'.
       01  WS-TRAN-FILE-STATUS         PIC X(02) VALUE '00'.
           88  WS-TRAN-FILE-OK         VALUE '00'.
           88  WS-TRAN-FILE-EOF        VALUE '10'.

      *================================================================
      * CALLED PROGRAM VARIABLES
      *================================================================
       01  WS-CCPBS022-PROG            PIC X(08) VALUE 'CCPBS022'.
      *================================================================
      * STANDARDIZED CARD INTERFACE (FOR CALLING CCPBS022)
      *================================================================
           COPY CARD-INTERFACE.

       01  WS-CARD-PROCESSING-DATA.
           05  WS-CARD-NUMBER          PIC X(16).
           05  WS-CARD-TYPE            PIC X(02).
           05  WS-EXPIRATION-DATE      PIC X(06).
           05  WS-CARD-CUSTOMER-ID     PIC X(09).
           05  WS-CARD-ACCOUNT-NUMBER  PIC X(20).
           05  WS-EMBOSS-FLAG          PIC X(01).
           05  WS-PRIORITY-CODE        PIC X(01).
           05  WS-CARD-STATUS          PIC X(01).
           05  WS-CARD-ERROR-CODE      PIC X(04).
           05  WS-CARD-ERROR-MESSAGE   PIC X(80).
           05  WS-CARD-RETURN-CODE     PIC 9(04).
           05  WS-CARD-FILLER          PIC X(20).

      *================================================================
      * PROCESSING CONTROL VARIABLES
      *================================================================
       01  WS-PROCESS-INDICATORS.
           05  WS-ACCOUNT-FOUND-FLAG   PIC X(01) VALUE 'N'.
               88  WS-ACCOUNT-FOUND    VALUE 'Y'.
               88  WS-ACCOUNT-NOT-FOUND VALUE 'N'.
           05  WS-CARD-REQUIRED-FLAG   PIC X(01) VALUE 'N'.
               88  WS-CARD-REQUIRED    VALUE 'Y'.
               88  WS-NO-CARD-REQUIRED VALUE 'N'.
           05  WS-TRANSACTION-FLAG     PIC X(01) VALUE 'Y'.
               88  WS-PROCESS-TRANSACTIONS VALUE 'Y'.
               88  WS-SKIP-TRANSACTIONS VALUE 'N'.

      *================================================================
      * COUNTERS AND ACCUMULATORS
      *================================================================
       01  WS-COUNTERS.
           05  WS-ACCOUNT-READ-COUNT   PIC 9(06) VALUE 0.
           05  WS-TRANSACTION-COUNT    PIC 9(06) VALUE 0.
           05  WS-CARD-PROCESS-COUNT   PIC 9(06) VALUE 0.
           05  WS-ERROR-COUNT          PIC 9(06) VALUE 0.

      *================================================================
      * DB2 SQL VARIABLES
      *================================================================
           EXEC SQL INCLUDE SQLCA END-EXEC.

       01  WS-SQL-ACCOUNT-VARS.
           05  :ACCT-NBR               PIC X(20).
           05  :ACCT-BALANCE-AMT       PIC S9(11)V99 COMP-3.
           05  :ACCT-STATUS-CD         PIC X(01).
           05  :LAST-ACTIVITY-DATE     PIC X(10).

           EXEC SQL DECLARE ACCT_CURSOR CURSOR FOR
               SELECT ACCOUNT_NUMBER, BALANCE_AMOUNT, STATUS_CODE,
                      LAST_ACTIVITY_DATE
               FROM TCH_ACCOUNT_MASTER
               WHERE ACCOUNT_NUMBER = :ACCT-NBR
                 AND STATUS_CODE IN ('A', 'O')
               FOR UPDATE OF BALANCE_AMOUNT, LAST_ACTIVITY_DATE
           END-EXEC.

       LINKAGE SECTION.
      *================================================================
      * STANDARDIZED CUSTOMER INTERFACE (RECEIVED FROM CCPBS010)
      *================================================================
           COPY CUSTOMER-INTERFACE.

       01  LS-CUSTOMER-DATA.
           05  LS-CUSTOMER-ID          PIC X(09).
           05  LS-ACCOUNT-NUMBER       PIC X(20).
           05  LS-CUSTOMER-NAME        PIC X(30).
           05  LS-ACCOUNT-TYPE         PIC X(02).
           05  LS-ACCOUNT-STATUS       PIC X(01).
           05  LS-VALIDATION-FLAG      PIC X(01).
           05  LS-PROCESS-FLAG         PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).
           05  LS-ERROR-MESSAGE        PIC X(80).
           05  LS-RETURN-CODE          PIC 9(04).
           05  LS-PROGRAM-ID           PIC X(08).
           05  LS-FILLER               PIC X(20).

       PROCEDURE DIVISION USING LS-CUSTOMER-DATA.

      *================================================================
      * MAIN PROCESSING CONTROL
      *================================================================
       0000-MAIN-PROCESSING.
           DISPLAY 'CCPBS016: ACCOUNT PROCESSING STARTED'

           PERFORM 1000-INITIALIZATION     THRU 1000-EXIT
           PERFORM 2000-VALIDATE-ACCOUNT   THRU 2000-EXIT
           PERFORM 3000-PROCESS-ACCOUNT    THRU 3000-EXIT
           PERFORM 4000-PROCESS-CARDS      THRU 4000-EXIT
           PERFORM 9000-CLEANUP            THRU 9000-EXIT

           DISPLAY 'CCPBS016: PROCESSING COMPLETE'
           GOBACK.

      *================================================================
      * INITIALIZATION PROCESSING
      *================================================================
       1000-INITIALIZATION.
           DISPLAY 'CCPBS016: INITIALIZATION STARTED'

           MOVE ZEROS TO WS-ACCOUNT-READ-COUNT
                         WS-TRANSACTION-COUNT
                         WS-CARD-PROCESS-COUNT
                         WS-ERROR-COUNT

           PERFORM 1100-OPEN-FILES         THRU 1100-EXIT
           PERFORM 1200-SETUP-DB2-CURSOR   THRU 1200-EXIT
           PERFORM 1300-VALIDATE-INPUT     THRU 1300-EXIT.
       1000-EXIT.
           EXIT.

      *================================================================
      * FILE OPERATIONS
      *================================================================
       1100-OPEN-FILES.
           OPEN I-O ACCOUNT-FILE

           IF NOT WS-ACCT-FILE-OK
               DISPLAY 'ERROR OPENING ACCOUNT FILE: ' WS-ACCT-FILE-STATUS
               MOVE 'E021' TO LS-ERROR-CODE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           END-IF

           OPEN INPUT TRANSACTION-FILE

           IF NOT WS-TRAN-FILE-OK
               DISPLAY 'ERROR OPENING TRANSACTION FILE: ' WS-TRAN-FILE-STATUS
               MOVE 'E022' TO LS-ERROR-CODE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           END-IF.
       1100-EXIT.
           EXIT.

      *================================================================
      * DB2 CURSOR SETUP
      *================================================================
       1200-SETUP-DB2-CURSOR.
           MOVE LS-ACCOUNT-NUMBER TO :ACCT-NBR

           EXEC SQL
               OPEN ACCT_CURSOR
           END-EXEC

           EVALUATE SQLCODE
               WHEN 0
                   DISPLAY 'DB2 ACCOUNT CURSOR OPENED'
               WHEN OTHER
                   DISPLAY 'DB2 CURSOR OPEN FAILED: ' SQLCODE
                   MOVE 'E023' TO LS-ERROR-CODE
                   MOVE 8 TO WS-RETURN-CODE
           END-EVALUATE.
       1200-EXIT.
           EXIT.

      *================================================================
      * INPUT VALIDATION
      *================================================================
       1300-VALIDATE-INPUT.
           IF LS-ACCOUNT-NUMBER = SPACES OR LS-CUSTOMER-ID = SPACES
               MOVE 'E002' TO LS-ERROR-CODE
               MOVE 'INVALID ACCOUNT OR CUSTOMER DATA' TO LS-ERROR-MESSAGE
               MOVE 'N' TO LS-PROCESS-FLAG
               MOVE 8 TO WS-RETURN-CODE
               SET WS-ACCOUNT-NOT-FOUND TO TRUE
           ELSE
               MOVE 'Y' TO LS-PROCESS-FLAG
               MOVE SPACES TO LS-ERROR-CODE
               MOVE SPACES TO LS-ERROR-MESSAGE
               MOVE 0 TO WS-RETURN-CODE
               SET WS-ACCOUNT-FOUND TO TRUE
           END-IF.
       1300-EXIT.
           EXIT.

      *================================================================
      * ACCOUNT VALIDATION WITH FILE AND DB2 ACCESS
      *================================================================
       2000-VALIDATE-ACCOUNT.
           IF WS-ACCOUNT-FOUND
               PERFORM 2100-READ-ACCOUNT-FILE  THRU 2100-EXIT
               PERFORM 2200-FETCH-DB2-ACCOUNT  THRU 2200-EXIT
               PERFORM 2300-CROSS-VALIDATE     THRU 2300-EXIT
           END-IF.
       2000-EXIT.
           EXIT.

      *================================================================
      * ACCOUNT FILE ACCESS
      *================================================================
       2100-READ-ACCOUNT-FILE.
           MOVE LS-ACCOUNT-NUMBER TO ACCT-KEY

           READ ACCOUNT-FILE

           EVALUATE WS-ACCT-FILE-STATUS
               WHEN '00'
                   ADD 1 TO WS-ACCOUNT-READ-COUNT
                   DISPLAY 'ACCOUNT RECORD FOUND: ' ACCT-KEY
                   MOVE ACCT-STATUS TO LS-ACCOUNT-STATUS
                   MOVE ACCT-TYPE TO LS-ACCOUNT-TYPE
               WHEN '23'
                   DISPLAY 'ACCOUNT NOT FOUND IN FILE: ' ACCT-KEY
                   MOVE 'W005' TO LS-ERROR-CODE
                   SET WS-ACCOUNT-NOT-FOUND TO TRUE
               WHEN OTHER
                   DISPLAY 'ACCOUNT FILE READ ERROR: ' WS-ACCT-FILE-STATUS
                   MOVE 'E024' TO LS-ERROR-CODE
                   ADD 1 TO WS-ERROR-COUNT
                   SET WS-ACCOUNT-NOT-FOUND TO TRUE
           END-EVALUATE.
       2100-EXIT.
           EXIT.

      *================================================================
      * DB2 ACCOUNT FETCH
      *================================================================
       2200-FETCH-DB2-ACCOUNT.
           EXEC SQL
               FETCH ACCT_CURSOR INTO :ACCT-NBR,
                                     :ACCT-BALANCE-AMT,
                                     :ACCT-STATUS-CD,
                                     :LAST-ACTIVITY-DATE
           END-EXEC

           EVALUATE SQLCODE
               WHEN 0
                   DISPLAY 'DB2 ACCOUNT FETCHED: ' :ACCT-NBR
                   DISPLAY 'BALANCE: ' :ACCT-BALANCE-AMT
                   DISPLAY 'STATUS: ' :ACCT-STATUS-CD
               WHEN 100
                   DISPLAY 'ACCOUNT NOT FOUND IN DB2: ' :ACCT-NBR
                   MOVE 'W006' TO LS-ERROR-CODE
               WHEN OTHER
                   DISPLAY 'DB2 FETCH ERROR: ' SQLCODE
                   MOVE 'E025' TO LS-ERROR-CODE
                   ADD 1 TO WS-ERROR-COUNT
           END-EVALUATE.
       2200-EXIT.
           EXIT.

      *================================================================
      * CROSS-VALIDATION BETWEEN FILE AND DB2
      *================================================================
       2300-CROSS-VALIDATE.
           IF SQLCODE = 0 AND WS-ACCT-FILE-OK
               IF ACCT-STATUS NOT = :ACCT-STATUS-CD
                   DISPLAY 'STATUS MISMATCH - FILE: ' ACCT-STATUS
                           ' DB2: ' :ACCT-STATUS-CD
                   MOVE 'W007' TO LS-ERROR-CODE
               END-IF

               IF ACCT-BALANCE NOT = :ACCT-BALANCE-AMT
                   DISPLAY 'BALANCE MISMATCH - FILE: ' ACCT-BALANCE
                           ' DB2: ' :ACCT-BALANCE-AMT
                   MOVE 'W008' TO LS-ERROR-CODE
               END-IF
           END-IF.
       2300-EXIT.
           EXIT.

      *================================================================
      * ACCOUNT PROCESSING WITH TRANSACTIONS
      *================================================================
       3000-PROCESS-ACCOUNT.
           IF WS-ACCOUNT-FOUND AND SQLCODE = 0
               PERFORM 3100-PROCESS-TRANSACTIONS THRU 3100-EXIT
                   UNTIL WS-TRAN-FILE-EOF
                      OR WS-ERROR-COUNT > 10
               PERFORM 3200-UPDATE-ACCOUNT-BALANCE THRU 3200-EXIT
               PERFORM 3300-DETERMINE-CARD-NEED    THRU 3300-EXIT
           END-IF.
       3000-EXIT.
           EXIT.

      *================================================================
      * TRANSACTION PROCESSING LOOP
      *================================================================
       3100-PROCESS-TRANSACTIONS.
           READ TRANSACTION-FILE

           EVALUATE WS-TRAN-FILE-STATUS
               WHEN '00'
                   ADD 1 TO WS-TRANSACTION-COUNT
                   PERFORM 3110-VALIDATE-TRANSACTION THRU 3110-EXIT
                   PERFORM 3120-APPLY-TRANSACTION    THRU 3120-EXIT
               WHEN '10'
                   DISPLAY 'END OF TRANSACTION FILE'
               WHEN OTHER
                   DISPLAY 'TRANSACTION FILE ERROR: ' WS-TRAN-FILE-STATUS
                   MOVE 'E026' TO LS-ERROR-CODE
                   ADD 1 TO WS-ERROR-COUNT
           END-EVALUATE.
       3100-EXIT.
           EXIT.

       3110-VALIDATE-TRANSACTION.
           IF TRAN-ACCOUNT-KEY NOT = LS-ACCOUNT-NUMBER
               DISPLAY 'TRANSACTION ACCOUNT MISMATCH: ' TRAN-ACCOUNT-KEY
               MOVE 'W009' TO LS-ERROR-CODE
               SET WS-SKIP-TRANSACTIONS TO TRUE
           ELSE
               SET WS-PROCESS-TRANSACTIONS TO TRUE
           END-IF.
       3110-EXIT.
           EXIT.

       3120-APPLY-TRANSACTION.
           IF WS-PROCESS-TRANSACTIONS
               EVALUATE TRAN-TYPE
                   WHEN 'CR'
                       ADD TRAN-AMOUNT TO ACCT-BALANCE
                       DISPLAY 'CREDIT APPLIED: ' TRAN-AMOUNT
                   WHEN 'DB'
                       SUBTRACT TRAN-AMOUNT FROM ACCT-BALANCE
                       DISPLAY 'DEBIT APPLIED: ' TRAN-AMOUNT
                   WHEN OTHER
                       DISPLAY 'UNKNOWN TRANSACTION TYPE: ' TRAN-TYPE
                       MOVE 'W010' TO LS-ERROR-CODE
               END-EVALUATE

               MOVE FUNCTION CURRENT-DATE(1:8) TO ACCT-LAST-ACTIVITY
           END-IF.
       3120-EXIT.
           EXIT.

      *================================================================
      * ACCOUNT BALANCE UPDATE
      *================================================================
       3200-UPDATE-ACCOUNT-BALANCE.
           REWRITE ACCOUNT-RECORD

           IF WS-ACCT-FILE-OK
               DISPLAY 'ACCOUNT BALANCE UPDATED: ' ACCT-BALANCE

               MOVE ACCT-BALANCE TO :ACCT-BALANCE-AMT
               MOVE ACCT-LAST-ACTIVITY TO :LAST-ACTIVITY-DATE

               EXEC SQL
                   UPDATE TCH_ACCOUNT_MASTER
                   SET BALANCE_AMOUNT = :ACCT-BALANCE-AMT,
                       LAST_ACTIVITY_DATE = :LAST-ACTIVITY-DATE
                   WHERE CURRENT OF ACCT_CURSOR
               END-EXEC

               IF SQLCODE = 0
                   DISPLAY 'DB2 ACCOUNT UPDATED'
                   EXEC SQL COMMIT END-EXEC
               ELSE
                   DISPLAY 'DB2 UPDATE FAILED: ' SQLCODE
                   EXEC SQL ROLLBACK END-EXEC
                   MOVE 'E027' TO LS-ERROR-CODE
               END-IF
           ELSE
               DISPLAY 'ACCOUNT FILE UPDATE ERROR: ' WS-ACCT-FILE-STATUS
               MOVE 'E028' TO LS-ERROR-CODE
               ADD 1 TO WS-ERROR-COUNT
           END-IF.
       3200-EXIT.
           EXIT.

      *================================================================
      * CARD REQUIREMENT DETERMINATION
      *================================================================
       3300-DETERMINE-CARD-NEED.
           IF ACCT-TYPE = 'CC' OR ACCT-TYPE = 'DC'
               IF ACCT-BALANCE > 0
                   SET WS-CARD-REQUIRED TO TRUE
                   DISPLAY 'CARD PROCESSING REQUIRED FOR: ' ACCT-KEY
               ELSE
                   SET WS-NO-CARD-REQUIRED TO TRUE
                   DISPLAY 'INSUFFICIENT BALANCE FOR CARD: ' ACCT-BALANCE
               END-IF
           ELSE
               SET WS-NO-CARD-REQUIRED TO TRUE
               DISPLAY 'ACCOUNT TYPE DOES NOT REQUIRE CARD: ' ACCT-TYPE
           END-IF.
       3300-EXIT.
           EXIT.

      *================================================================
      * CARD PROCESSING WITH CROSS-PROGRAM CALL
      *================================================================
       4000-PROCESS-CARDS.
           IF WS-CARD-REQUIRED
               PERFORM 4100-PREPARE-CARD-DATA  THRU 4100-EXIT
               PERFORM 4200-CALL-CARD-SERVICE  THRU 4200-EXIT
               PERFORM 4300-PROCESS-CARD-RESULT THRU 4300-EXIT
           END-IF.
       4000-EXIT.
           EXIT.

      *================================================================
      * CARD DATA PREPARATION
      *================================================================
       4100-PREPARE-CARD-DATA.
      *    GENERATE CARD NUMBER FROM CUSTOMER AND ACCOUNT DATA
           STRING LS-CUSTOMER-ID DELIMITED BY SIZE
                  ACCT-KEY(1:7) DELIMITED BY SIZE
                  INTO WS-CARD-NUMBER
           END-STRING

      *    POPULATE CARD DATA FROM LINKAGE AND FILE DATA
           MOVE ACCT-TYPE TO WS-CARD-TYPE
           MOVE FUNCTION CURRENT-DATE(1:6) TO WS-EXPIRATION-DATE
           ADD 2 TO WS-EXPIRATION-DATE(1:2)
           MOVE LS-CUSTOMER-ID TO WS-CARD-CUSTOMER-ID
           MOVE LS-ACCOUNT-NUMBER TO WS-CARD-ACCOUNT-NUMBER
           MOVE 'N' TO WS-EMBOSS-FLAG
           MOVE '1' TO WS-PRIORITY-CODE
           MOVE 'P' TO WS-CARD-STATUS
           MOVE SPACES TO WS-CARD-ERROR-CODE
           MOVE SPACES TO WS-CARD-ERROR-MESSAGE
           MOVE 0 TO WS-CARD-RETURN-CODE.
       4100-EXIT.
           EXIT.

      *================================================================
      * CROSS-PROGRAM CALL TO CCPBS022
      *================================================================
       4200-CALL-CARD-SERVICE.
           CALL WS-CCPBS022-PROG USING WS-CARD-PROCESSING-DATA
               ON EXCEPTION
                   DISPLAY 'CALL TO CCPBS022 FAILED'
                   MOVE 'E029' TO LS-ERROR-CODE
                   ADD 1 TO WS-ERROR-COUNT
               NOT ON EXCEPTION
                   DISPLAY 'CALL TO CCPBS022 SUCCESSFUL'
                   ADD 1 TO WS-CARD-PROCESS-COUNT
           END-CALL.
       4200-EXIT.
           EXIT.

      *================================================================
      * CARD PROCESSING RESULT HANDLING
      *================================================================
       4300-PROCESS-CARD-RESULT.
           IF WS-EMBOSS-FLAG = 'Y'
               DISPLAY 'CARD PROCESSING SUCCESSFUL: ' WS-CARD-NUMBER
               MOVE 'A' TO LS-ACCOUNT-STATUS
               DISPLAY 'CARD STATUS: ' WS-CARD-STATUS
           ELSE
               DISPLAY 'CARD PROCESSING FAILED: ' WS-CARD-ERROR-CODE
               IF WS-CARD-ERROR-CODE NOT = SPACES
                   MOVE WS-CARD-ERROR-CODE TO LS-ERROR-CODE
               END-IF
               IF WS-CARD-ERROR-MESSAGE NOT = SPACES
                   MOVE WS-CARD-ERROR-MESSAGE TO LS-ERROR-MESSAGE
               END-IF
           END-IF.
       4300-EXIT.
           EXIT.

      *================================================================
      * CLEANUP PROCESSING
      *================================================================
       9000-CLEANUP.
           PERFORM 9100-CLOSE-DB2-CURSOR   THRU 9100-EXIT
           PERFORM 9200-CLOSE-FILES        THRU 9200-EXIT
           PERFORM 9300-DISPLAY-TOTALS     THRU 9300-EXIT.
       9000-EXIT.
           EXIT.

       9100-CLOSE-DB2-CURSOR.
           EXEC SQL
               CLOSE ACCT_CURSOR
           END-EXEC

           IF SQLCODE = 0
               DISPLAY 'DB2 ACCOUNT CURSOR CLOSED'
           ELSE
               DISPLAY 'DB2 CURSOR CLOSE ERROR: ' SQLCODE
           END-IF.
       9100-EXIT.
           EXIT.

       9200-CLOSE-FILES.
           CLOSE ACCOUNT-FILE

           IF WS-ACCT-FILE-OK
               DISPLAY 'ACCOUNT FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'ACCOUNT FILE CLOSE ERROR: ' WS-ACCT-FILE-STATUS
           END-IF

           CLOSE TRANSACTION-FILE

           IF WS-TRAN-FILE-OK
               DISPLAY 'TRANSACTION FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'TRANSACTION FILE CLOSE ERROR: ' WS-TRAN-FILE-STATUS
           END-IF.
       9200-EXIT.
           EXIT.

       9300-DISPLAY-TOTALS.
           DISPLAY '================================================'
           DISPLAY 'CCPBS016 PROCESSING TOTALS:'
           DISPLAY 'ACCOUNTS READ    : ' WS-ACCOUNT-READ-COUNT
           DISPLAY 'TRANSACTIONS PROC: ' WS-TRANSACTION-COUNT
           DISPLAY 'CARDS PROCESSED  : ' WS-CARD-PROCESS-COUNT
           DISPLAY 'ERRORS ENCOUNTERED: ' WS-ERROR-COUNT
           DISPLAY '================================================'.
       9300-EXIT.
           EXIT.

      *================================================================
      * ERROR HANDLING AND ABEND ROUTINE
      *================================================================
       9999-ABEND-ROUTINE.
           DISPLAY '************************************************'
           DISPLAY 'CCPBS016 ABNORMAL TERMINATION'
           DISPLAY 'ERROR CODE: ' LS-ERROR-CODE
           DISPLAY 'RETURN CODE: ' WS-RETURN-CODE
           DISPLAY '************************************************'

           PERFORM 9200-CLOSE-FILES THRU 9200-EXIT
           PERFORM 9100-CLOSE-DB2-CURSOR THRU 9100-EXIT

           MOVE WS-RETURN-CODE TO RETURN-CODE
           GOBACK.
       9999-EXIT.
           EXIT.
