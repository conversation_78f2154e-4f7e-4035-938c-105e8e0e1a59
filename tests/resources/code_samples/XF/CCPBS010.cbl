      *================================================================
      * PROGRAM CCPBS010.CBL - BUSINESS SERVICE MODULE 010
      * Called by: CPBD888 and related programs
      * Purpose: Customer validation and processing with IMS/DB2 access
      * Dependencies: Calls CCPBS016 for account validation
      * Last Modified: 01/15/2024
      * Enhanced: Complex paragraph dependencies, IMS DLI, DB2 SQL
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CCPBS010.

       ENVIRONMENT DIVISION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT CUSTOMER-FILE        ASSIGN TO CUSTFILE
                  ACCESS IS RANDOM
                  ORGANIZATION IS INDEXED
                  RECORD KEY IS CUST-KEY
                  FILE STATUS IS WS-CUST-FILE-STATUS.

       DATA DIVISION.
       FILE SECTION.
       FD  CUSTOMER-FILE
           RECORD CONTAINS 200 CHARACTERS
           DATA RECORD IS CUSTOMER-RECORD.
       01  CUSTOMER-RECORD.
           05  CUST-KEY                PIC X(09).
           05  CUST-NAME               PIC X(30).
           05  CUST-ADDRESS            PIC X(50).
           05  CUST-STATUS             PIC X(01).
           05  CUST-ACCOUNT-NUMBER     PIC X(20).
           05  CUST-ACCOUNT-TYPE       PIC X(02).
           05  CUST-FILLER             PIC X(88).

       WORKING-STORAGE SECTION.
      *================================================================
      * PROGRAM CONTROL VARIABLES
      *================================================================
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CCPBS010'.
       01  WS-RETURN-CODE              PIC 9(04) VALUE 0.
       01  WS-CUST-FILE-STATUS         PIC X(02) VALUE '00'.
           88  WS-CUST-FILE-OK         VALUE '00'.
           88  WS-CUST-FILE-NOT-FOUND  VALUE '23'.
           88  WS-CUST-FILE-EOF        VALUE '10'.

      *================================================================
      * CALLED PROGRAM VARIABLES
      *================================================================
       01  WS-CCPBS016-PROG            PIC X(08) VALUE 'CCPBS016'.
       01  WS-CALL-STATUS              PIC X(01) VALUE 'N'.
           88  WS-CALL-SUCCESS         VALUE 'Y'.
           88  WS-CALL-FAILED          VALUE 'N'.

      *================================================================
      * LOOP CONTROL VARIABLES
      *================================================================
       01  WS-LOOP-COUNTER             PIC 9(04) VALUE 0.
       01  WS-MAX-LOOPS                PIC 9(04) VALUE 100.
       01  WS-PROCESS-FLAG             PIC X(01) VALUE 'Y'.
           88  WS-CONTINUE-PROCESSING  VALUE 'Y'.
           88  WS-STOP-PROCESSING      VALUE 'N'.

      *================================================================
      * IMS DLI VARIABLES
      *================================================================
       01  WS-DLI-FUNCTIONS.
           05  FC-GU                   PIC X(04) VALUE 'GU  '.
           05  FC-GN                   PIC X(04) VALUE 'GN  '.
           05  FC-ISRT                 PIC X(04) VALUE 'ISRT'.
           05  FC-REPL                 PIC X(04) VALUE 'REPL'.

       01  WS-IMS-STATUS               PIC X(02) VALUE SPACES.
           88  IMS-STATUS-OK           VALUE SPACES.
           88  IMS-STATUS-NOT-FOUND    VALUE 'GE'.
           88  IMS-STATUS-END-OF-DB    VALUE 'GB'.

      *================================================================
      * IMS SEGMENT LAYOUTS
      *================================================================
       01  AM00-ACCOUNT-MASTER.
           05  AM00-ACCOUNT-KEY        PIC X(20).
           05  AM00-CUSTOMER-ID        PIC 9(09).
           05  AM00-ACCOUNT-TYPE       PIC X(02).
           05  AM00-ACCOUNT-STATUS     PIC X(01).
           05  AM00-OPEN-DATE          PIC X(08).
           05  AM00-BALANCE            PIC S9(13)V99 COMP-3.
           05  AM00-FILLER             PIC X(50).

       01  AMCAM00-SEGMENT             PIC X(150).
       01  AM01-ACCT-CUST-KEY          PIC X(29).
       01  AMCAM01-SEGMENT             PIC X(150).

      *================================================================
      * IMS SEGMENT SEARCH ARGUMENTS (SSA)
      *================================================================
       01  WS-SSA-ACCOUNT-MASTER       PIC X(50).
       01  WS-SSA-CUSTOMER-SEGMENT     PIC X(50).

      *================================================================
      * DB2 SQL VARIABLES
      *================================================================
           EXEC SQL INCLUDE SQLCA END-EXEC.

       01  WS-SQL-HOST-VARS.
           05  :CLPRC-CLIENT-NBR       PIC X(09).
           05  :CLPRC-SYSTEM-TYPE      PIC X(04).
           05  :CLPRC-CURR-DATE        PIC X(10).
           05  :CLPRC-PREV-DATE        PIC X(10).

           EXEC SQL DECLARE C1 CURSOR FOR
               SELECT CLIENT_NBR, SYSTEM_TYPE, CURRENT_DATE
               FROM TCH_CLIENT_PROCESSING
               WHERE CLIENT_NBR = :CLPRC-CLIENT-NBR
               ORDER BY SYSTEM_TYPE
           END-EXEC.

      *================================================================
      * CHECKPOINT/RESTART VARIABLES
      *================================================================
       01  WS-CHECKPOINT-DATA.
           05  WS-CHKP-FREQUENCY       PIC 9(06) VALUE 1000.
           05  WS-CHKP-COUNTER         PIC 9(06) VALUE 0.
           05  WS-FORCE-CHKP-IND       PIC X(01) VALUE 'N'.
               88  FORCE-CHECKPOINT    VALUE 'Y'.

       01  WS-COUNTERS.
           05  WS-READ-COUNTER         PIC 9(09) VALUE 0.
           05  WS-PROCESS-COUNTER      PIC 9(09) VALUE 0.
           05  WS-ERROR-COUNTER        PIC 9(06) VALUE 0.
           05  WS-TOTAL-READ-COUNT     PIC 9(09) VALUE 0.

       LINKAGE SECTION.
      *================================================================
      * STANDARDIZED CUSTOMER INTERFACE
      *================================================================
           COPY CUSTOMER-INTERFACE.

       01  LS-CUSTOMER-DATA.
           05  LS-CUSTOMER-ID          PIC X(09).
           05  LS-ACCOUNT-NUMBER       PIC X(20).
           05  LS-CUSTOMER-NAME        PIC X(30).
           05  LS-ACCOUNT-TYPE         PIC X(02).
           05  LS-ACCOUNT-STATUS       PIC X(01).
           05  LS-VALIDATION-FLAG      PIC X(01).
           05  LS-PROCESS-FLAG         PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).
           05  LS-ERROR-MESSAGE        PIC X(80).
           05  LS-RETURN-CODE          PIC 9(04).
           05  LS-PROGRAM-ID           PIC X(08).
           05  LS-FILLER               PIC X(20).

      *================================================================
      * IMS PCB LINKAGE
      *================================================================
       01  DBDAM01-PCB-MASK            PIC X(100).

       PROCEDURE DIVISION USING LS-CUSTOMER-DATA
                                DBDAM01-PCB-MASK.

      *================================================================
      * MAIN PROCESSING CONTROL
      *================================================================
       0000-MAIN-PROCESSING.
           DISPLAY 'CCPBS010: CUSTOMER VALIDATION STARTED'

           PERFORM 1000-INITIALIZATION     THRU 1000-EXIT
           PERFORM 2000-VALIDATE-CUSTOMER  THRU 2000-EXIT
           PERFORM 3000-PROCESS-ACCOUNTS   THRU 3000-EXIT
           PERFORM 4000-UPDATE-STATISTICS  THRU 4000-EXIT
           PERFORM 9000-CLEANUP            THRU 9000-EXIT

           DISPLAY 'CCPBS010: PROCESSING COMPLETE'
           GOBACK.

      *================================================================
      * INITIALIZATION PROCESSING
      *================================================================
       1000-INITIALIZATION.
           DISPLAY 'CCPBS010: INITIALIZATION STARTED'

           MOVE ZEROS TO WS-LOOP-COUNTER
                         WS-READ-COUNTER
                         WS-PROCESS-COUNTER
                         WS-ERROR-COUNTER

           PERFORM 1100-OPEN-FILES         THRU 1100-EXIT
           PERFORM 1200-SETUP-DB2-CURSOR   THRU 1200-EXIT
           PERFORM 1300-VALIDATE-LINKAGE   THRU 1300-EXIT.
       1000-EXIT.
           EXIT.

      *================================================================
      * FILE OPERATIONS
      *================================================================
       1100-OPEN-FILES.
           OPEN I-O CUSTOMER-FILE

           IF NOT WS-CUST-FILE-OK
               DISPLAY 'ERROR OPENING CUSTOMER FILE: ' WS-CUST-FILE-STATUS
               MOVE 'E010' TO LS-ERROR-CODE
               MOVE 'FILE OPEN ERROR' TO LS-ERROR-MESSAGE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           END-IF.
       1100-EXIT.
           EXIT.

      *================================================================
      * DB2 CURSOR SETUP
      *================================================================
       1200-SETUP-DB2-CURSOR.
           MOVE LS-CUSTOMER-ID TO :CLPRC-CLIENT-NBR
           MOVE 'CARD' TO :CLPRC-SYSTEM-TYPE

           EXEC SQL
               OPEN C1
           END-EXEC

           EVALUATE SQLCODE
               WHEN 0
                   DISPLAY 'DB2 CURSOR OPENED SUCCESSFULLY'
               WHEN -911
                   DISPLAY 'DB2 DEADLOCK DETECTED, RETRYING...'
                   PERFORM 1210-RETRY-DB2-OPEN THRU 1210-EXIT
               WHEN OTHER
                   DISPLAY 'DB2 CURSOR OPEN FAILED: ' SQLCODE
                   MOVE 'E011' TO LS-ERROR-CODE
                   MOVE 'DB2 CURSOR ERROR' TO LS-ERROR-MESSAGE
                   MOVE 8 TO WS-RETURN-CODE
                   PERFORM 9999-ABEND-ROUTINE
           END-EVALUATE.
       1200-EXIT.
           EXIT.

      *================================================================
      * DB2 RETRY LOGIC FOR DEADLOCKS
      *================================================================
       1210-RETRY-DB2-OPEN.
           EXEC SQL
               ROLLBACK
           END-EXEC

      *    WAIT 1 SECOND BEFORE RETRY
           CALL 'ILBOWAT0' USING BY VALUE 1000000

           EXEC SQL
               OPEN C1
           END-EXEC

           IF SQLCODE NOT = 0
               DISPLAY 'DB2 RETRY FAILED: ' SQLCODE
               MOVE 'E011' TO LS-ERROR-CODE
               MOVE 'DB2 RETRY FAILED' TO LS-ERROR-MESSAGE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           ELSE
               DISPLAY 'DB2 RETRY SUCCESSFUL'
           END-IF.
       1210-EXIT.
           EXIT.

      *================================================================
      * LINKAGE VALIDATION
      *================================================================
       1300-VALIDATE-LINKAGE.
           IF LS-CUSTOMER-ID = SPACES OR LS-CUSTOMER-ID = ZEROS
               MOVE 'E001' TO LS-ERROR-CODE
               MOVE 'INVALID CUSTOMER ID' TO LS-ERROR-MESSAGE
               MOVE 'N' TO LS-VALIDATION-FLAG
               MOVE 8 TO LS-RETURN-CODE
               SET WS-STOP-PROCESSING TO TRUE
           ELSE
               MOVE 'Y' TO LS-VALIDATION-FLAG
               MOVE SPACES TO LS-ERROR-CODE
               MOVE SPACES TO LS-ERROR-MESSAGE
               MOVE 0 TO LS-RETURN-CODE
               SET WS-CONTINUE-PROCESSING TO TRUE
           END-IF.
       1300-EXIT.
           EXIT.

      *================================================================
      * CUSTOMER VALIDATION WITH IMS ACCESS
      *================================================================
       2000-VALIDATE-CUSTOMER.
           IF WS-CONTINUE-PROCESSING
               PERFORM 2100-READ-CUSTOMER-FILE  THRU 2100-EXIT
               PERFORM 2200-ACCESS-IMS-DATABASE THRU 2200-EXIT
               PERFORM 2300-VALIDATE-CUSTOMER-DATA THRU 2300-EXIT
           END-IF.
       2000-EXIT.
           EXIT.

      *================================================================
      * CUSTOMER FILE ACCESS
      *================================================================
       2100-READ-CUSTOMER-FILE.
           MOVE LS-CUSTOMER-ID TO CUST-KEY

           READ CUSTOMER-FILE

           EVALUATE WS-CUST-FILE-STATUS
               WHEN '00'
                   ADD 1 TO WS-READ-COUNTER
                   DISPLAY 'CUSTOMER RECORD FOUND: ' CUST-KEY
               WHEN '23'
                   DISPLAY 'CUSTOMER NOT FOUND IN FILE: ' CUST-KEY
                   MOVE 'W001' TO LS-ERROR-CODE
                   MOVE 'CUSTOMER NOT IN FILE' TO LS-ERROR-MESSAGE
               WHEN OTHER
                   DISPLAY 'CUSTOMER FILE READ ERROR: ' WS-CUST-FILE-STATUS
                   MOVE 'E012' TO LS-ERROR-CODE
                   MOVE 'FILE READ ERROR' TO LS-ERROR-MESSAGE
                   ADD 1 TO WS-ERROR-COUNTER
           END-EVALUATE.
       2100-EXIT.
           EXIT.

      *================================================================
      * IMS DATABASE ACCESS WITH COMPLEX NAVIGATION
      *================================================================
       2200-ACCESS-IMS-DATABASE.
           PERFORM 2210-READ-ACCOUNT-MASTER THRU 2210-EXIT

           IF IMS-STATUS-OK
               PERFORM 2220-READ-CUSTOMER-SEGMENTS THRU 2220-EXIT
                   VARYING WS-LOOP-COUNTER FROM 1 BY 1
                   UNTIL WS-LOOP-COUNTER > WS-MAX-LOOPS
                      OR NOT IMS-STATUS-OK
           END-IF.
       2200-EXIT.
           EXIT.

      *================================================================
      * IMS ACCOUNT MASTER READ
      *================================================================
       2210-READ-ACCOUNT-MASTER.
           MOVE LS-ACCOUNT-NUMBER TO AM00-ACCOUNT-KEY

      *    BUILD SSA FOR ACCOUNT MASTER SEGMENT
           STRING 'AM00    (ACCTKEY =' DELIMITED BY SIZE
                  LS-ACCOUNT-NUMBER DELIMITED BY SIZE
                  ')' DELIMITED BY SIZE
                  INTO WS-SSA-ACCOUNT-MASTER
           END-STRING

           CALL 'CBLTDLI' USING FC-GU
                                DBDAM01-PCB-MASK
                                AMCAM00-SEGMENT
                                WS-SSA-ACCOUNT-MASTER

           MOVE AMCAM00-SEGMENT(1:2) TO WS-IMS-STATUS

           EVALUATE TRUE
               WHEN IMS-STATUS-OK
      *            SAFELY MOVE SEGMENT DATA WITH SIZE CHECK
                   MOVE AMCAM00-SEGMENT(3:100) TO AM00-ACCOUNT-MASTER
                   ADD 1 TO WS-READ-COUNTER
                   DISPLAY 'IMS ACCOUNT MASTER READ: ' AM00-ACCOUNT-KEY
               WHEN IMS-STATUS-NOT-FOUND
                   DISPLAY 'ACCOUNT NOT FOUND IN IMS: ' LS-ACCOUNT-NUMBER
                   MOVE 'W002' TO LS-ERROR-CODE
                   MOVE 'ACCOUNT NOT IN IMS' TO LS-ERROR-MESSAGE
               WHEN OTHER
                   DISPLAY 'IMS READ ERROR: ' WS-IMS-STATUS
                   MOVE 'E013' TO LS-ERROR-CODE
                   MOVE 'IMS ACCESS ERROR' TO LS-ERROR-MESSAGE
                   ADD 1 TO WS-ERROR-COUNTER
           END-EVALUATE.
       2210-EXIT.
           EXIT.

      *================================================================
      * IMS CUSTOMER SEGMENTS READ
      *================================================================
       2220-READ-CUSTOMER-SEGMENTS.
           MOVE AM00-CUSTOMER-ID TO AM01-ACCT-CUST-KEY(1:9)
           MOVE AM00-ACCOUNT-KEY TO AM01-ACCT-CUST-KEY(10:20)

      *    BUILD SSA FOR CUSTOMER SEGMENT
           STRING 'AM01    (CUSTKEY =' DELIMITED BY SIZE
                  AM00-CUSTOMER-ID DELIMITED BY SIZE
                  ')' DELIMITED BY SIZE
                  INTO WS-SSA-CUSTOMER-SEGMENT
           END-STRING

           CALL 'CBLTDLI' USING FC-GN
                                DBDAM01-PCB-MASK
                                AMCAM01-SEGMENT
                                WS-SSA-CUSTOMER-SEGMENT

           MOVE AMCAM01-SEGMENT(1:2) TO WS-IMS-STATUS

           EVALUATE TRUE
               WHEN IMS-STATUS-OK
                   ADD 1 TO WS-READ-COUNTER
                   DISPLAY 'CUSTOMER SEGMENT READ: ' AM01-ACCT-CUST-KEY
               WHEN IMS-STATUS-NOT-FOUND
                   DISPLAY 'NO MORE CUSTOMER SEGMENTS'
               WHEN IMS-STATUS-END-OF-DB
                   DISPLAY 'END OF DATABASE REACHED'
               WHEN OTHER
                   DISPLAY 'IMS SEGMENT READ ERROR: ' WS-IMS-STATUS
                   ADD 1 TO WS-ERROR-COUNTER
           END-EVALUATE.
       2220-EXIT.
           EXIT.

      *================================================================
      * CUSTOMER DATA VALIDATION
      *================================================================
       2300-VALIDATE-CUSTOMER-DATA.
           IF IMS-STATUS-OK AND WS-CUST-FILE-OK
               PERFORM 2310-CROSS-REFERENCE-CHECK THRU 2310-EXIT
               PERFORM 2320-BUSINESS-RULE-CHECK   THRU 2320-EXIT
           END-IF.
       2300-EXIT.
           EXIT.

       2310-CROSS-REFERENCE-CHECK.
           IF CUST-KEY NOT = LS-CUSTOMER-ID
               DISPLAY 'CUSTOMER ID MISMATCH: FILE=' CUST-KEY
                       ' LINKAGE=' LS-CUSTOMER-ID
               MOVE 'E014' TO LS-ERROR-CODE
               MOVE 'CUSTOMER ID MISMATCH' TO LS-ERROR-MESSAGE
               ADD 1 TO WS-ERROR-COUNTER
           END-IF

           IF CUST-ACCOUNT-NUMBER NOT = LS-ACCOUNT-NUMBER
               DISPLAY 'ACCOUNT NUMBER MISMATCH: FILE=' CUST-ACCOUNT-NUMBER
                       ' LINKAGE=' LS-ACCOUNT-NUMBER
               MOVE 'E014' TO LS-ERROR-CODE
               MOVE 'ACCOUNT NUMBER MISMATCH' TO LS-ERROR-MESSAGE
               ADD 1 TO WS-ERROR-COUNTER
           END-IF.
       2310-EXIT.
           EXIT.

       2320-BUSINESS-RULE-CHECK.
           IF AM00-ACCOUNT-STATUS = 'C'
               DISPLAY 'ACCOUNT IS CLOSED: ' AM00-ACCOUNT-KEY
               MOVE 'W003' TO LS-ERROR-CODE
               MOVE 'ACCOUNT CLOSED' TO LS-ERROR-MESSAGE
           END-IF

           IF AM00-BALANCE < 0
               DISPLAY 'NEGATIVE BALANCE: ' AM00-BALANCE
               MOVE 'W004' TO LS-ERROR-CODE
               MOVE 'NEGATIVE BALANCE' TO LS-ERROR-MESSAGE
           END-IF.
       2320-EXIT.
           EXIT.

      *================================================================
      * ACCOUNT PROCESSING WITH CROSS-PROGRAM CALLS
      *================================================================
       3000-PROCESS-ACCOUNTS.
           IF WS-CONTINUE-PROCESSING
               PERFORM 3100-CALL-ACCOUNT-VALIDATION THRU 3100-EXIT
               PERFORM 3200-UPDATE-CUSTOMER-RECORD  THRU 3200-EXIT
               PERFORM 3300-CHECKPOINT-PROCESSING   THRU 3300-EXIT
           END-IF.
       3000-EXIT.
           EXIT.

      *================================================================
      * CROSS-PROGRAM CALL TO CCPBS016
      *================================================================
       3100-CALL-ACCOUNT-VALIDATION.
      *    POPULATE ADDITIONAL FIELDS FROM FILE DATA
           MOVE CUST-NAME TO LS-CUSTOMER-NAME
           MOVE CUST-ACCOUNT-TYPE TO LS-ACCOUNT-TYPE
           MOVE CUST-STATUS TO LS-ACCOUNT-STATUS
           MOVE 'Y' TO LS-PROCESS-FLAG
           MOVE WS-PROGRAM-NAME TO LS-PROGRAM-ID

           CALL WS-CCPBS016-PROG USING LS-CUSTOMER-DATA
               ON EXCEPTION
                   DISPLAY 'CALL TO CCPBS016 FAILED'
                   MOVE 'E015' TO LS-ERROR-CODE
                   MOVE 'PROGRAM CALL FAILED' TO LS-ERROR-MESSAGE
                   MOVE 'N' TO WS-CALL-STATUS
                   ADD 1 TO WS-ERROR-COUNTER
               NOT ON EXCEPTION
                   DISPLAY 'CALL TO CCPBS016 SUCCESSFUL'
                   MOVE 'Y' TO WS-CALL-STATUS
                   ADD 1 TO WS-PROCESS-COUNTER
           END-CALL.
       3100-EXIT.
           EXIT.

      *================================================================
      * CUSTOMER RECORD UPDATE WITH IMS
      *================================================================
       3200-UPDATE-CUSTOMER-RECORD.
           IF WS-CALL-SUCCESS AND IMS-STATUS-OK
               PERFORM 3210-PREPARE-UPDATE-DATA THRU 3210-EXIT
               PERFORM 3220-IMS-REPLACE-CALL    THRU 3220-EXIT
               PERFORM 3230-FILE-UPDATE         THRU 3230-EXIT
           END-IF.
       3200-EXIT.
           EXIT.

       3210-PREPARE-UPDATE-DATA.
           MOVE 'A' TO AM00-ACCOUNT-STATUS
           MOVE FUNCTION CURRENT-DATE(1:8) TO AM00-OPEN-DATE
           MOVE AM00-ACCOUNT-MASTER TO AMCAM00-SEGMENT.
       3210-EXIT.
           EXIT.

       3220-IMS-REPLACE-CALL.
           CALL 'CBLTDLI' USING FC-REPL
                                DBDAM01-PCB-MASK
                                AMCAM00-SEGMENT

           MOVE AMCAM00-SEGMENT(1:2) TO WS-IMS-STATUS

           IF IMS-STATUS-OK
               DISPLAY 'IMS RECORD UPDATED: ' AM00-ACCOUNT-KEY
               ADD 1 TO WS-PROCESS-COUNTER
           ELSE
               DISPLAY 'IMS UPDATE FAILED: ' WS-IMS-STATUS
               MOVE 'E016' TO LS-ERROR-CODE
               MOVE 'IMS UPDATE ERROR' TO LS-ERROR-MESSAGE
               ADD 1 TO WS-ERROR-COUNTER
           END-IF.
       3220-EXIT.
           EXIT.

       3230-FILE-UPDATE.
           MOVE 'A' TO CUST-STATUS

           REWRITE CUSTOMER-RECORD

           IF WS-CUST-FILE-OK
               DISPLAY 'CUSTOMER FILE UPDATED: ' CUST-KEY
           ELSE
               DISPLAY 'FILE UPDATE ERROR: ' WS-CUST-FILE-STATUS
               MOVE 'E017' TO LS-ERROR-CODE
               MOVE 'FILE UPDATE ERROR' TO LS-ERROR-MESSAGE
               ADD 1 TO WS-ERROR-COUNTER
           END-IF.
       3230-EXIT.
           EXIT.

      *================================================================
      * CHECKPOINT PROCESSING
      *================================================================
       3300-CHECKPOINT-PROCESSING.
           ADD 1 TO WS-CHKP-COUNTER

           IF WS-CHKP-COUNTER >= WS-CHKP-FREQUENCY
              OR FORCE-CHECKPOINT
               PERFORM 3310-TAKE-CHECKPOINT THRU 3310-EXIT
               MOVE ZEROS TO WS-CHKP-COUNTER
           END-IF.
       3300-EXIT.
           EXIT.

       3310-TAKE-CHECKPOINT.
           DISPLAY 'TAKING CHECKPOINT AT RECORD: ' WS-PROCESS-COUNTER

      *    CALL 'CHKP' USING WS-CHECKPOINT-DATA
      *        ON EXCEPTION
      *            DISPLAY 'CHECKPOINT FAILED'
      *            MOVE 'E018' TO LS-ERROR-CODE
      *        NOT ON EXCEPTION
      *            DISPLAY 'CHECKPOINT SUCCESSFUL'
      *    END-CALL

           DISPLAY 'CHECKPOINT COMPLETED'.
       3310-EXIT.
           EXIT.

      *================================================================
      * STATISTICS UPDATE WITH DB2
      *================================================================
       4000-UPDATE-STATISTICS.
           PERFORM 4100-FETCH-DB2-DATA     THRU 4100-EXIT
           PERFORM 4200-UPDATE-DB2-STATS   THRU 4200-EXIT.
       4000-EXIT.
           EXIT.

       4100-FETCH-DB2-DATA.
           EXEC SQL
               FETCH C1 INTO :CLPRC-CLIENT-NBR,
                            :CLPRC-SYSTEM-TYPE,
                            :CLPRC-CURR-DATE
           END-EXEC

           EVALUATE SQLCODE
               WHEN 0
                   DISPLAY 'DB2 FETCH SUCCESSFUL'
                   DISPLAY 'CLIENT: ' :CLPRC-CLIENT-NBR
                   DISPLAY 'SYSTEM: ' :CLPRC-SYSTEM-TYPE
               WHEN 100
                   DISPLAY 'NO MORE DB2 ROWS'
               WHEN OTHER
                   DISPLAY 'DB2 FETCH ERROR: ' SQLCODE
                   MOVE 'E019' TO LS-ERROR-CODE
                   MOVE 'DB2 FETCH ERROR' TO LS-ERROR-MESSAGE
           END-EVALUATE.
       4100-EXIT.
           EXIT.

       4200-UPDATE-DB2-STATS.
           IF SQLCODE = 0
               EXEC SQL
                   UPDATE TCH_PROCESSING_STATS
                   SET PROCESS_COUNT = PROCESS_COUNT + :WS-PROCESS-COUNTER,
                       ERROR_COUNT = ERROR_COUNT + :WS-ERROR-COUNTER,
                       LAST_UPDATE_DATE = CURRENT DATE
                   WHERE CLIENT_NBR = :CLPRC-CLIENT-NBR
                     AND SYSTEM_TYPE = :CLPRC-SYSTEM-TYPE
               END-EXEC

               IF SQLCODE = 0
                   DISPLAY 'STATISTICS UPDATED SUCCESSFULLY'
                   EXEC SQL COMMIT END-EXEC
               ELSE
                   DISPLAY 'STATISTICS UPDATE FAILED: ' SQLCODE
                   EXEC SQL ROLLBACK END-EXEC
                   MOVE 'E020' TO LS-ERROR-CODE
                   MOVE 'DB2 UPDATE ERROR' TO LS-ERROR-MESSAGE
               END-IF
           END-IF.
       4200-EXIT.
           EXIT.

      *================================================================
      * CLEANUP PROCESSING
      *================================================================
       9000-CLEANUP.
           PERFORM 9100-CLOSE-DB2-CURSOR   THRU 9100-EXIT
           PERFORM 9200-CLOSE-FILES        THRU 9200-EXIT
           PERFORM 9300-DISPLAY-TOTALS     THRU 9300-EXIT.
       9000-EXIT.
           EXIT.

       9100-CLOSE-DB2-CURSOR.
           EXEC SQL
               CLOSE C1
           END-EXEC

           IF SQLCODE = 0
               DISPLAY 'DB2 CURSOR CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'DB2 CURSOR CLOSE ERROR: ' SQLCODE
           END-IF.
       9100-EXIT.
           EXIT.

       9200-CLOSE-FILES.
           CLOSE CUSTOMER-FILE

           IF WS-CUST-FILE-OK
               DISPLAY 'CUSTOMER FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'CUSTOMER FILE CLOSE ERROR: ' WS-CUST-FILE-STATUS
           END-IF.
       9200-EXIT.
           EXIT.

       9300-DISPLAY-TOTALS.
           DISPLAY '================================================'
           DISPLAY 'CCPBS010 PROCESSING TOTALS:'
           DISPLAY 'RECORDS READ     : ' WS-READ-COUNTER
           DISPLAY 'RECORDS PROCESSED: ' WS-PROCESS-COUNTER
           DISPLAY 'ERRORS ENCOUNTERED: ' WS-ERROR-COUNTER
           DISPLAY 'CHECKPOINTS TAKEN: ' WS-CHKP-COUNTER
           DISPLAY '================================================'.
       9300-EXIT.
           EXIT.

      *================================================================
      * ERROR HANDLING AND ABEND ROUTINE
      *================================================================
       9999-ABEND-ROUTINE.
           DISPLAY '************************************************'
           DISPLAY 'CCPBS010 ABNORMAL TERMINATION'
           DISPLAY 'ERROR CODE: ' LS-ERROR-CODE
           DISPLAY 'ERROR MESSAGE: ' LS-ERROR-MESSAGE
           DISPLAY 'RETURN CODE: ' WS-RETURN-CODE
           DISPLAY '************************************************'

           PERFORM 9200-CLOSE-FILES THRU 9200-EXIT
           PERFORM 9100-CLOSE-DB2-CURSOR THRU 9100-EXIT

           MOVE WS-RETURN-CODE TO RETURN-CODE
           GOBACK.
       9999-EXIT.
           EXIT.
