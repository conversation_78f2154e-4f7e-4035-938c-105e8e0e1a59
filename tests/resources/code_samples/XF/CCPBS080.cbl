      *================================================================
      * PROGRAM CCPBS080.CBL - BUSINESS SERVICE MODULE 080
      * Called by: CPBD888 and related programs
      * Purpose: PIN processing and validation
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CCPBS080.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CCPBS080'.
       
       LINKAGE SECTION.
       01  LS-PIN-DATA.
           05  LS-CARD-NUMBER          PIC X(16).
           05  LS-PIN-BLOCK            PIC X(16).
           05  LS-PIN-VERIFICATION     PIC X(01).
           05  LS-PIN-ATTEMPTS         PIC 9(02).
           05  LS-ERROR-CODE           PIC X(04).
       
       PROCEDURE DIVISION USING LS-PIN-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'CCPBS080: PIN PROCESSING STARTED'
           
           IF LS-CARD-NUMBER = SPACES
               MOVE 'E005' TO LS-ERROR-CODE
               MOVE 'N' TO LS-PIN-VERIFICATION
               MOVE 8 TO RETURN-CODE
           ELSE
               MOVE 'Y' TO LS-PIN-VERIFICATION
               MOVE 1 TO LS-PIN-ATTEMPTS
               MOVE SPACES TO LS-ERROR-CODE
               MOVE 0 TO RETURN-CODE
           END-IF
           
           DISPLAY 'CCPBS080: PROCESSING COMPLETE'
           GOBACK.
