      *================================================================
      * PROGRAM CEVBS030.CBL - CARD EVENT BUSINESS SERVICE 030
      * Called by: CPBD888 and related programs
      * Purpose: Card event processing and logging
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CEVBS030.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CEVBS030'.
       
       LINKAGE SECTION.
       01  LS-EVENT-DATA.
           05  LS-EVENT-TYPE           PIC X(04).
           05  LS-CARD-NUMBER          PIC X(16).
           05  LS-EVENT-DATE           PIC X(08).
           05  LS-EVENT-TIME           PIC X(06).
           05  LS-EVENT-STATUS         PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).
       
       PROCEDURE DIVISION USING LS-EVENT-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'CEVBS030: EVENT PROCESSING STARTED'
           
           IF LS-CARD-NUMBER = SPACES
               MOVE 'E007' TO LS-ERROR-CODE
               MOVE 'F' TO LS-EVENT-STATUS
               MOVE 8 TO RETURN-CODE
           ELSE
               MOVE 'S' TO LS-EVENT-STATUS
               MOVE SPACES TO LS-ERROR-CODE
               MOVE 0 TO RETURN-CODE
           END-IF
           
           DISPLAY 'CEVBS030: EVENT LOGGED'
           GOBACK.
