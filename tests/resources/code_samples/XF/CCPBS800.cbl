      *================================================================
      * PROGRAM CCPBS800.CBL - BUSINESS SERVICE MODULE 800
      * Called by: CPBD888 and related programs
      * Purpose: Reporting and statistics services with DB2 access
      * Dependencies: Terminal program in chain
      * Last Modified: 01/15/2024
      * Enhanced: Complex paragraph dependencies, DB2 SQL, file I/O
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CCPBS800.

       ENVIRONMENT DIVISION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT REPORT-FILE          ASSIGN TO RPTFILE
                  ACCESS IS SEQUENTIAL
                  ORGANIZATION IS SEQUENTIAL
                  FILE STATUS IS WS-REPORT-FILE-STATUS.

           SELECT STATISTICS-FILE      ASSIGN TO STATFILE
                  ACCESS IS SEQUENTIAL
                  ORGANIZATION IS SEQUENTIAL
                  FILE STATUS IS WS-STAT-FILE-STATUS.

       DATA DIVISION.
       FILE SECTION.
       FD  REPORT-FILE
           RECORD CONTAINS 132 CHARACTERS
           DATA RECORD IS REPORT-RECORD.
       01  REPORT-RECORD               PIC X(132).

       FD  STATISTICS-FILE
           RECORD CONTAINS 80 CHARACTERS
           DATA RECORD IS STATISTICS-RECORD.
       01  STATISTICS-RECORD.
           05  STAT-DATE               PIC X(08).
           05  STAT-PROGRAM-NAME       PIC X(08).
           05  STAT-RECORD-COUNT       PIC 9(09).
           05  STAT-ERROR-COUNT        PIC 9(06).
           05  STAT-PROCESS-TIME       PIC 9(06).
           05  STAT-STATUS             PIC X(01).
           05  STAT-FILLER             PIC X(40).

       WORKING-STORAGE SECTION.
      *================================================================
      * PROGRAM CONTROL VARIABLES
      *================================================================
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CCPBS800'.
       01  WS-RETURN-CODE              PIC 9(04) VALUE 0.
       01  WS-REPORT-FILE-STATUS       PIC X(02) VALUE '00'.
           88  WS-REPORT-FILE-OK       VALUE '00'.
       01  WS-STAT-FILE-STATUS         PIC X(02) VALUE '00'.
           88  WS-STAT-FILE-OK         VALUE '00'.

      *================================================================
      * REPORT CONTROL VARIABLES
      *================================================================
       01  WS-REPORT-CONTROLS.
           05  WS-PAGE-NUMBER          PIC 9(04) VALUE 1.
           05  WS-LINE-COUNT           PIC 9(03) VALUE 0.
           05  WS-MAX-LINES            PIC 9(03) VALUE 60.
           05  WS-REPORT-DATE          PIC X(08).
           05  WS-REPORT-TIME          PIC X(06).

      *================================================================
      * PROCESSING COUNTERS
      *================================================================
       01  WS-COUNTERS.
           05  WS-TOTAL-RECORDS        PIC 9(09) VALUE 0.
           05  WS-TOTAL-ERRORS         PIC 9(06) VALUE 0.
           05  WS-REPORTS-GENERATED    PIC 9(04) VALUE 0.
           05  WS-DB2-ROWS-PROCESSED   PIC 9(06) VALUE 0.

      *================================================================
      * DB2 SQL VARIABLES
      *================================================================
           EXEC SQL INCLUDE SQLCA END-EXEC.

       01  WS-SQL-REPORT-VARS.
           05  :RPT-DATE               PIC X(10).
           05  :RPT-PROGRAM-ID         PIC X(08).
           05  :RPT-RECORD-COUNT       PIC S9(09) COMP-3.
           05  :RPT-ERROR-COUNT        PIC S9(06) COMP-3.
           05  :RPT-STATUS             PIC X(01).

      *================================================================
      * NULL INDICATORS FOR DB2 HOST VARIABLES
      *================================================================
       01  WS-NULL-INDICATORS.
           05  :RPT-DATE-NULL          PIC S9(04) COMP.
           05  :RPT-PROGRAM-ID-NULL    PIC S9(04) COMP.
           05  :RPT-RECORD-COUNT-NULL  PIC S9(04) COMP.
           05  :RPT-ERROR-COUNT-NULL   PIC S9(04) COMP.
           05  :RPT-STATUS-NULL        PIC S9(04) COMP.

           EXEC SQL DECLARE RPT_CURSOR CURSOR FOR
               SELECT PROCESS_DATE, PROGRAM_ID, RECORD_COUNT,
                      ERROR_COUNT, PROCESS_STATUS
               FROM TCH_PROCESSING_STATS
               WHERE PROCESS_DATE >= CURRENT DATE - 7 DAYS
               ORDER BY PROCESS_DATE DESC, PROGRAM_ID
           END-EXEC.

      *================================================================
      * REPORT LAYOUT TEMPLATES
      *================================================================
       01  WS-REPORT-HEADERS.
           05  WS-HEADER-1.
               10  FILLER              PIC X(50) VALUE SPACES.
               10  FILLER              PIC X(32)
                   VALUE 'CARD PROCESSING STATISTICS REPORT'.
               10  FILLER              PIC X(50) VALUE SPACES.
           05  WS-HEADER-2.
               10  FILLER              PIC X(10) VALUE 'DATE: '.
               10  WS-HDR-DATE         PIC X(08).
               10  FILLER              PIC X(20) VALUE SPACES.
               10  FILLER              PIC X(10) VALUE 'PAGE: '.
               10  WS-HDR-PAGE         PIC ZZZ9.
               10  FILLER              PIC X(80) VALUE SPACES.
           05  WS-HEADER-3.
               10  FILLER              PIC X(132) VALUE ALL '-'.
           05  WS-COLUMN-HEADERS.
               10  FILLER              PIC X(10) VALUE 'DATE'.
               10  FILLER              PIC X(12) VALUE 'PROGRAM'.
               10  FILLER              PIC X(15) VALUE 'RECORDS'.
               10  FILLER              PIC X(10) VALUE 'ERRORS'.
               10  FILLER              PIC X(10) VALUE 'STATUS'.
               10  FILLER              PIC X(75) VALUE SPACES.

       01  WS-DETAIL-LINE.
           05  WS-DTL-DATE             PIC X(10).
           05  FILLER                  PIC X(02) VALUE SPACES.
           05  WS-DTL-PROGRAM          PIC X(08).
           05  FILLER                  PIC X(04) VALUE SPACES.
           05  WS-DTL-RECORDS          PIC ZZZ,ZZZ,ZZ9.
           05  FILLER                  PIC X(04) VALUE SPACES.
           05  WS-DTL-ERRORS           PIC ZZZ,ZZ9.
           05  FILLER                  PIC X(06) VALUE SPACES.
           05  WS-DTL-STATUS           PIC X(01).
           05  FILLER                  PIC X(85) VALUE SPACES.

       LINKAGE SECTION.
       01  LS-REPORT-DATA.
           05  LS-REPORT-TYPE          PIC X(04).
           05  LS-RECORD-COUNT         PIC 9(09).
           05  LS-ERROR-COUNT          PIC 9(06).
           05  LS-REPORT-FLAG          PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).

       PROCEDURE DIVISION USING LS-REPORT-DATA.

      *================================================================
      * MAIN PROCESSING CONTROL
      *================================================================
       0000-MAIN-PROCESSING.
           DISPLAY 'CCPBS800: REPORTING STARTED'

           PERFORM 1000-INITIALIZATION     THRU 1000-EXIT
           PERFORM 2000-PROCESS-REPORTS    THRU 2000-EXIT
           PERFORM 3000-GENERATE-STATISTICS THRU 3000-EXIT
           PERFORM 9000-CLEANUP            THRU 9000-EXIT

           DISPLAY 'CCPBS800: PROCESSING COMPLETE'
           GOBACK.

      *================================================================
      * INITIALIZATION PROCESSING
      *================================================================
       1000-INITIALIZATION.
           DISPLAY 'CCPBS800: INITIALIZATION STARTED'

           MOVE FUNCTION CURRENT-DATE(1:8) TO WS-REPORT-DATE
           MOVE FUNCTION CURRENT-DATE(9:6) TO WS-REPORT-TIME

           MOVE ZEROS TO WS-TOTAL-RECORDS
                         WS-TOTAL-ERRORS
                         WS-REPORTS-GENERATED
                         WS-DB2-ROWS-PROCESSED

           PERFORM 1100-OPEN-FILES         THRU 1100-EXIT
           PERFORM 1200-SETUP-DB2-CURSOR   THRU 1200-EXIT
           PERFORM 1300-VALIDATE-INPUT     THRU 1300-EXIT.
       1000-EXIT.
           EXIT.

      *================================================================
      * FILE OPERATIONS
      *================================================================
       1100-OPEN-FILES.
           OPEN OUTPUT REPORT-FILE

           IF NOT WS-REPORT-FILE-OK
               DISPLAY 'ERROR OPENING REPORT FILE: ' WS-REPORT-FILE-STATUS
               MOVE 'E041' TO LS-ERROR-CODE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           END-IF

           OPEN OUTPUT STATISTICS-FILE

           IF NOT WS-STAT-FILE-OK
               DISPLAY 'ERROR OPENING STATISTICS FILE: ' WS-STAT-FILE-STATUS
               MOVE 'E042' TO LS-ERROR-CODE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           END-IF.
       1100-EXIT.
           EXIT.

      *================================================================
      * DB2 CURSOR SETUP
      *================================================================
       1200-SETUP-DB2-CURSOR.
           EXEC SQL
               OPEN RPT_CURSOR
           END-EXEC

           EVALUATE SQLCODE
               WHEN 0
                   DISPLAY 'DB2 REPORT CURSOR OPENED'
               WHEN OTHER
                   DISPLAY 'DB2 CURSOR OPEN FAILED: ' SQLCODE
                   MOVE 'E043' TO LS-ERROR-CODE
                   MOVE 8 TO WS-RETURN-CODE
           END-EVALUATE.
       1200-EXIT.
           EXIT.

      *================================================================
      * INPUT VALIDATION
      *================================================================
       1300-VALIDATE-INPUT.
           EVALUATE LS-REPORT-TYPE
               WHEN 'STAT'
                   DISPLAY 'STATISTICS REPORT REQUESTED'
                   MOVE 'Y' TO LS-REPORT-FLAG
                   MOVE 0 TO WS-RETURN-CODE
               WHEN 'SUMM'
                   DISPLAY 'SUMMARY REPORT REQUESTED'
                   MOVE 'Y' TO LS-REPORT-FLAG
                   MOVE 0 TO WS-RETURN-CODE
               WHEN 'DETL'
                   DISPLAY 'DETAIL REPORT REQUESTED'
                   MOVE 'Y' TO LS-REPORT-FLAG
                   MOVE 0 TO WS-RETURN-CODE
               WHEN OTHER
                   MOVE 'E006' TO LS-ERROR-CODE
                   MOVE 'N' TO LS-REPORT-FLAG
                   MOVE 8 TO WS-RETURN-CODE
           END-EVALUATE.
       1300-EXIT.
           EXIT.

      *================================================================
      * REPORT PROCESSING CONTROL
      *================================================================
       2000-PROCESS-REPORTS.
           IF LS-REPORT-FLAG = 'Y'
               PERFORM 2100-PRINT-REPORT-HEADERS THRU 2100-EXIT

               EVALUATE LS-REPORT-TYPE
                   WHEN 'STAT'
                       PERFORM 2200-GENERATE-STATISTICS-REPORT
                           THRU 2200-EXIT
                   WHEN 'SUMM'
                       PERFORM 2300-GENERATE-SUMMARY-REPORT
                           THRU 2300-EXIT
                   WHEN 'DETL'
                       PERFORM 2400-GENERATE-DETAIL-REPORT
                           THRU 2400-EXIT
               END-EVALUATE

               PERFORM 2500-PRINT-REPORT-TOTALS THRU 2500-EXIT
           END-IF.
       2000-EXIT.
           EXIT.

      *================================================================
      * REPORT HEADER PRINTING
      *================================================================
       2100-PRINT-REPORT-HEADERS.
           MOVE WS-REPORT-DATE TO WS-HDR-DATE
           MOVE WS-PAGE-NUMBER TO WS-HDR-PAGE

           WRITE REPORT-RECORD FROM WS-HEADER-1
           WRITE REPORT-RECORD FROM WS-HEADER-2
           WRITE REPORT-RECORD FROM WS-HEADER-3
           WRITE REPORT-RECORD FROM WS-COLUMN-HEADERS
           WRITE REPORT-RECORD FROM WS-HEADER-3

           MOVE 5 TO WS-LINE-COUNT.
       2100-EXIT.
           EXIT.

      *================================================================
      * STATISTICS REPORT GENERATION
      *================================================================
       2200-GENERATE-STATISTICS-REPORT.
           PERFORM 2210-FETCH-DB2-STATISTICS THRU 2210-EXIT
               UNTIL SQLCODE NOT = 0

           ADD 1 TO WS-REPORTS-GENERATED.
       2200-EXIT.
           EXIT.

       2210-FETCH-DB2-STATISTICS.
           EXEC SQL
               FETCH RPT_CURSOR INTO :RPT-DATE :RPT-DATE-NULL,
                                    :RPT-PROGRAM-ID :RPT-PROGRAM-ID-NULL,
                                    :RPT-RECORD-COUNT :RPT-RECORD-COUNT-NULL,
                                    :RPT-ERROR-COUNT :RPT-ERROR-COUNT-NULL,
                                    :RPT-STATUS :RPT-STATUS-NULL
           END-EXEC

           EVALUATE SQLCODE
               WHEN 0
                   ADD 1 TO WS-DB2-ROWS-PROCESSED
                   PERFORM 2211-CHECK-NULL-VALUES  THRU 2211-EXIT
                   PERFORM 2212-FORMAT-STATISTICS-LINE THRU 2212-EXIT
                   PERFORM 2213-WRITE-REPORT-LINE      THRU 2213-EXIT
                   IF :RPT-RECORD-COUNT-NULL = 0
                       ADD :RPT-RECORD-COUNT TO WS-TOTAL-RECORDS
                   END-IF
                   IF :RPT-ERROR-COUNT-NULL = 0
                       ADD :RPT-ERROR-COUNT TO WS-TOTAL-ERRORS
                   END-IF
               WHEN 100
                   DISPLAY 'END OF STATISTICS DATA'
               WHEN OTHER
                   DISPLAY 'DB2 FETCH ERROR: ' SQLCODE
                   MOVE 'E044' TO LS-ERROR-CODE
           END-EVALUATE.
       2210-EXIT.
           EXIT.

       2211-CHECK-NULL-VALUES.
           IF :RPT-DATE-NULL < 0
               MOVE 'N/A' TO :RPT-DATE
           END-IF
           IF :RPT-PROGRAM-ID-NULL < 0
               MOVE 'UNKNOWN' TO :RPT-PROGRAM-ID
           END-IF
           IF :RPT-RECORD-COUNT-NULL < 0
               MOVE 0 TO :RPT-RECORD-COUNT
           END-IF
           IF :RPT-ERROR-COUNT-NULL < 0
               MOVE 0 TO :RPT-ERROR-COUNT
           END-IF
           IF :RPT-STATUS-NULL < 0
               MOVE 'U' TO :RPT-STATUS
           END-IF.
       2211-EXIT.
           EXIT.

       2212-FORMAT-STATISTICS-LINE.
           MOVE :RPT-DATE TO WS-DTL-DATE
           MOVE :RPT-PROGRAM-ID TO WS-DTL-PROGRAM
           MOVE :RPT-RECORD-COUNT TO WS-DTL-RECORDS
           MOVE :RPT-ERROR-COUNT TO WS-DTL-ERRORS
           MOVE :RPT-STATUS TO WS-DTL-STATUS.
       2212-EXIT.
           EXIT.

       2213-WRITE-REPORT-LINE.
           IF WS-LINE-COUNT >= WS-MAX-LINES
               PERFORM 2214-NEW-PAGE THRU 2214-EXIT
           END-IF

           WRITE REPORT-RECORD FROM WS-DETAIL-LINE
           ADD 1 TO WS-LINE-COUNT.
       2213-EXIT.
           EXIT.

       2214-NEW-PAGE.
           ADD 1 TO WS-PAGE-NUMBER
           MOVE 0 TO WS-LINE-COUNT
           PERFORM 2100-PRINT-REPORT-HEADERS THRU 2100-EXIT.
       2214-EXIT.
           EXIT.

      *================================================================
      * SUMMARY REPORT GENERATION
      *================================================================
       2300-GENERATE-SUMMARY-REPORT.
           PERFORM 2310-CALCULATE-SUMMARIES THRU 2310-EXIT
           PERFORM 2320-WRITE-SUMMARY-LINES  THRU 2320-EXIT

           ADD 1 TO WS-REPORTS-GENERATED.
       2300-EXIT.
           EXIT.

       2310-CALCULATE-SUMMARIES.
      *    SIMPLIFIED SUMMARY CALCULATION
           MOVE LS-RECORD-COUNT TO WS-TOTAL-RECORDS
           MOVE LS-ERROR-COUNT TO WS-TOTAL-ERRORS

           DISPLAY 'SUMMARY CALCULATIONS COMPLETED'.
       2310-EXIT.
           EXIT.

       2320-WRITE-SUMMARY-LINES.
           MOVE 'TOTAL RECORDS PROCESSED: ' TO REPORT-RECORD(1:25)
           MOVE WS-TOTAL-RECORDS TO REPORT-RECORD(26:15)
           WRITE REPORT-RECORD

           MOVE 'TOTAL ERRORS ENCOUNTERED: ' TO REPORT-RECORD(1:25)
           MOVE WS-TOTAL-ERRORS TO REPORT-RECORD(26:15)
           WRITE REPORT-RECORD.
       2320-EXIT.
           EXIT.

      *================================================================
      * DETAIL REPORT GENERATION
      *================================================================
       2400-GENERATE-DETAIL-REPORT.
           PERFORM 2410-WRITE-DETAIL-HEADERS THRU 2410-EXIT
           PERFORM 2420-PROCESS-DETAIL-DATA  THRU 2420-EXIT

           ADD 1 TO WS-REPORTS-GENERATED.
       2400-EXIT.
           EXIT.

       2410-WRITE-DETAIL-HEADERS.
           MOVE 'DETAILED PROCESSING REPORT' TO REPORT-RECORD
           WRITE REPORT-RECORD

           MOVE ALL '-' TO REPORT-RECORD
           WRITE REPORT-RECORD.
       2410-EXIT.
           EXIT.

       2420-PROCESS-DETAIL-DATA.
           MOVE 'PROGRAM: CCPBS010 - CUSTOMER VALIDATION' TO REPORT-RECORD
           WRITE REPORT-RECORD

           MOVE 'PROGRAM: CCPBS016 - ACCOUNT PROCESSING' TO REPORT-RECORD
           WRITE REPORT-RECORD

           MOVE 'PROGRAM: CCPBS022 - CARD PROCESSING' TO REPORT-RECORD
           WRITE REPORT-RECORD

           MOVE 'PROGRAM: CCPBS800 - REPORTING SERVICES' TO REPORT-RECORD
           WRITE REPORT-RECORD.
       2420-EXIT.
           EXIT.

      *================================================================
      * REPORT TOTALS
      *================================================================
       2500-PRINT-REPORT-TOTALS.
           MOVE ALL '=' TO REPORT-RECORD
           WRITE REPORT-RECORD

           MOVE 'REPORT GENERATION TOTALS:' TO REPORT-RECORD
           WRITE REPORT-RECORD

           STRING 'TOTAL RECORDS: ' DELIMITED BY SIZE
                  WS-TOTAL-RECORDS DELIMITED BY SIZE
                  INTO REPORT-RECORD
           END-STRING
           WRITE REPORT-RECORD

           STRING 'TOTAL ERRORS: ' DELIMITED BY SIZE
                  WS-TOTAL-ERRORS DELIMITED BY SIZE
                  INTO REPORT-RECORD
           END-STRING
           WRITE REPORT-RECORD

           STRING 'DB2 ROWS PROCESSED: ' DELIMITED BY SIZE
                  WS-DB2-ROWS-PROCESSED DELIMITED BY SIZE
                  INTO REPORT-RECORD
           END-STRING
           WRITE REPORT-RECORD.
       2500-EXIT.
           EXIT.

      *================================================================
      * STATISTICS GENERATION
      *================================================================
       3000-GENERATE-STATISTICS.
           PERFORM 3100-CREATE-STATISTICS-RECORD THRU 3100-EXIT
           PERFORM 3200-WRITE-STATISTICS-FILE    THRU 3200-EXIT
           PERFORM 3300-UPDATE-DB2-STATISTICS    THRU 3300-EXIT.
       3000-EXIT.
           EXIT.

      *================================================================
      * STATISTICS RECORD CREATION
      *================================================================
       3100-CREATE-STATISTICS-RECORD.
           MOVE WS-REPORT-DATE TO STAT-DATE
           MOVE WS-PROGRAM-NAME TO STAT-PROGRAM-NAME
           MOVE WS-TOTAL-RECORDS TO STAT-RECORD-COUNT
           MOVE WS-TOTAL-ERRORS TO STAT-ERROR-COUNT
           MOVE 0 TO STAT-PROCESS-TIME
           MOVE 'C' TO STAT-STATUS

           DISPLAY 'STATISTICS RECORD CREATED'.
       3100-EXIT.
           EXIT.

      *================================================================
      * STATISTICS FILE WRITE
      *================================================================
       3200-WRITE-STATISTICS-FILE.
           WRITE STATISTICS-RECORD

           IF WS-STAT-FILE-OK
               DISPLAY 'STATISTICS WRITTEN TO FILE'
           ELSE
               DISPLAY 'STATISTICS WRITE ERROR: ' WS-STAT-FILE-STATUS
               MOVE 'E045' TO LS-ERROR-CODE
           END-IF.
       3200-EXIT.
           EXIT.

      *================================================================
      * DB2 STATISTICS UPDATE
      *================================================================
       3300-UPDATE-DB2-STATISTICS.
           MOVE WS-REPORT-DATE TO :RPT-DATE
           MOVE WS-PROGRAM-NAME TO :RPT-PROGRAM-ID
           MOVE WS-TOTAL-RECORDS TO :RPT-RECORD-COUNT
           MOVE WS-TOTAL-ERRORS TO :RPT-ERROR-COUNT
           MOVE 'C' TO :RPT-STATUS

           EXEC SQL
               INSERT INTO TCH_PROCESSING_STATS
                   (PROCESS_DATE, PROGRAM_ID, RECORD_COUNT,
                    ERROR_COUNT, PROCESS_STATUS, UPDATE_TIMESTAMP)
               VALUES
                   (:RPT-DATE, :RPT-PROGRAM-ID, :RPT-RECORD-COUNT,
                    :RPT-ERROR-COUNT, :RPT-STATUS, CURRENT TIMESTAMP)
           END-EXEC

           EVALUATE SQLCODE
               WHEN 0
                   DISPLAY 'DB2 STATISTICS INSERTED'
                   EXEC SQL COMMIT END-EXEC
               WHEN -803
                   DISPLAY 'DUPLICATE STATISTICS RECORD'
                   PERFORM 3310-UPDATE-EXISTING-STATS THRU 3310-EXIT
               WHEN OTHER
                   DISPLAY 'DB2 INSERT ERROR: ' SQLCODE
                   EXEC SQL ROLLBACK END-EXEC
                   MOVE 'E046' TO LS-ERROR-CODE
           END-EVALUATE.
       3300-EXIT.
           EXIT.

       3310-UPDATE-EXISTING-STATS.
           EXEC SQL
               UPDATE TCH_PROCESSING_STATS
               SET RECORD_COUNT = :RPT-RECORD-COUNT,
                   ERROR_COUNT = :RPT-ERROR-COUNT,
                   PROCESS_STATUS = :RPT-STATUS,
                   UPDATE_TIMESTAMP = CURRENT TIMESTAMP
               WHERE PROCESS_DATE = :RPT-DATE
                 AND PROGRAM_ID = :RPT-PROGRAM-ID
           END-EXEC

           IF SQLCODE = 0
               DISPLAY 'DB2 STATISTICS UPDATED'
               EXEC SQL COMMIT END-EXEC
           ELSE
               DISPLAY 'DB2 UPDATE ERROR: ' SQLCODE
               EXEC SQL ROLLBACK END-EXEC
               MOVE 'E047' TO LS-ERROR-CODE
           END-IF.
       3310-EXIT.
           EXIT.

      *================================================================
      * CLEANUP PROCESSING
      *================================================================
       9000-CLEANUP.
           PERFORM 9100-CLOSE-DB2-CURSOR   THRU 9100-EXIT
           PERFORM 9200-CLOSE-FILES        THRU 9200-EXIT
           PERFORM 9300-DISPLAY-TOTALS     THRU 9300-EXIT.
       9000-EXIT.
           EXIT.

       9100-CLOSE-DB2-CURSOR.
           EXEC SQL
               CLOSE RPT_CURSOR
           END-EXEC

           IF SQLCODE = 0
               DISPLAY 'DB2 REPORT CURSOR CLOSED'
           ELSE
               DISPLAY 'DB2 CURSOR CLOSE ERROR: ' SQLCODE
           END-IF.
       9100-EXIT.
           EXIT.

       9200-CLOSE-FILES.
           CLOSE REPORT-FILE

           IF WS-REPORT-FILE-OK
               DISPLAY 'REPORT FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'REPORT FILE CLOSE ERROR: ' WS-REPORT-FILE-STATUS
           END-IF

           CLOSE STATISTICS-FILE

           IF WS-STAT-FILE-OK
               DISPLAY 'STATISTICS FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'STATISTICS FILE CLOSE ERROR: ' WS-STAT-FILE-STATUS
           END-IF.
       9200-EXIT.
           EXIT.

       9300-DISPLAY-TOTALS.
           DISPLAY '================================================'
           DISPLAY 'CCPBS800 PROCESSING TOTALS:'
           DISPLAY 'TOTAL RECORDS    : ' WS-TOTAL-RECORDS
           DISPLAY 'TOTAL ERRORS     : ' WS-TOTAL-ERRORS
           DISPLAY 'REPORTS GENERATED: ' WS-REPORTS-GENERATED
           DISPLAY 'DB2 ROWS PROCESSED: ' WS-DB2-ROWS-PROCESSED
           DISPLAY '================================================'.
       9300-EXIT.
           EXIT.

      *================================================================
      * ERROR HANDLING AND ABEND ROUTINE
      *================================================================
       9999-ABEND-ROUTINE.
           DISPLAY '************************************************'
           DISPLAY 'CCPBS800 ABNORMAL TERMINATION'
           DISPLAY 'ERROR CODE: ' LS-ERROR-CODE
           DISPLAY 'RETURN CODE: ' WS-RETURN-CODE
           DISPLAY '************************************************'

           PERFORM 9200-CLOSE-FILES THRU 9200-EXIT
           PERFORM 9100-CLOSE-DB2-CURSOR THRU 9100-EXIT

           MOVE WS-RETURN-CODE TO RETURN-CODE
           GOBACK.
       9999-EXIT.
           EXIT.
