      * THE DRIVER PROGRAM
       IDENTIFICATION DIVISION.
       PROGRAM-ID CPBD888.

       ENVIRONMENT DIVISION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT PRMFILE-FILE         ASSIGN TO PRMF<PERSON><PERSON>
                  FILE STATUS IS WS-PRMFILE-FILE-STATUS.
           SELECT HEX-PRINT-<PERSON>ILE       ASSIGN TO HEXPRNT.
           SELECT CARD-REQUEST-FILE    ASSIGN TO CARDREQ.
           SELECT NEXT-NUMBER-FILE     ASSIGN TO NEXTNUM.
           SELECT CHIPKEY-FILE         ASSIGN TO CHIPKEY
                  ACCESS    IS RA<PERSON>OM
                  ORGANIZATION IS INDEXED
                  RECORD KEY    IS CKI-ACCT-KEY
                  FILE STATUS   IS CHPKY-STATUS.
           SELECT DAY1-FILE            ASSIGN TO DAYIFILE
                  ACCESS    IS DYNAMIC
                  ORGANIZATION IS INDEXED
                  RECORD KEY    IS WS-VSAM-KEY
                  FILE STATUS   IS DAYIFILE-ST.

       DATA DIVISION.

       FILE SECTION.

       FD  PRMFILE-FILE
           LABEL RECORDS ARE STANDARD
           RECORDING MODE IS F.
       01  PRMFILE-FILE-LAYOUT.
           02  PARM-REGION                    PIC X(01).
           02  PARM-FILLER                    PIC X(79).

       FD  HEX-PRINT-FILE
           LABEL RECORDS ARE STANDARD
           RECORDING MODE IS F
           RECORD CONTAINS 80 CHARACTERS
           BLOCK CONTAINS 0 RECORDS
           DATA RECORD IS TRANS-PRINT-HEX-CODES.
       COPY CCPTHPCR.

       FD  CARD-REQUEST-FILE
           LABEL RECORDS ARE STANDARD
           RECORDING MODE IS F
           RECORD CONTAINS 734 CHARACTERS
           BLOCK CONTAINS 0 RECORDS
           DATA RECORD IS TRANSMISSION-DATA.
       01  INPUT-RECORD                       PIC X(734).

       FD  CHIP-KEY-FILE
           RECORD CONTAINS 100 CHARACTERS
           DATA RECORD IS CHIP-KEY-RECORD.
       01  CHIP-KEY-RECORD.
           COPY CCPCKICB.

       FD  DAY1-FILE.
       01  WS-VSAM-KEY.
           05  FS-ACCOUNT-IDENTIFIER          PIC S9(11) COMP-3.
           05  FS-CUSTOMER-ID                 PIC 9(09).

       FD  NEXT-NUMBER-FILE
           DATA RECORD IS NEXT-NUMBER-FILE-LAYOUT.
       01  NEXT-NUMBER-RECORD.
           COPY CCPNMFCB REPLACING ==:*:== BY ==FD-==.


       WORKING-STORAGE SECTION.
       01 WS-CUSTOM-DATA-82-N       PIC 9(8) VALUE 0.

       01 WS-PRIORITY-PASS-CARD.
          05 WS-INIT-PPC-NO         PIC X(2) VALUE '01'.
          05 WS-PPC-NO              PIC X(8) VALUE SPACES.

       01 WS-SIZE-OF-ACCT-NO       PIC 9(02) VALUE ZERO.

       01 WS-CHIP-TYPE-CD          PIC X(01).
       01 WS-CUSTOMER-ID           PIC 9(09).
       01 WS-AREA-VALUES.
          05 WS-A-MARKER            PIC X(40) VALUE
             'CCPBDG00 WORKING STORAGE STARTS HERE ==>'.
          05 WS-GCSXSQ50            PIC X(8) VALUE 'GCSXSQ50'.
          05 WS-GCSABEND            PIC X(8) VALUE 'GCSABEND'.
          05 WS-GCSMSQ00            PIC X(8) VALUE 'GCSMSQ00'.
          05 CBLTDLI                PIC X(8) VALUE 'CBLTDLI'.
          05 WS-WORK-FORM           PIC X(15) VALUE SPACES.

          05 WS-DISC-SW             PIC X(01) VALUE 'N'.
             88 WS-DISC-NOT-FOUND   VALUE 'N'.
             88 WS-DISC-FOUND       VALUE 'Y'.

          05 WS-FEDEX-SW            PIC X(01) VALUE 'N'.
             88 WS-FEDEX-NO         VALUE 'N'.
             88 WS-FEDEX-YES        VALUE 'Y'.

          05 WS-ADDENDUM-SW         PIC X(01) VALUE 'N'.
             88 WS-ADDENDUM-NOT-FOUND VALUE 'N'.
             88 WS-ADDENDUM-FOUND   VALUE 'Y'.

          05 WS-INSERT-TO-PROCESS-IND
                                    PIC X(01) VALUE 'N'.
             88 MORE-INSERTS-TO-PROCESS VALUE 'N'.
             88 NO-MORE-INSERTS     VALUE 'Y'.

          05 WS-CURSOR-OPEN-FLAG    PIC X(01) VALUE 'N'.
             88 CURSOR-NOT-OPENED   VALUE 'N'.
             88 CURSOR-OPENED       VALUE 'Y'.

          05 WS-USE-DISCLOSURE-FLAG PIC X(01) VALUE 'N'.
             88 DO-NOT-USE-DISCLOSURE VALUE 'N'.
             88 USE-DISCLOSURE-FLAG VALUE 'Y'.

          05 WS-USE-ALTADDENDUM1-FLAG PIC X(01) VALUE 'N'.
             88 DO-NOT-USE-ALTADDENDUM VALUE 'N'.
             88 USE-ALTADDENDUM1-FLAG VALUE 'Y'.

          05 WS-AM01-READ-FLAG      PIC X(01) VALUE 'Y'.
             88 AM01S-TO-READ       VALUE 'Y'.
             88 NO-MORE-AM01S       VALUE 'N'.


       05  WS-PINRET-PROCESS-FLAG        PIC X(01) VALUE 'I'.
           88  PINRET-SUCCESS            VALUE 'S'.
           88  PINRET-FAILURE            VALUE 'F'.
           88  PINRET-INPROG             VALUE 'I'.
           88  PINRET-COMPLETE           VALUE 'S' 'F'.

       05  WS-REMAINDER                  PIC 9(04) BINARY.
       05  WS-EXP-YR                     PIC 9(04) BINARY.
       05  WS-EXP-YR-YYYY                PIC 9(04).

       05  WS-TIME-HOLD                  PIC 9(8) VALUE ZEROS.
       05  WS-TIME-REDF REDEFINES WS-TIME-HOLD.
           10 WS-CURR-TIME               PIC 9(6).
           10 FILLER                     PIC X(2).
       05  WS-TIME-HHMMSS REDEFINES WS-TIME-HOLD.
           10 WS-CURR-TIME-HH            PIC 9(2).
           10 WS-CURR-TIME-MM            PIC 9(2).
           10 WS-CURR-TIME-SS            PIC 9(2).
           10 WS-CURR-TIME-HS            PIC 9(2).
       01  SUB1                          PIC S9(04)  COMP.
       01  ZIP-SUB                       PIC S9(02)  COMP.
       01  SUB                           PIC S9(04)  COMP.
       01  SUB2                          PIC S9(04)  COMP.

       COPYBOOK FOR CHECKPOINT/RESTART PARAMETERS

       COPY GICBSCKP.

       ***********************************************************

       PROGRAM-ROUTINES CALLED.

       ***********************************************************
       01  WS-CCPBS010-PROG             PIC X(08) VALUE 'CCPBS010'.
       01  WS-CCPBS016-PROG             PIC X(08) VALUE 'CCPBS016'.
       01  WS-CCPBS022-PROG             PIC X(08) VALUE 'CCPBS022'.
       01  WS-CCPBS070-PROG             PIC X(08) VALUE 'CCPBS070'.
       01  WS-CCPBS080-PROG             PIC X(08) VALUE 'CCPBS080'.
       01  WS-ENDBIF-PROG               PIC X(08) VALUE SPACES.
       01  WS-GCCBSP10-PROG             PIC X(08) VALUE 'GCCBSP10'.
       01  WS-CCPBS800-PROG             PIC X(08) VALUE 'CCPBS800'.
       01  WS-CEVBS030                  PIC X(08) VALUE 'CEVBS030'.
       01  WS-PIM-SERVICE-MODULE        PIC X(08) VALUE 'CCPBS020'.
       CHECKPOINT/RESTART KEY AREA

       01 WS-CHKP-RESTART-KEY-AREA.
          05 WS-CHKP-RESTART-2ND-INDX-KEY.
             10 WS-CHKP-RESTRT-STAGE         PIC X(01) VALUE SPACES.
             10 WS-CHKP-CLIENT-NUM           PIC 9(02) VALUE ZEROS.
             10 WS-CHKP-APPLICATION-NUM      PIC 9(12) VALUE ZEROS.
             10 WS-CHKP-APPLICATION-SFX      PIC 9(02) VALUE ZEROS.
          05 WS-CHKP-RESTART-COUNTERS.
             10 WS-CHKP-RESTRT-CTR-1         PIC 9(09) VALUE ZEROS.
             10 WS-CHKP-RESTRT-CTR-2         PIC 9(09) VALUE ZEROS.
             10 WS-CHKP-RESTRT-CTR-3         PIC 9(09) VALUE ZEROS.
             10 WS-CHKP-RESTRT-CTR-4         PIC 9(09) VALUE ZEROS.
             10 WS-CHKP-RESTRT-CTR-5         PIC 9(09) VALUE ZEROS.
             10 WS-CHKP-RESTRT-CTR-6         PIC 9(09) VALUE ZEROS.
          05 WS-EVENT-478                    PIC X(01) VALUE 'N'.
          88 EVENT-478                                  VALUE 'Y'.
          05 WS-CDATE                        PIC S9(7) COMP-3.
          05 WS-CTIME                        PIC S9(7) COMP-3.
          05 WS-478-JUL-DATE                 PIC S9(5) COMP-3.

       01 WS-SWITCHES.
          05 DAY1FILE-ST                     PIC X(02).
             88 DAY1-FILE-STATUS-OK                    VALUE '00'.
             88 DAY1-FILE-STATUS-NOT-FOUND            VALUE '23'.
          05 WS-PRMFILE-FILE-STATUS          PIC X(02) VALUE '00'.
             88 WS-PRMFILE-OK                          VALUE '00'.
             88 WS-PRMFILE-EOF                         VALUE '10'.
          05 WS-VALID-INFO-CODE              PIC X(02).
             88 WS-CUSTOM-DATA-02                      VALUE '02'.
             88 WS-CUSTOM-DATA-04                      VALUE '04'.
             88 WS-CUSTOM-DATA-05                      VALUE '05'.
          05 WS-FOUND-REC                    PIC X(01) VALUE SPACE.
          05 WS-RESTART-SW                   PIC 9(01) VALUE 0.
             88 WS-RESTART                             VALUE 0.
             88 WS-NO-RESTART                          VALUE 1.
          05 WS-CHECKPOINT-IND               PIC X(01) VALUE 'N'.
             88 WS-INITIAL-CHECKPOINT-TAKEN            VALUE 'N'.
             88 WS-NO-CHECKPOINT-TAKEN                 VALUE 'Y'.

          05 WS-GET-MORE-CUST-INFO           PIC X(01) VALUE 'Y'.
             88 WS-GET-CM00-SEG                        VALUE 'N'.
             88 WS-GET-MORE-CM-SEGS                    VALUE 'Y'.
          05 WS-CM01-SEGMENTS                PIC X(01).
             88 WS-MORE-CM01-SEGS                      VALUE 'Y'.
             88 WS-NO-MORE-CM01-SEGS                   VALUE 'N'.

          05 WS-CM02-SEGMENTS                PIC X(01).
             88 WS-MORE-CM02-SEGS                      VALUE 'Y'.
             88 WS-NO-MORE-CM02-SEGS                   VALUE 'N'.

      * ADDITIONAL WORKING STORAGE VARIABLES
       01  WS-NO-IND                        PIC X(01) VALUE 'N'.
       01  WS-NO-MORE-DATA-TO-PROCESS-IND   PIC X(01) VALUE 'N'.
       01  WS-NO-MORE-CARDS-IND             PIC X(01) VALUE 'N'.
       01  WS-TOTAL-PRIM-CUST-FM-COUNT      PIC 9(09) VALUE ZEROS.
       01  CHPKY-STATUS                     PIC X(02) VALUE '00'.
       01  DAYIFILE-ST                      PIC X(02) VALUE '00'.

      * PCB MASKS FOR IMS
       01  IO-PCB                           PIC X(100).
       01  DBPOB01-PCB-MASK                 PIC X(100).
       01  DBDAM01-PCB-MASK                 PIC X(100).
       01  DBDCU01-PCB-MASK                 PIC X(100).
       01  DBDEL01-PCB-MASK                 PIC X(100).
       01  DBDCC01-PCB-MASK                 PIC X(100).
       01  DBDSH01-PCB-MASK                 PIC X(100).
       01  DBDRW01-PCB-MASK                 PIC X(100).
       01  DBDXA01-PCB-MASK                 PIC X(100).
       01  DBDSC01-PCB-MASK                 PIC X(100).
       01  GSAM-FIXED01-PCB-MASK            PIC X(100).
       01  GSAM-FIXED02-PCB-MASK            PIC X(100).
       01  GSAM-FIXED03-PCB-MASK            PIC X(100).
       01  GSAM-FIXED04-PCB-MASK            PIC X(100).
       01  GSAM-FIXED05-PCB-MASK            PIC X(100).
       01  GSAM-VARIABLE01-PCB-MASK         PIC X(100).
       01  GSAM-FIXED06-PCB-MASK            PIC X(100).
       01  GSAM-FIXED07-PCB-MASK            PIC X(100).
       01  GSAM-FIXED08-PCB-MASK            PIC X(100).
       01  GSAM-FIXED09-PCB-MASK            PIC X(100).
       01  GSAM-FIXED10-PCB-MASK            PIC X(100).
       01  GSAM-FIXED11-PCB-MASK            PIC X(100).

      * SKIPPED A LOT OF DATA STRUCTURES

       PROCEDURE DIVISION USING IO-PCB.
                           DBPOB01-PCB-MASK
                           DBDAM01-PCB-MASK
                           DBDCU01-PCB-MASK
                           DBDEL01-PCB-MASK
                           DBDCC01-PCB-MASK
                           DBDSH01-PCB-MASK
                           DBDRW01-PCB-MASK
                           DBDXA01-PCB-MASK
                           DBDSC01-PCB-MASK
                           GSAM-FIXED01-PCB-MASK
                           GSAM-FIXED02-PCB-MASK
                           GSAM-FIXED03-PCB-MASK
                           GSAM-FIXED04-PCB-MASK
                           GSAM-FIXED05-PCB-MASK
                           GSAM-VARIABLE01-PCB-MASK
                           GSAM-FIXED06-PCB-MASK
                           GSAM-FIXED07-PCB-MASK
                           GSAM-FIXED08-PCB-MASK
                           GSAM-FIXED09-PCB-MASK
                           GSAM-FIXED10-PCB-MASK
                           GSAM-FIXED11-PCB-MASK.

       * IOPCB =================> IO PCB MASK
       * DBPFB01K-PCB-MASK =====> PCB FOR TRANSMISSION FILE
       * DBPFB02K-PCB-MASK =====> PCB FOR TOTALS MANAGEMENT REPORT
       * DBPFB03K-PCB-MASK =====> PCB FOR PREVIOUS ACCOUNT REPORT
       * DBPFB04K-PCB-MASK =====> PCB FOR TOTALS ACCESS INVENTORY
       * DBPFB05K-PCB-MASK =====> PCB FOR FEDEX LABELS
       * GSAM-FIXED01-PCB-MASK =====> GSAM OUTPUT FILE
       * GSAM-FIXED02-PCB-MASK =====> GSAM OUTPUT FILE
       * GSAM-FIXED03-PCB-MASK =====> GSAM OUTPUT FILE
       * GSAM-FIXED04-PCB-MASK =====> GSAM OUTPUT FILE
       * GSAM-FIXED05-PCB-MASK =====> GSAM OUTPUT FILE

       0000-MAINLINE-CONTROL.

           DISPLAY 'PROGRAM CCPB0600 BEGIN PROCESSING'

           MOVE WS-NO-IND                TO WS-NO-MORE-DATA-TO-PROCESS-IND
                                            WS-NO-MORE-CARDS-IND
           PERFORM 1000-INITIALIZATION      THRU 1000-EXIT
           PERFORM 2000-PROCESS-REQUEST-FILE THRU 2000-EXIT
           PERFORM 1700-CLOSE-DAY1FILE      THRU 1700-EXIT
           CLOSE   HEX-PRINT-FILE
                   CARD-REQUEST-FILE
                   PRMFILE-FILE
           PERFORM 5020-CLOSE-CARD-TRAN-FILE THRU 5020-EXIT
           PERFORM 5120-CLOSE-TMIS-FILE      THRU 5120-EXIT
           PERFORM 5220-CLOSE-EMBOSSED-RPT-FILE    THRU 5220-EXIT
           PERFORM 5320-CLOSE-INVENTORY-FILE       THRU 5320-EXIT
           PERFORM 5420-CLOSE-FEDEX-LABELS-FILE    THRU 5420-EXIT
           PERFORM 5520-CLOSE-SCRIPT-REQ-FILE      THRU 5520-EXIT
           PERFORM 5620-CLOSE-CHIP-ERR-REC         THRU 5620-EXIT
           PERFORM 5720-CLOSE-SCIMS-NEW-ADD        THRU 5720-EXIT
           PERFORM 5965-CLOSE-EVENTS-LOG           THRU 5965-EXIT
           PERFORM 5975-CLOSE-EXCEPTION-FILE       THRU 5975-EXIT
           DISPLAY 'CCPB0600....NORMAL END....'
           DISPLAY 'TOTAL-PRIM-CUST-FM    =' WS-TOTAL-PRIM-CUST-FM-COUNT
           PERFORM 9990-TERMINATE               THRU 9990-EXIT
           GOBACK
           EXIT.
       0000-EXIT.
           EXIT.

       1000-INITIALIZATION.
           DISPLAY 'CPBD888: INITIALIZATION STARTED'
           OPEN INPUT  PRMFILE-FILE
                       CARD-REQUEST-FILE
           OPEN OUTPUT HEX-PRINT-FILE
           OPEN I-O    CHIPKEY-FILE
                       DAY1-FILE
                       NEXT-NUMBER-FILE

           PERFORM 1100-READ-PARAMETERS     THRU 1100-EXIT
           PERFORM 1200-INITIALIZE-COUNTERS THRU 1200-EXIT
           PERFORM 1300-SETUP-CHECKPOINT    THRU 1300-EXIT
           DISPLAY 'CPBD888: INITIALIZATION COMPLETE'.
       1000-EXIT.
           EXIT.

       1100-READ-PARAMETERS.
           READ PRMFILE-FILE
           IF WS-PRMFILE-OK
               MOVE PARM-REGION TO WS-WORK-FORM
           ELSE
               DISPLAY 'ERROR READING PARAMETER FILE'
               MOVE 8 TO RETURN-CODE
           END-IF.
       1100-EXIT.
           EXIT.

       1200-INITIALIZE-COUNTERS.
           MOVE ZEROS TO WS-CUSTOM-DATA-82-N
           MOVE ZEROS TO WS-SIZE-OF-ACCT-NO
           MOVE ZEROS TO WS-CUSTOMER-ID
           MOVE SPACES TO WS-CHIP-TYPE-CD.
       1200-EXIT.
           EXIT.

       1300-SETUP-CHECKPOINT.
           MOVE 'N' TO WS-CHECKPOINT-IND
           MOVE ZEROS TO WS-CHKP-RESTRT-CTR-1
                         WS-CHKP-RESTRT-CTR-2
                         WS-CHKP-RESTRT-CTR-3.
       1300-EXIT.
           EXIT.

       1700-CLOSE-DAY1FILE.
           CLOSE DAY1-FILE
           IF DAY1-FILE-STATUS-OK
               DISPLAY 'DAY1 FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'ERROR CLOSING DAY1 FILE: ' DAYIFILE-ST
           END-IF.
       1700-EXIT.
           EXIT.

       2000-PROCESS-REQUEST-FILE.
           DISPLAY 'CPBD888: PROCESSING REQUEST FILE'
           PERFORM 2100-READ-REQUEST-RECORD THRU 2100-EXIT
               UNTIL WS-PRMFILE-EOF
           DISPLAY 'CPBD888: REQUEST FILE PROCESSING COMPLETE'.
       2000-EXIT.
           EXIT.

       2100-READ-REQUEST-RECORD.
           READ CARD-REQUEST-FILE INTO INPUT-RECORD
           AT END
               SET WS-PRMFILE-EOF TO TRUE
           NOT AT END
               PERFORM 2200-PROCESS-CARD-REQUEST THRU 2200-EXIT
           END-READ.
       2100-EXIT.
           EXIT.

       2200-PROCESS-CARD-REQUEST.
           DISPLAY 'PROCESSING CARD REQUEST: ' INPUT-RECORD(1:20)
           PERFORM 2300-VALIDATE-REQUEST    THRU 2300-EXIT
           PERFORM 2400-CALL-BUSINESS-LOGIC THRU 2400-EXIT
           PERFORM 2500-WRITE-HEX-OUTPUT    THRU 2500-EXIT.
       2200-EXIT.
           EXIT.

       2300-VALIDATE-REQUEST.
           IF INPUT-RECORD = SPACES
               DISPLAY 'INVALID REQUEST RECORD'
               MOVE 'N' TO WS-FOUND-REC
           ELSE
               MOVE 'Y' TO WS-FOUND-REC
           END-IF.
       2300-EXIT.
           EXIT.

       2400-CALL-BUSINESS-LOGIC.
           CALL WS-CCPBS010-PROG USING INPUT-RECORD
           CALL WS-CCPBS016-PROG USING INPUT-RECORD
           CALL WS-CCPBS022-PROG USING INPUT-RECORD.
       2400-EXIT.
           EXIT.

       2500-WRITE-HEX-OUTPUT.
           MOVE INPUT-RECORD(1:60) TO TPH-HEX-DATA
           MOVE 'TX' TO TPH-RECORD-TYPE
           MOVE '1234567890' TO TPH-TRANSACTION-ID
           MOVE 'ABCD' TO TPH-CHECKSUM
           WRITE TRANS-PRINT-HEX-CODES.
       2500-EXIT.
           EXIT.

       5020-CLOSE-CARD-TRAN-FILE.
           DISPLAY 'CLOSING CARD TRANSACTION FILE'.
       5020-EXIT.
           EXIT.

       5120-CLOSE-TMIS-FILE.
           DISPLAY 'CLOSING TMIS FILE'.
       5120-EXIT.
           EXIT.

       5220-CLOSE-EMBOSSED-RPT-FILE.
           DISPLAY 'CLOSING EMBOSSED REPORT FILE'.
       5220-EXIT.
           EXIT.

       5320-CLOSE-INVENTORY-FILE.
           DISPLAY 'CLOSING INVENTORY FILE'.
       5320-EXIT.
           EXIT.

       5420-CLOSE-FEDEX-LABELS-FILE.
           DISPLAY 'CLOSING FEDEX LABELS FILE'.
       5420-EXIT.
           EXIT.

       5520-CLOSE-SCRIPT-REQ-FILE.
           DISPLAY 'CLOSING SCRIPT REQUEST FILE'.
       5520-EXIT.
           EXIT.

       5620-CLOSE-CHIP-ERR-REC.
           DISPLAY 'CLOSING CHIP ERROR RECORD FILE'.
       5620-EXIT.
           EXIT.

       5720-CLOSE-SCIMS-NEW-ADD.
           DISPLAY 'CLOSING SCIMS NEW ADDRESS FILE'.
       5720-EXIT.
           EXIT.

       5965-CLOSE-EVENTS-LOG.
           DISPLAY 'CLOSING EVENTS LOG FILE'.
       5965-EXIT.
           EXIT.

       5975-CLOSE-EXCEPTION-FILE.
           DISPLAY 'CLOSING EXCEPTION FILE'.
       5975-EXIT.
           EXIT.

       9990-TERMINATE.
           DISPLAY 'CPBD888: TERMINATION PROCESSING'
           CALL WS-CCPBS800-PROG USING WS-CUSTOM-DATA-82-N
           DISPLAY 'CPBD888: PROGRAM TERMINATED NORMALLY'.
       9990-EXIT.
           EXIT.

