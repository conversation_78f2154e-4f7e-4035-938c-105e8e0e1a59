      *================================================================
      * PROGRAM GCSXSQ50.CBL - GENERAL CONTROL SYSTEM SEQUENCE 50
      * Called by: CPBD888 and related programs
      * Purpose: Sequence control and management
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. GCSXSQ50.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'GCSXSQ50'.
       
       LINKAGE SECTION.
       01  LS-SEQUENCE-DATA.
           05  LS-SEQUENCE-NUMBER      PIC 9(10).
           05  LS-SEQUENCE-TYPE        PIC X(04).
           05  LS-SEQUENCE-STATUS      PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).
       
       PROCEDURE DIVISION USING LS-SEQUENCE-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'GCSXSQ50: SEQUENCE PROCESSING STARTED'
           
           ADD 1 TO LS-SEQUENCE-NUMBER
           MOVE 'A' TO LS-SEQUENCE-STATUS
           MOVE SPACES TO LS-ERROR-CODE
           MOVE 0 TO RETURN-CODE
           
           DISPLAY 'GCSXSQ50: SEQUENCE NUMBER: ' LS-SEQUENCE-NUMBER
           GOBACK.
