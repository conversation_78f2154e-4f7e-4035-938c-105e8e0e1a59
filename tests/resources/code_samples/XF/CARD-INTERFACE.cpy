      *================================================================
      * COPYBOOK: CARD-INTERFACE.CPY
      * Purpose: Standard card processing interface for program calls
      * Used by: CCPBS016, CCPBS022, and related programs
      * Last Modified: 01/15/2024
      *================================================================
      
      *================================================================
      * CARD PROCESSING INTERFACE
      *================================================================
       01  CARD-INTERFACE.
           05  CARD-NUMBER             PIC X(16).
           05  CARD-TYPE               PIC X(02).
               88  CARD-TYPE-CREDIT    VALUE 'CC'.
               88  CARD-TYPE-DEBIT     VALUE 'DC'.
               88  CARD-TYPE-ATM       VALUE 'AT'.
           05  CARD-EXPIRATION-DATE    PIC X(06).
           05  CARD-CUSTOMER-ID        PIC X(09).
           05  CARD-ACCOUNT-NUMBER     PIC X(20).
           05  CARD-EMBOSS-FLAG        PIC X(01).
               88  CARD-EMBOSS-OK      VALUE 'Y'.
               88  CARD-EMBOSS-FAIL    VALUE 'N'.
           05  CARD-PRIORITY-CODE      PIC X(01).
               88  CARD-PRIORITY-HIGH  VALUE '1'.
               88  CARD-PRIORITY-LOW   VALUE '9'.
           05  CARD-STATUS             PIC X(01).
               88  CARD-STATUS-ACTIVE  VALUE 'A'.
               88  CARD-STATUS-PENDING VALUE 'P'.
               88  CARD-STATUS-CANCEL  VALUE 'C'.
               88  CARD-STATUS-LOST    VALUE 'L'.
           05  CARD-ERROR-CODE         PIC X(04).
           05  CARD-ERROR-MESSAGE      PIC X(80).
           05  CARD-RETURN-CODE        PIC 9(04).
           05  CARD-FILLER             PIC X(20).
