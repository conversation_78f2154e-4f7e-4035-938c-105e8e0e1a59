      *================================================================
      * PROGRAM CCPBS020.CBL - PIM SERVICE MODULE
      * Called by: CPBD888 and related programs
      * Purpose: Personal Information Management services
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CCPBS020.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CCPBS020'.
       
       LINKAGE SECTION.
       01  LS-PIM-DATA.
           05  LS-CUSTOMER-ID          PIC 9(09).
           05  LS-PERSONAL-INFO        PIC X(200).
           05  LS-PRIVACY-FLAG         PIC X(01).
           05  LS-UPDATE-FLAG          PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).
       
       PROCEDURE DIVISION USING LS-PIM-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'CCPBS020: PIM PROCESSING STARTED'
           
           IF LS-CUSTOMER-ID = ZEROS
               MOVE 'E008' TO LS-ERROR-CODE
               MOVE 'N' TO LS-UPDATE-FLAG
               MOVE 8 TO RETURN-CODE
           ELSE
               MOVE 'Y' TO LS-PRIVACY-FLAG
               MOVE 'Y' TO LS-UPDATE-FLAG
               MOVE SPACES TO LS-ERROR-CODE
               MOVE 0 TO RETURN-CODE
           END-IF
           
           DISPLAY 'CCPBS020: PROCESSING COMPLETE'
           GOBACK.
