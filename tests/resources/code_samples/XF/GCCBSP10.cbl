      *================================================================
      * PROGRAM GCCBSP10.CBL - GENERAL CONTROL BLOCK SERVICE PROGRAM
      * Called by: CPBD888 and related programs
      * Purpose: General utility and control functions
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. GCCBSP10.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'GCCBSP10'.
       
       LINKAGE SECTION.
       01  LS-CONTROL-DATA.
           05  LS-FUNCTION-CODE        PIC X(04).
           05  LS-INPUT-DATA           PIC X(100).
           05  LS-OUTPUT-DATA          PIC X(100).
           05  LS-RETURN-CODE          PIC 9(04).
           05  LS-ERROR-MESSAGE        PIC X(80).
       
       PROCEDURE DIVISION USING LS-CONTROL-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'GCCBSP10: CONTROL PROCESSING STARTED'
           
           EVALUATE LS-FUNCTION-CODE
               WHEN 'INIT'
                   MOVE 'INITIALIZED' TO LS-OUTPUT-DATA
                   MOVE 0 TO LS-RETURN-CODE
               WHEN 'TERM'
                   MOVE 'TERMINATED' TO LS-OUTPUT-DATA
                   MOVE 0 TO LS-RETURN-CODE
               WHEN OTHER
                   MOVE 'UNKNOWN FUNCTION' TO LS-ERROR-MESSAGE
                   MOVE 8 TO LS-RETURN-CODE
           END-EVALUATE
           
           MOVE LS-RETURN-CODE TO RETURN-CODE
           DISPLAY 'GCCBSP10: PROCESSING COMPLETE'
           GOBACK.
