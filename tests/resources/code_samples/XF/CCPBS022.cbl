      *================================================================
      * PROGRAM CCPBS022.CBL - BUSINESS SERVICE MODULE 022
      * Called by: CCPBS016 and related programs
      * Purpose: Card processing and embossing with IMS database access
      * Dependencies: None (terminal program in chain)
      * Last Modified: 01/15/2024
      * Enhanced: Complex paragraph dependencies, IMS DLI, file I/O
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CCPBS022.

       ENVIRONMENT DIVISION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT CARD-MASTER-FILE     ASSIGN TO CARDMAST
                  ACCESS IS RANDOM
                  ORGANIZATION IS INDEXED
                  RECORD KEY IS CARD-MASTER-KEY
                  FILE STATUS IS WS-CARD-FILE-STATUS.

           SELECT EMBOSS-REQUEST-FILE  ASSIGN TO EMBOSSREQ
                  ACCESS IS SEQUENTIAL
                  ORGANIZATION IS SEQUENTIAL
                  FILE STATUS IS WS-EMBOSS-FILE-STATUS.

       DATA DIVISION.
       FILE SECTION.
       FD  CARD-MASTER-FILE
           RECORD CONTAINS 120 CHARACTERS
           DATA RECORD IS CARD-MASTER-RECORD.
       01  CARD-MASTER-RECORD.
           05  CARD-MASTER-KEY         PIC X(16).
           05  CARD-ACCOUNT-NUMBER     PIC X(20).
           05  CARD-CUSTOMER-ID        PIC 9(09).
           05  CARD-TYPE-CODE          PIC X(02).
           05  CARD-STATUS             PIC X(01).
           05  CARD-ISSUE-DATE         PIC X(08).
           05  CARD-EXPIRY-DATE        PIC X(06).
           05  CARD-EMBOSS-NAME        PIC X(26).
           05  CARD-PIN-OFFSET         PIC X(04).
           05  CARD-FILLER             PIC X(44).

       FD  EMBOSS-REQUEST-FILE
           RECORD CONTAINS 100 CHARACTERS
           DATA RECORD IS EMBOSS-REQUEST-RECORD.
       01  EMBOSS-REQUEST-RECORD.
           05  EMB-CARD-NUMBER         PIC X(16).
           05  EMB-CUSTOMER-NAME       PIC X(30).
           05  EMB-CARD-TYPE           PIC X(02).
           05  EMB-PRIORITY-CODE       PIC X(01).
           05  EMB-EXPIRY-DATE         PIC X(06).
           05  EMB-REQUEST-DATE        PIC X(08).
           05  EMB-REQUEST-TIME        PIC X(06).
           05  EMB-BRANCH-CODE         PIC X(04).
           05  EMB-FILLER              PIC X(27).

       WORKING-STORAGE SECTION.
      *================================================================
      * PROGRAM CONTROL VARIABLES
      *================================================================
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CCPBS022'.
       01  WS-RETURN-CODE              PIC 9(04) VALUE 0.
       01  WS-CARD-FILE-STATUS         PIC X(02) VALUE '00'.
           88  WS-CARD-FILE-OK         VALUE '00'.
           88  WS-CARD-FILE-NOT-FOUND  VALUE '23'.
       01  WS-EMBOSS-FILE-STATUS       PIC X(02) VALUE '00'.
           88  WS-EMBOSS-FILE-OK       VALUE '00'.

      *================================================================
      * PROCESSING CONTROL FLAGS
      *================================================================
       01  WS-PROCESSING-FLAGS.
           05  WS-CARD-VALID-FLAG      PIC X(01) VALUE 'N'.
               88  WS-CARD-VALID       VALUE 'Y'.
               88  WS-CARD-INVALID     VALUE 'N'.
           05  WS-EMBOSS-READY-FLAG    PIC X(01) VALUE 'N'.
               88  WS-EMBOSS-READY     VALUE 'Y'.
               88  WS-EMBOSS-NOT-READY VALUE 'N'.
           05  WS-PIN-REQUIRED-FLAG    PIC X(01) VALUE 'N'.
               88  WS-PIN-REQUIRED     VALUE 'Y'.
               88  WS-PIN-NOT-REQUIRED VALUE 'N'.

      *================================================================
      * COUNTERS AND STATISTICS
      *================================================================
       01  WS-COUNTERS.
           05  WS-CARD-READ-COUNT      PIC 9(06) VALUE 0.
           05  WS-EMBOSS-WRITE-COUNT   PIC 9(06) VALUE 0.
           05  WS-IMS-INSERT-COUNT     PIC 9(06) VALUE 0.
           05  WS-ERROR-COUNT          PIC 9(06) VALUE 0.

      *================================================================
      * IMS DLI VARIABLES
      *================================================================
       01  WS-DLI-FUNCTIONS.
           05  FC-GU                   PIC X(04) VALUE 'GU  '.
           05  FC-ISRT                 PIC X(04) VALUE 'ISRT'.
           05  FC-REPL                 PIC X(04) VALUE 'REPL'.

       01  WS-IMS-STATUS               PIC X(02) VALUE SPACES.
           88  IMS-STATUS-OK           VALUE SPACES.
           88  IMS-STATUS-NOT-FOUND    VALUE 'GE'.
           88  IMS-STATUS-DUPLICATE    VALUE 'II'.

      *================================================================
      * IMS SEGMENT LAYOUTS
      *================================================================
       01  CARD-MASTER-SEGMENT.
           05  CMS-CARD-NUMBER         PIC X(16).
           05  CMS-ACCOUNT-NUMBER      PIC X(20).
           05  CMS-CUSTOMER-ID         PIC 9(09).
           05  CMS-CARD-TYPE           PIC X(02).
           05  CMS-STATUS-CODE         PIC X(01).
           05  CMS-ISSUE-DATE          PIC X(08).
           05  CMS-EXPIRY-DATE         PIC X(06).
           05  CMS-PIN-OFFSET          PIC X(04).
           05  CMS-FILLER              PIC X(34).

       01  CARD-HISTORY-SEGMENT.
           05  CHS-CARD-NUMBER         PIC X(16).
           05  CHS-ACTION-CODE         PIC X(02).
           05  CHS-ACTION-DATE         PIC X(08).
           05  CHS-ACTION-TIME         PIC X(06).
           05  CHS-USER-ID             PIC X(08).
           05  CHS-REASON-CODE         PIC X(04).
           05  CHS-FILLER              PIC X(56).

       01  CARD-IMS-SEGMENT            PIC X(100).
       01  HIST-IMS-SEGMENT            PIC X(100).

      *================================================================
      * WORK AREAS
      *================================================================
       01  WS-WORK-AREAS.
           05  WS-CURRENT-DATE         PIC X(08).
           05  WS-CURRENT-TIME         PIC X(06).
           05  WS-PIN-WORK-AREA        PIC X(08).
           05  WS-VALIDATION-RESULT    PIC X(01).
               88  WS-VALIDATION-OK    VALUE 'Y'.
               88  WS-VALIDATION-FAIL  VALUE 'N'.

       LINKAGE SECTION.
      *================================================================
      * STANDARDIZED CARD INTERFACE (RECEIVED FROM CCPBS016)
      *================================================================
           COPY CARD-INTERFACE.

       01  LS-CARD-DATA.
           05  LS-CARD-NUMBER          PIC X(16).
           05  LS-CARD-TYPE            PIC X(02).
           05  LS-EXPIRATION-DATE      PIC X(06).
           05  LS-CARD-CUSTOMER-ID     PIC X(09).
           05  LS-CARD-ACCOUNT-NUMBER  PIC X(20).
           05  LS-EMBOSS-FLAG          PIC X(01).
           05  LS-PRIORITY-CODE        PIC X(01).
           05  LS-CARD-STATUS          PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).
           05  LS-ERROR-MESSAGE        PIC X(80).
           05  LS-RETURN-CODE          PIC 9(04).
           05  LS-FILLER               PIC X(20).

      *================================================================
      * IMS PCB LINKAGE
      *================================================================
       01  GSAM-FIXED05-PCB-MASK      PIC X(100).

       PROCEDURE DIVISION USING LS-CARD-DATA
                                GSAM-FIXED05-PCB-MASK.

      *================================================================
      * MAIN PROCESSING CONTROL
      *================================================================
       0000-MAIN-PROCESSING.
           DISPLAY 'CCPBS022: CARD PROCESSING STARTED'

           PERFORM 1000-INITIALIZATION     THRU 1000-EXIT
           PERFORM 2000-VALIDATE-CARD      THRU 2000-EXIT
           PERFORM 3000-PROCESS-CARD       THRU 3000-EXIT
           PERFORM 4000-EMBOSS-PROCESSING  THRU 4000-EXIT
           PERFORM 5000-IMS-PROCESSING     THRU 5000-EXIT
           PERFORM 9000-CLEANUP            THRU 9000-EXIT

           DISPLAY 'CCPBS022: PROCESSING COMPLETE'
           GOBACK.

      *================================================================
      * INITIALIZATION PROCESSING
      *================================================================
       1000-INITIALIZATION.
           DISPLAY 'CCPBS022: INITIALIZATION STARTED'

           MOVE FUNCTION CURRENT-DATE(1:8) TO WS-CURRENT-DATE
           MOVE FUNCTION CURRENT-DATE(9:6) TO WS-CURRENT-TIME

           MOVE ZEROS TO WS-CARD-READ-COUNT
                         WS-EMBOSS-WRITE-COUNT
                         WS-IMS-INSERT-COUNT
                         WS-ERROR-COUNT

           PERFORM 1100-OPEN-FILES         THRU 1100-EXIT
           PERFORM 1200-VALIDATE-INPUT     THRU 1200-EXIT.
       1000-EXIT.
           EXIT.

      *================================================================
      * FILE OPERATIONS
      *================================================================
       1100-OPEN-FILES.
           OPEN I-O CARD-MASTER-FILE

           IF NOT WS-CARD-FILE-OK
               DISPLAY 'ERROR OPENING CARD MASTER FILE: ' WS-CARD-FILE-STATUS
               MOVE 'E030' TO LS-ERROR-CODE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           END-IF

           OPEN OUTPUT EMBOSS-REQUEST-FILE

           IF NOT WS-EMBOSS-FILE-OK
               DISPLAY 'ERROR OPENING EMBOSS FILE: ' WS-EMBOSS-FILE-STATUS
               MOVE 'E031' TO LS-ERROR-CODE
               MOVE 8 TO WS-RETURN-CODE
               PERFORM 9999-ABEND-ROUTINE
           END-IF.
       1100-EXIT.
           EXIT.

      *================================================================
      * INPUT VALIDATION
      *================================================================
       1200-VALIDATE-INPUT.
           IF LS-CARD-NUMBER = SPACES
               MOVE 'E003' TO LS-ERROR-CODE
               MOVE 'N' TO LS-EMBOSS-FLAG
               MOVE 8 TO WS-RETURN-CODE
               SET WS-CARD-INVALID TO TRUE
           ELSE
               MOVE SPACES TO LS-ERROR-CODE
               MOVE 0 TO WS-RETURN-CODE
               SET WS-CARD-VALID TO TRUE
           END-IF.
       1200-EXIT.
           EXIT.

      *================================================================
      * CARD VALIDATION WITH FILE ACCESS
      *================================================================
       2000-VALIDATE-CARD.
           IF WS-CARD-VALID
               PERFORM 2100-READ-CARD-MASTER   THRU 2100-EXIT
               PERFORM 2200-VALIDATE-CARD-DATA THRU 2200-EXIT
               PERFORM 2300-CHECK-BUSINESS-RULES THRU 2300-EXIT
           END-IF.
       2000-EXIT.
           EXIT.

      *================================================================
      * CARD MASTER FILE ACCESS
      *================================================================
       2100-READ-CARD-MASTER.
           MOVE LS-CARD-NUMBER TO CARD-MASTER-KEY

           READ CARD-MASTER-FILE

           EVALUATE WS-CARD-FILE-STATUS
               WHEN '00'
                   ADD 1 TO WS-CARD-READ-COUNT
                   DISPLAY 'CARD MASTER FOUND: ' CARD-MASTER-KEY
                   SET WS-CARD-VALID TO TRUE
               WHEN '23'
                   DISPLAY 'CARD NOT FOUND: ' CARD-MASTER-KEY
                   PERFORM 2110-CREATE-NEW-CARD THRU 2110-EXIT
               WHEN OTHER
                   DISPLAY 'CARD FILE READ ERROR: ' WS-CARD-FILE-STATUS
                   MOVE 'E032' TO LS-ERROR-CODE
                   ADD 1 TO WS-ERROR-COUNT
                   SET WS-CARD-INVALID TO TRUE
           END-EVALUATE.
       2100-EXIT.
           EXIT.

      *================================================================
      * NEW CARD CREATION
      *================================================================
       2110-CREATE-NEW-CARD.
           MOVE LS-CARD-NUMBER TO CARD-MASTER-KEY
           MOVE LS-CARD-CUSTOMER-ID TO CARD-CUSTOMER-ID
           MOVE LS-CARD-ACCOUNT-NUMBER TO CARD-ACCOUNT-NUMBER
           MOVE LS-CARD-TYPE TO CARD-TYPE-CODE
           MOVE 'P' TO CARD-STATUS
           MOVE WS-CURRENT-DATE TO CARD-ISSUE-DATE
           MOVE LS-EXPIRATION-DATE TO CARD-EXPIRY-DATE
           MOVE 'NEW CARD HOLDER' TO CARD-EMBOSS-NAME
           MOVE '0000' TO CARD-PIN-OFFSET

           WRITE CARD-MASTER-RECORD

           IF WS-CARD-FILE-OK
               DISPLAY 'NEW CARD CREATED: ' CARD-MASTER-KEY
               SET WS-CARD-VALID TO TRUE
           ELSE
               DISPLAY 'CARD CREATION FAILED: ' WS-CARD-FILE-STATUS
               MOVE 'E033' TO LS-ERROR-CODE
               ADD 1 TO WS-ERROR-COUNT
               SET WS-CARD-INVALID TO TRUE
           END-IF.
       2110-EXIT.
           EXIT.

      *================================================================
      * CARD DATA VALIDATION
      *================================================================
       2200-VALIDATE-CARD-DATA.
           MOVE 'Y' TO WS-VALIDATION-RESULT

           IF CARD-STATUS = 'C'
               DISPLAY 'CARD IS CANCELLED: ' CARD-MASTER-KEY
               MOVE 'W011' TO LS-ERROR-CODE
               MOVE 'N' TO WS-VALIDATION-RESULT
           END-IF

           IF CARD-STATUS = 'L'
               DISPLAY 'CARD IS LOST/STOLEN: ' CARD-MASTER-KEY
               MOVE 'E034' TO LS-ERROR-CODE
               MOVE 'N' TO WS-VALIDATION-RESULT
               ADD 1 TO WS-ERROR-COUNT
           END-IF

           IF CARD-EXPIRY-DATE < WS-CURRENT-DATE(1:6)
               DISPLAY 'CARD IS EXPIRED: ' CARD-EXPIRY-DATE
               MOVE 'W012' TO LS-ERROR-CODE
               MOVE 'N' TO WS-VALIDATION-RESULT
           END-IF.
       2200-EXIT.
           EXIT.

      *================================================================
      * BUSINESS RULES VALIDATION
      *================================================================
       2300-CHECK-BUSINESS-RULES.
           IF WS-VALIDATION-OK
               EVALUATE LS-CARD-TYPE
                   WHEN 'CC'
                       SET WS-PIN-REQUIRED TO TRUE
                       DISPLAY 'CREDIT CARD - PIN REQUIRED'
                   WHEN 'DC'
                       SET WS-PIN-REQUIRED TO TRUE
                       DISPLAY 'DEBIT CARD - PIN REQUIRED'
                   WHEN 'AT'
                       SET WS-PIN-NOT-REQUIRED TO TRUE
                       DISPLAY 'ATM CARD - NO PIN REQUIRED'
                   WHEN OTHER
                       DISPLAY 'UNKNOWN CARD TYPE: ' LS-CARD-TYPE
                       MOVE 'E035' TO LS-ERROR-CODE
                       MOVE 'N' TO WS-VALIDATION-RESULT
                       ADD 1 TO WS-ERROR-COUNT
               END-EVALUATE
           END-IF.
       2300-EXIT.
           EXIT.

      *================================================================
      * CARD PROCESSING
      *================================================================
       3000-PROCESS-CARD.
           IF WS-VALIDATION-OK
               PERFORM 3100-GENERATE-PIN       THRU 3100-EXIT
               PERFORM 3200-UPDATE-CARD-STATUS THRU 3200-EXIT
               PERFORM 3300-SET-EMBOSS-FLAG    THRU 3300-EXIT
           END-IF.
       3000-EXIT.
           EXIT.

      *================================================================
      * PIN GENERATION
      *================================================================
       3100-GENERATE-PIN.
           IF WS-PIN-REQUIRED
               PERFORM 3110-CALCULATE-PIN-OFFSET THRU 3110-EXIT
               PERFORM 3120-VALIDATE-PIN-OFFSET  THRU 3120-EXIT
           ELSE
               MOVE '0000' TO CARD-PIN-OFFSET
               DISPLAY 'NO PIN REQUIRED FOR CARD TYPE: ' LS-CARD-TYPE
           END-IF.
       3100-EXIT.
           EXIT.

       3110-CALCULATE-PIN-OFFSET.
      *    SIMPLIFIED PIN OFFSET CALCULATION
           COMPUTE WS-PIN-WORK-AREA =
               FUNCTION NUMVAL(CARD-CUSTOMER-ID) + 1234

           MOVE WS-PIN-WORK-AREA(5:4) TO CARD-PIN-OFFSET
           DISPLAY 'PIN OFFSET CALCULATED: ' CARD-PIN-OFFSET.
       3110-EXIT.
           EXIT.

       3120-VALIDATE-PIN-OFFSET.
           IF CARD-PIN-OFFSET = '0000' OR CARD-PIN-OFFSET = SPACES
               MOVE '9999' TO CARD-PIN-OFFSET
               DISPLAY 'DEFAULT PIN OFFSET ASSIGNED'
           END-IF.
       3120-EXIT.
           EXIT.

      *================================================================
      * CARD STATUS UPDATE
      *================================================================
       3200-UPDATE-CARD-STATUS.
           MOVE 'A' TO CARD-STATUS
           MOVE WS-CURRENT-DATE TO CARD-ISSUE-DATE

           REWRITE CARD-MASTER-RECORD

           IF WS-CARD-FILE-OK
               DISPLAY 'CARD STATUS UPDATED: ' CARD-MASTER-KEY
               SET WS-EMBOSS-READY TO TRUE
           ELSE
               DISPLAY 'CARD UPDATE FAILED: ' WS-CARD-FILE-STATUS
               MOVE 'E036' TO LS-ERROR-CODE
               ADD 1 TO WS-ERROR-COUNT
               SET WS-EMBOSS-NOT-READY TO TRUE
           END-IF.
       3200-EXIT.
           EXIT.

      *================================================================
      * EMBOSS FLAG SETTING
      *================================================================
       3300-SET-EMBOSS-FLAG.
           IF WS-EMBOSS-READY AND WS-VALIDATION-OK
               MOVE 'Y' TO LS-EMBOSS-FLAG
               MOVE '1' TO LS-PRIORITY-CODE
               MOVE 'A' TO LS-CARD-STATUS
               MOVE SPACES TO LS-ERROR-CODE
               MOVE SPACES TO LS-ERROR-MESSAGE
               MOVE 0 TO LS-RETURN-CODE
               DISPLAY 'CARD READY FOR EMBOSSING: ' CARD-MASTER-KEY
           ELSE
               MOVE 'N' TO LS-EMBOSS-FLAG
               MOVE '9' TO LS-PRIORITY-CODE
               MOVE 'P' TO LS-CARD-STATUS
               DISPLAY 'CARD NOT READY FOR EMBOSSING'
           END-IF.
       3300-EXIT.
           EXIT.

      *================================================================
      * EMBOSS PROCESSING
      *================================================================
       4000-EMBOSS-PROCESSING.
           IF LS-EMBOSS-FLAG = 'Y'
               PERFORM 4100-CREATE-EMBOSS-REQUEST THRU 4100-EXIT
               PERFORM 4200-WRITE-EMBOSS-FILE     THRU 4200-EXIT
           END-IF.
       4000-EXIT.
           EXIT.

      *================================================================
      * EMBOSS REQUEST CREATION
      *================================================================
       4100-CREATE-EMBOSS-REQUEST.
           MOVE LS-CARD-NUMBER TO EMB-CARD-NUMBER
           MOVE CARD-EMBOSS-NAME TO EMB-CUSTOMER-NAME
           MOVE LS-CARD-TYPE TO EMB-CARD-TYPE
           MOVE LS-PRIORITY-CODE TO EMB-PRIORITY-CODE
           MOVE LS-EXPIRATION-DATE TO EMB-EXPIRY-DATE
           MOVE WS-CURRENT-DATE TO EMB-REQUEST-DATE
           MOVE WS-CURRENT-TIME TO EMB-REQUEST-TIME
           MOVE '0001' TO EMB-BRANCH-CODE

           DISPLAY 'EMBOSS REQUEST CREATED FOR: ' EMB-CARD-NUMBER.
       4100-EXIT.
           EXIT.

      *================================================================
      * EMBOSS FILE WRITE
      *================================================================
       4200-WRITE-EMBOSS-FILE.
           WRITE EMBOSS-REQUEST-RECORD

           IF WS-EMBOSS-FILE-OK
               ADD 1 TO WS-EMBOSS-WRITE-COUNT
               DISPLAY 'EMBOSS REQUEST WRITTEN: ' EMB-CARD-NUMBER
           ELSE
               DISPLAY 'EMBOSS WRITE ERROR: ' WS-EMBOSS-FILE-STATUS
               MOVE 'E037' TO LS-ERROR-CODE
               ADD 1 TO WS-ERROR-COUNT
           END-IF.
       4200-EXIT.
           EXIT.

      *================================================================
      * IMS DATABASE PROCESSING
      *================================================================
       5000-IMS-PROCESSING.
           IF LS-EMBOSS-FLAG = 'Y'
               PERFORM 5100-INSERT-CARD-MASTER  THRU 5100-EXIT
               PERFORM 5200-INSERT-CARD-HISTORY THRU 5200-EXIT
           END-IF.
       5000-EXIT.
           EXIT.

      *================================================================
      * IMS CARD MASTER INSERT
      *================================================================
       5100-INSERT-CARD-MASTER.
           MOVE CARD-MASTER-KEY TO CMS-CARD-NUMBER
           MOVE CARD-ACCOUNT-NUMBER TO CMS-ACCOUNT-NUMBER
           MOVE CARD-CUSTOMER-ID TO CMS-CUSTOMER-ID
           MOVE CARD-TYPE-CODE TO CMS-CARD-TYPE
           MOVE CARD-STATUS TO CMS-STATUS-CODE
           MOVE CARD-ISSUE-DATE TO CMS-ISSUE-DATE
           MOVE CARD-EXPIRY-DATE TO CMS-EXPIRY-DATE
           MOVE CARD-PIN-OFFSET TO CMS-PIN-OFFSET

           MOVE CARD-MASTER-SEGMENT TO CARD-IMS-SEGMENT

           CALL 'CBLTDLI' USING FC-ISRT
                                GSAM-FIXED05-PCB-MASK
                                CARD-IMS-SEGMENT

           MOVE CARD-IMS-SEGMENT(1:2) TO WS-IMS-STATUS

           EVALUATE TRUE
               WHEN IMS-STATUS-OK
                   ADD 1 TO WS-IMS-INSERT-COUNT
                   DISPLAY 'IMS CARD MASTER INSERTED: ' CMS-CARD-NUMBER
               WHEN IMS-STATUS-DUPLICATE
                   DISPLAY 'DUPLICATE CARD IN IMS: ' CMS-CARD-NUMBER
                   PERFORM 5110-UPDATE-EXISTING-CARD THRU 5110-EXIT
               WHEN OTHER
                   DISPLAY 'IMS INSERT ERROR: ' WS-IMS-STATUS
                   MOVE 'E038' TO LS-ERROR-CODE
                   ADD 1 TO WS-ERROR-COUNT
           END-EVALUATE.
       5100-EXIT.
           EXIT.

       5110-UPDATE-EXISTING-CARD.
           CALL 'CBLTDLI' USING FC-REPL
                                GSAM-FIXED05-PCB-MASK
                                CARD-IMS-SEGMENT

           MOVE CARD-IMS-SEGMENT(1:2) TO WS-IMS-STATUS

           IF IMS-STATUS-OK
               DISPLAY 'IMS CARD MASTER UPDATED: ' CMS-CARD-NUMBER
           ELSE
               DISPLAY 'IMS UPDATE FAILED: ' WS-IMS-STATUS
               MOVE 'E039' TO LS-ERROR-CODE
               ADD 1 TO WS-ERROR-COUNT
           END-IF.
       5110-EXIT.
           EXIT.

      *================================================================
      * IMS CARD HISTORY INSERT
      *================================================================
       5200-INSERT-CARD-HISTORY.
           MOVE CMS-CARD-NUMBER TO CHS-CARD-NUMBER
           MOVE 'EM' TO CHS-ACTION-CODE
           MOVE WS-CURRENT-DATE TO CHS-ACTION-DATE
           MOVE WS-CURRENT-TIME TO CHS-ACTION-TIME
           MOVE 'CCPBS022' TO CHS-USER-ID
           MOVE 'EMBOSS' TO CHS-REASON-CODE

           MOVE CARD-HISTORY-SEGMENT TO HIST-IMS-SEGMENT

           CALL 'CBLTDLI' USING FC-ISRT
                                GSAM-FIXED05-PCB-MASK
                                HIST-IMS-SEGMENT

           MOVE HIST-IMS-SEGMENT(1:2) TO WS-IMS-STATUS

           IF IMS-STATUS-OK
               DISPLAY 'CARD HISTORY INSERTED: ' CHS-CARD-NUMBER
           ELSE
               DISPLAY 'HISTORY INSERT ERROR: ' WS-IMS-STATUS
               MOVE 'E040' TO LS-ERROR-CODE
               ADD 1 TO WS-ERROR-COUNT
           END-IF.
       5200-EXIT.
           EXIT.

      *================================================================
      * CLEANUP PROCESSING
      *================================================================
       9000-CLEANUP.
           PERFORM 9100-CLOSE-FILES        THRU 9100-EXIT
           PERFORM 9200-DISPLAY-TOTALS     THRU 9200-EXIT.
       9000-EXIT.
           EXIT.

       9100-CLOSE-FILES.
           CLOSE CARD-MASTER-FILE

           IF WS-CARD-FILE-OK
               DISPLAY 'CARD MASTER FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'CARD FILE CLOSE ERROR: ' WS-CARD-FILE-STATUS
           END-IF

           CLOSE EMBOSS-REQUEST-FILE

           IF WS-EMBOSS-FILE-OK
               DISPLAY 'EMBOSS REQUEST FILE CLOSED SUCCESSFULLY'
           ELSE
               DISPLAY 'EMBOSS FILE CLOSE ERROR: ' WS-EMBOSS-FILE-STATUS
           END-IF.
       9100-EXIT.
           EXIT.

       9200-DISPLAY-TOTALS.
           DISPLAY '================================================'
           DISPLAY 'CCPBS022 PROCESSING TOTALS:'
           DISPLAY 'CARDS READ       : ' WS-CARD-READ-COUNT
           DISPLAY 'EMBOSS REQUESTS  : ' WS-EMBOSS-WRITE-COUNT
           DISPLAY 'IMS INSERTS      : ' WS-IMS-INSERT-COUNT
           DISPLAY 'ERRORS ENCOUNTERED: ' WS-ERROR-COUNT
           DISPLAY '================================================'.
       9200-EXIT.
           EXIT.

      *================================================================
      * ERROR HANDLING AND ABEND ROUTINE
      *================================================================
       9999-ABEND-ROUTINE.
           DISPLAY '************************************************'
           DISPLAY 'CCPBS022 ABNORMAL TERMINATION'
           DISPLAY 'ERROR CODE: ' LS-ERROR-CODE
           DISPLAY 'RETURN CODE: ' WS-RETURN-CODE
           DISPLAY '************************************************'

           PERFORM 9100-CLOSE-FILES THRU 9100-EXIT

           MOVE WS-RETURN-CODE TO RETURN-CODE
           GOBACK.
       9999-EXIT.
           EXIT.
