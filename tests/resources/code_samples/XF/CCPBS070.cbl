      *================================================================
      * PROGRAM CCPBS070.CBL - BUSINESS SERVICE MODULE 070
      * Called by: CPBD888 and related programs
      * Purpose: Security and encryption services
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. CCPBS070.
       
       DATA DIVISION.
       WORKING-STORAGE SECTION.
       01  WS-PROGRAM-NAME             PIC X(08) VALUE 'CCPBS070'.
       
       LINKAGE SECTION.
       01  LS-SECURITY-DATA.
           05  LS-DATA-TO-ENCRYPT      PIC X(100).
           05  LS-ENCRYPTED-DATA       PIC X(128).
           05  LS-ENCRYPTION-KEY       PIC X(32).
           05  LS-SECURITY-FLAG        PIC X(01).
           05  LS-ERROR-CODE           PIC X(04).
       
       PROCEDURE DIVISION USING LS-SECURITY-DATA.
       
       0000-MAIN-PROCESSING.
           DISPLAY 'CCPBS070: SECURITY PROCESSING STARTED'
           
           IF LS-DATA-TO-ENCRYPT = SPACES
               MOVE 'E004' TO LS-ERROR-CODE
               MOVE 'N' TO LS-SECURITY-FLAG
               MOVE 8 TO RETURN-CODE
           ELSE
               MOVE LS-DATA-TO-ENCRYPT TO LS-ENCRYPTED-DATA
               MOVE 'Y' TO LS-SECURITY-FLAG
               MOVE SPACES TO LS-ERROR-CODE
               MOVE 0 TO RETURN-CODE
           END-IF
           
           DISPLAY 'CCPBS070: PROCESSING COMPLETE'
           GOBACK.
