#================================================================
# IMS SYSTEM DEFINITION - DBC MEMBER
# MEMBER: IMSDBC.CTL
# AUTHOR: D<PERSON><PERSON><PERSON>E ADMINISTRATION TEAM
# DATE: 01/15/2024
#================================================================

# Database Definitions
DATABASE NAME=EMPBENDB,ACCESS=UP,RECOVERY=(BACKOUT,<PERSON>ORWARD),
         SIZALRT=(85,90,95),ERASE=NO,
         DBRC=YES,HALDB=NO,
         TYPHALDB=PARTITION

AREA     NAME=EMPBENDB,SIZE=4096K,UOW=1000

DATASET  DD1=EMPBEN01,NAME=PAYROLL.IMS.EMPBEN01,
         SIZE=2048,BLOCK=4096,RECORD=4096,
         DEVICE=3390,UNIT=SYSDA

DATASET  DD2=EMPBEN02,NAME=PAYROLL.IMS.EMPBEN02,
         SIZE=2048,BLOCK=4096,RECORD=4096,
         DEVICE=3390,UNIT=SYSDA

DATABASE NAME=EMPTAXDB,ACCESS=UP,RECOVERY=(BACKOUT,FORWARD),
         SIZALRT=(85,90,95),ERASE=NO,
         DBRC=YES,HALDB=NO,
         TYPHALDB=PARTITION

AREA     NAME=EMPTAXDB,SIZE=8192K,UOW=2000

DATASET  DD1=EMPTAX01,NAME=PAYROLL.IMS.EMPTAX01,
         SIZE=4096,BLOCK=8192,RECORD=8192,
         DEVICE=3390,UNIT=SYSDA

DATASET  DD2=EMPTAX02,NAME=PAYROLL.IMS.EMPTAX02,
         SIZE=4096,BLOCK=8192,RECORD=8192,
         DEVICE=3390,UNIT=SYSDA

DATABASE NAME=DEPTDB,ACCESS=UP,RECOVERY=(BACKOUT,FORWARD),
         SIZALRT=(85,90,95),ERASE=NO,
         DBRC=YES,HALDB=NO,
         TYPHALDB=PARTITION

AREA     NAME=DEPTDB,SIZE=2048K,UOW=500

DATASET  DD1=DEPT01,NAME=PAYROLL.IMS.DEPT01,
         SIZE=1024,BLOCK=4096,RECORD=4096,
         DEVICE=3390,UNIT=SYSDA

# Transaction Definitions
TRANSACT CODE=SLRY,MODE=SNGL,
         MSGTYPE=(SNGLSEG,NONRESPONSE),
         CLASS=1,PRTY=3,
         PARLIM=1,PLCT=30,PLCTTIME=30,
         PROGRAM=SLRYCALC,PSB=SLRYCPSB

TRANSACT CODE=CALC,MODE=SNGL,
         MSGTYPE=(SNGLSEG,RESPONSE),
         CLASS=1,PRTY=2,
         PARLIM=5,PLCT=60,PLCTTIME=60,
         PROGRAM=SLRYVAL,PSB=SLRYCPSB

# Program Definitions
APPLCTN PSB=SLRYCPSB,PGMTYPE=TP,
        RESIDENT=NO,SCHDTYPE=PARALLEL,
        TRANSTAT=N,LOCK=NO,
        SEGNO=999,SEGSIZE=32K

APPLCTN PSB=SLRYVAL,PGMTYPE=TP,
        RESIDENT=NO,SCHDTYPE=PARALLEL,
        TRANSTAT=N,LOCK=NO,
        SEGNO=999,SEGSIZE=16K

# Terminal Definitions
TERMINAL NAME=T3270001,TYPE=3270,
         BUFSIZE=2048,COMPT=(SNA,VTAM),
         EDIT=(UC,SB),OUTBUF=4

TERMINAL NAME=T3270002,TYPE=3270,
         BUFSIZE=2048,COMPT=(SNA,VTAM),
         EDIT=(UC,SB),OUTBUF=4

# LTERM Definitions
LTERM    NAME=L3270001,TYPE=3270,
         TERMINAL=T3270001

LTERM    NAME=L3270002,TYPE=3270,
         TERMINAL=T3270002

# Node Definitions
NODE     NAME=PAYNODE1,TYPE=NTO,
         TERMINAL=(T3270001,T3270002)

# Control Region Options
OPTIONS  DBBF=(100,2000,E),
         FORMAT=(MFS,NOLPAGE),
         IMSID=IMS1,
         JBUF=30,
         MAXCLAS=255,
         MAXPST=255,
         MAXREGN=255,
         PREINIT=(1,1,N),
         PROG=(NN,NN),
         SPASZ=(512,16384),
         SPCLS=(001,010),
         USEROUT=30