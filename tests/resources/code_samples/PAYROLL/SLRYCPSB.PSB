*================================================================
* PSB - PROGRAM SPECIFICATION BLOCK FOR SLRYCALC
* MEMBER: SLRYCPSB.PSB
* AUTHOR: D<PERSON><PERSON><PERSON>E ADMINISTRATION TEAM
* DATE: 01/15/2024
*================================================================
PSB    TYPE=PSBGEN,LANG=COBOL,PSBNAME=SLRYCPSB
*
* PCB FOR EMPLOYEE BENEFITS DATABASE
PCB    TYPE=DB,DBDNAME=EMPBENDB,PROCOPT=G,KEYLEN=10
SENSEG NAME=EMPBEN,PARENT=0,PROCOPT=G
*
* PCB FOR EMPLOYEE TAX DATABASE
PCB    TYPE=DB,DBDNAME=EMPTAXDB,PROCOPT=G,KEYLEN=12
SENSEG NAME=EMPTAX,PARENT=0,PROCOPT=G
SENSEG NAME=FEDTAX,PARENT=EMPTAX,PROCOPT=G
SENSEG NAME=STTAX,PARENT=EMPTAX,PROCOPT=G
*
* PCB FOR DEPARTMENT DATABASE
PCB    TYPE=DB,DBDNAME=DEPTDB,PROCOPT=G,KEYLEN=3
SENSEG NAME=DEPT,PARENT=0,PROCOPT=G
SENSEG NAME=DEPTBUDG,PARENT=DEPT,PROCOPT=G
*
PSBGEN
END