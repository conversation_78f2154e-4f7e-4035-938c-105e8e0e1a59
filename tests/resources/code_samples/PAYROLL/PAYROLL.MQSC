* ================================================================
* ACME CORPORATION PAYROLL SYSTEM
* MQ SERIES SETUP SCRIPT
* MEMBER: PAYROLL.MQSC
* AUTHOR: MIDDLEWARE TEAM
* DATE: 01/15/2024
* ================================================================

* ----------------------------------------------------------------
* LOCAL QUEUE DEFINITIONS
* ----------------------------------------------------------------

* Main Payroll Processing Queue
DEFINE QLOCAL('PAYROLL.QUEUE') +
       DESCR('MAIN PAYROLL PROCESSING QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(YES) +
       MAXDEPTH(5000) +
       MAXMSGL(4194304) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       TRIGGER +
       TRIGTYPE(FIRST) +
       TRIGDATA('PAYROLL.PROCESS') +
       PROCESS('PAYROLL.TRIGGER.PROCESS') +
       INITQ('PAYROLL.INITIATION.QUEUE') +
       REPLACE

* Error Queue for Failed Messages
DEFINE QLOCAL('PAYROLL.ERROR.QUEUE') +
       DESCR('PAYROLL ERROR QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(YES) +
       MAXDEPTH(1000) +
       MAXMSGL(4194304) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       NOTRIGGER +
       REPLACE

* Dead Letter Queue
DEFINE QLOCAL('PAYROLL.DEAD.LETTER.QUEUE') +
       DESCR('PAYROLL DEAD LETTER QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(YES) +
       MAXDEPTH(10000) +
       MAXMSGL(4194304) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       NOTRIGGER +
       REPLACE

* Salary Calculation Request Queue
DEFINE QLOCAL('PAYROLL.SALARY.REQUEST') +
       DESCR('SALARY CALCULATION REQUEST QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(YES) +
       MAXDEPTH(2000) +
       MAXMSGL(1048576) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       TRIGGER +
       TRIGTYPE(EVERY) +
       TRIGDATA('SALARY.CALC') +
       PROCESS('SALARY.TRIGGER.PROCESS') +
       INITQ('PAYROLL.INITIATION.QUEUE') +
       REPLACE

* Salary Calculation Response Queue
DEFINE QLOCAL('PAYROLL.SALARY.RESPONSE') +
       DESCR('SALARY CALCULATION RESPONSE QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(YES) +
       MAXDEPTH(2000) +
       MAXMSGL(1048576) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       NOTRIGGER +
       REPLACE

* Audit Trail Queue
DEFINE QLOCAL('PAYROLL.AUDIT.QUEUE') +
       DESCR('PAYROLL AUDIT TRAIL QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(YES) +
       MAXDEPTH(10000) +
       MAXMSGL(2097152) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       NOTRIGGER +
       REPLACE

* Initiation Queue for Triggers
DEFINE QLOCAL('PAYROLL.INITIATION.QUEUE') +
       DESCR('PAYROLL TRIGGER INITIATION QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(NO) +
       MAXDEPTH(100) +
       MAXMSGL(1024) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       NOTRIGGER +
       REPLACE

* ----------------------------------------------------------------
* PROCESS DEFINITIONS FOR TRIGGERS
* ----------------------------------------------------------------

DEFINE PROCESS('PAYROLL.TRIGGER.PROCESS') +
       DESCR('MAIN PAYROLL TRIGGER PROCESS') +
       APPLTYPE(CICS) +
       APPLICID('SLRY') +
       ENVDATA('PAYROLL=YES,DEBUG=NO') +
       USERDATA('PAYROLL.PROCESS.DATA') +
       REPLACE

DEFINE PROCESS('SALARY.TRIGGER.PROCESS') +
       DESCR('SALARY CALCULATION TRIGGER PROCESS') +
       APPLTYPE(CICS) +
       APPLICID('CALC') +
       ENVDATA('SALARY=YES,REALTIME=YES') +
       USERDATA('SALARY.CALC.DATA') +
       REPLACE

* ----------------------------------------------------------------
* TRANSMISSION QUEUE FOR REMOTE PROCESSING
* ----------------------------------------------------------------
DEFINE QLOCAL('PAYROLL.XMIT.QUEUE') +
       DESCR('PAYROLL TRANSMISSION QUEUE') +
       PUT(ENABLED) +
       GET(ENABLED) +
       DEFPSIST(YES) +
       MAXDEPTH(1000) +
       MAXMSGL(4194304) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(XMITQ) +
       TRIGGER +
       TRIGTYPE(FIRST) +
       TRIGDATA('XMIT.TRIGGER') +
       PROCESS('XMIT.TRIGGER.PROCESS') +
       INITQ('PAYROLL.INITIATION.QUEUE') +
       REPLACE

* ----------------------------------------------------------------
* CHANNEL DEFINITIONS
* ----------------------------------------------------------------

* Server Connection Channel
DEFINE CHANNEL('PAYROLL.SVRCONN') +
       CHLTYPE(SVRCONN) +
       TRPTYPE(TCP) +
       DESCR('PAYROLL SERVER CONNECTION CHANNEL') +
       MCAUSER('PAYROLL1') +
       MAXMSGL(4194304) +
       MAXINST(100) +
       MAXINSTC(10) +
       SHARECNV(10) +
       HBINT(300) +
       KAINT(AUTO) +
       REPLACE

* Sender Channel for Remote Queue Manager
DEFINE CHANNEL('PAYROLL.TO.REMOTE') +
       CHLTYPE(SDR) +
       TRPTYPE(TCP) +
       CONNAME('REMOTE.HOST.COM(1414)') +
       XMITQ('PAYROLL.XMIT.QUEUE') +
       DESCR('PAYROLL SENDER CHANNEL TO REMOTE') +
       MCAUSER('PAYROLL1') +
       MAXMSGL(4194304) +
       HBINT(300) +
       KAINT(AUTO) +
       BATCHSZ(50) +
       DISCINT(0) +
       SHORTRTY(10) +
       SHORTTMR(60) +
       LONGRTY(999999999) +
       LONGTMR(1200) +
       REPLACE

* Receiver Channel from Remote Queue Manager
DEFINE CHANNEL('PAYROLL.FROM.REMOTE') +
       CHLTYPE(RCVR) +
       TRPTYPE(TCP) +
       DESCR('PAYROLL RECEIVER CHANNEL FROM REMOTE') +
       MCAUSER('PAYROLL1') +
       MAXMSGL(4194304) +
       PUTAUT(CTX) +
       REPLACE

* ----------------------------------------------------------------
* REMOTE QUEUE DEFINITIONS
* ----------------------------------------------------------------
DEFINE QREMOTE('REMOTE.PAYROLL.QUEUE') +
       DESCR('REMOTE PAYROLL PROCESSING QUEUE') +
       PUT(ENABLED) +
       DEFPSIST(YES) +
       RNAME('PAYROLL.QUEUE') +
       RQMNAME('REMOTE.QM') +
       XMITQ('PAYROLL.XMIT.QUEUE') +
       REPLACE

* ----------------------------------------------------------------
* ALIAS QUEUE DEFINITIONS
* ----------------------------------------------------------------
DEFINE QALIAS('PAYROLL.INPUT') +
       DESCR('PAYROLL INPUT ALIAS QUEUE') +
       TARGET('PAYROLL.QUEUE') +
       DEFPSIST(YES) +
       PUT(ENABLED) +
       GET(ENABLED) +
       REPLACE

DEFINE QALIAS('PAYROLL.OUTPUT') +
       DESCR('PAYROLL OUTPUT ALIAS QUEUE') +
       TARGET('PAYROLL.SALARY.RESPONSE') +
       DEFPSIST(YES) +
       PUT(ENABLED) +
       GET(ENABLED) +
       REPLACE

* ----------------------------------------------------------------
* MODEL QUEUE DEFINITIONS
* ----------------------------------------------------------------
DEFINE QMODEL('PAYROLL.TEMP.MODEL') +
       DESCR('PAYROLL TEMPORARY QUEUE MODEL') +
       DEFTYPE(TEMPDYN) +
       DEFPSIST(NO) +
       MAXDEPTH(100) +
       MAXMSGL(1048576) +
       DEFSOPT(SHARED) +
       NOHARDENBO +
       USAGE(NORMAL) +
       NOTRIGGER +
       REPLACE

* ----------------------------------------------------------------
* LISTENER DEFINITIONS
* ----------------------------------------------------------------
DEFINE LISTENER('PAYROLL.TCP.LISTENER') +
       TRPTYPE(TCP) +
       PORT(1414) +
       IPADDR('0.0.0.0') +
       BACKLOG(50) +
       CONTROL(QMGR) +
       DESCR('PAYROLL TCP LISTENER') +
       REPLACE