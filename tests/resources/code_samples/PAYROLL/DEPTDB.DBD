*================================================================
* DBD - DATABASE DESCRIPTION FOR DEPARTMENT DATA
* MEMBER: DEPTDB.DBD
* AUTHOR: <PERSON><PERSON><PERSON><PERSON>E ADMINISTRATION TEAM
* DATE: 01/15/2024
*================================================================
DBD    NAME=DEPTDB,ACCESS=HIDAM
DATASET DD1=DEPT01,DEVICE=3390,SIZE=4096
*
* ROOT SEGMENT - DEPARTMENT MASTER
SEGM   NAME=DEPT,PARENT=0,BYTES=80,
       POINTER=TB,RULES=(LLL,DDD)
FIELD  NAME=(DEPTKEY,SEQ,U),BYTES=3,START=1,TYPE=C
FIELD  NAME=DEPTCODE,BYTES=3,START=1,TYPE=C
FIELD  NAME=DEPTNAME,BYTES=30,START=4,TYPE=C
FIELD  NAME=MANAGER,BYTES=10,START=34,TYPE=C
FIELD  NAME=LOCATION,BYTES=20,START=44,TYPE=C
FIELD  NAME=COSTCENTER,BYTES=6,START=64,TYPE=C
FIELD  NAME=STATUS,BYTES=1,START=70,TYPE=C
FIELD  NAME=CREATEDATE,BYTES=8,START=71,TYPE=C
FIELD  NAME=FILLER,BYTES=2,START=79,TYPE=C
*
* CHILD SEGMENT - DEPARTMENT BUDGET
SEGM   NAME=DEPTBUDG,PARENT=DEPT,BYTES=32,
       POINTER=TB,RULES=(HERE,LAST)
FIELD  NAME=(BUDGKEY,SEQ,U),BYTES=7,START=1,TYPE=C
FIELD  NAME=DEPTCODE,BYTES=3,START=1,TYPE=C
FIELD  NAME=FISCALYEAR,BYTES=4,START=4,TYPE=P
FIELD  NAME=BUDGETAMT,BYTES=8,START=8,TYPE=P
FIELD  NAME=ACTUALAMT,BYTES=8,START=16,TYPE=P
FIELD  NAME=VARIANCE,BYTES=8,START=24,TYPE=P
*
DBDGEN
FINISH
END