-- ================================================================
-- ACME CORPORATION PAYROLL SYSTEM
-- DB2 DATABASE SETUP AND CONFIGURATION
-- AUTHOR: <PERSON>AT<PERSON><PERSON><PERSON> ADMINISTRATION TEAM
-- DATE: 01/15/2024
-- ================================================================

-- ----------------------------------------------------------------
-- CREATE PAYROLL DATABASE
-- ----------------------------------------------------------------
CREATE DATABASE PAYROLLDB
    BUFFERPOOL BP0
    INDEXBP BP0
    CCSID EBCDIC;

-- ----------------------------------------------------------------
-- CREATE TABLESPACE FOR PAYROLL TABLES
-- ----------------------------------------------------------------
CREATE TABLESPACE PAYROLLTS
    IN PAYROLLDB
    USING STOGROUP SYSDEFLT
    PRIQTY 5000
    SECQTY 1000
    ERASE NO
    FREEPAGE 10
    PCTFREE 15
    GBPCACHE CHANGED
    TRACKMOD YES
    COMPRESS YES;

-- ----------------------------------------------------------------
-- CREATE SALARY STATISTICS TABLE
-- ----------------------------------------------------------------
CREATE TABLE PAYROLL.SALARY_STATS (
    DEPT_CODE       CHAR(3) NOT NULL,
    DEPT_NAME       VARCHAR(30) NOT NULL,
    AVG_SALARY      DECIMAL(9,2),
    MIN_SALARY      DECIMAL(9,2),
    MAX_SALARY      DECIMAL(9,2),
    EMP_COUNT       INTEGER,
    LAST_UPDATED    TIMESTAMP,
    CREATED_BY      CHAR(8) DEFAULT USER,
    PRIMARY KEY (DEPT_CODE)
    ) IN PAYROLLDB.PAYROLLTS;

-- ----------------------------------------------------------------
-- CREATE EMPLOYEE SALARY HISTORY TABLE
-- ----------------------------------------------------------------
CREATE TABLE PAYROLL.SALARY_HISTORY (
    EMP_ID          CHAR(10) NOT NULL,
    EFFECTIVE_DATE  DATE NOT NULL,
    SALARY_AMOUNT   DECIMAL(9,2) NOT NULL,
    SALARY_TYPE     CHAR(1) NOT NULL CHECK (SALARY_TYPE IN ('B','G','N')),
    CHANGE_REASON   VARCHAR(50),
    APPROVED_BY     CHAR(8),
    CREATED_DATE    TIMESTAMP DEFAULT CURRENT TIMESTAMP,
    PRIMARY KEY (EMP_ID, EFFECTIVE_DATE)
    ) IN PAYROLLDB.PAYROLLTS;

-- ----------------------------------------------------------------
-- CREATE TAX WITHHOLDING TABLE
-- ----------------------------------------------------------------
CREATE TABLE PAYROLL.TAX_WITHHOLDING (
    EMP_ID          CHAR(10) NOT NULL,
    TAX_YEAR        SMALLINT NOT NULL,
    FEDERAL_TAX     DECIMAL(8,2) DEFAULT 0,
    STATE_TAX       DECIMAL(8,2) DEFAULT 0,
    LOCAL_TAX       DECIMAL(8,2) DEFAULT 0,
    FICA_TAX        DECIMAL(8,2) DEFAULT 0,
    MEDICARE_TAX    DECIMAL(8,2) DEFAULT 0,
    LAST_UPDATED    TIMESTAMP DEFAULT CURRENT TIMESTAMP,
    PRIMARY KEY (EMP_ID, TAX_YEAR)
    ) IN PAYROLLDB.PAYROLLTS;

-- ----------------------------------------------------------------
-- CREATE BENEFIT DEDUCTIONS TABLE
-- ----------------------------------------------------------------
CREATE TABLE PAYROLL.BENEFIT_DEDUCTIONS (
    EMP_ID          CHAR(10) NOT NULL,
    BENEFIT_CODE    CHAR(4) NOT NULL,
    DEDUCTION_AMT   DECIMAL(7,2) NOT NULL,
    EFFECTIVE_DATE  DATE NOT NULL,
    END_DATE        DATE,
    STATUS          CHAR(1) DEFAULT 'A' CHECK (STATUS IN ('A','I','T')),
    PRIMARY KEY (EMP_ID, BENEFIT_CODE, EFFECTIVE_DATE)
    ) IN PAYROLLDB.PAYROLLTS;

-- ----------------------------------------------------------------
-- CREATE AUDIT TRAIL TABLE
-- ----------------------------------------------------------------
CREATE TABLE PAYROLL.AUDIT_TRAIL (
    AUDIT_ID        INTEGER NOT NULL GENERATED ALWAYS AS IDENTITY,
    TABLE_NAME      VARCHAR(30) NOT NULL,
    OPERATION_TYPE  CHAR(1) NOT NULL CHECK (OPERATION_TYPE IN ('I','U','D')),
    EMP_ID          CHAR(10),
    OLD_VALUES      VARCHAR(500),
    NEW_VALUES      VARCHAR(500),
    CHANGED_BY      CHAR(8) DEFAULT USER,
    CHANGED_DATE    TIMESTAMP DEFAULT CURRENT TIMESTAMP,
    PRIMARY KEY (AUDIT_ID)
    ) IN PAYROLLDB.PAYROLLTS;

-- ----------------------------------------------------------------
-- CREATE INDEXES FOR PERFORMANCE
-- ----------------------------------------------------------------
CREATE INDEX PAYROLL.IX_SALARY_STATS_DEPT
    ON PAYROLL.SALARY_STATS (DEPT_CODE);

CREATE INDEX PAYROLL.IX_SALARY_HIST_EMP
    ON PAYROLL.SALARY_HISTORY (EMP_ID);

CREATE INDEX PAYROLL.IX_SALARY_HIST_DATE
    ON PAYROLL.SALARY_HISTORY (EFFECTIVE_DATE);

CREATE INDEX PAYROLL.IX_TAX_WITH_YEAR
    ON PAYROLL.TAX_WITHHOLDING (TAX_YEAR, EMP_ID);

CREATE INDEX PAYROLL.IX_BENEFIT_STATUS
    ON PAYROLL.BENEFIT_DEDUCTIONS (STATUS, EMP_ID);

CREATE INDEX PAYROLL.IX_AUDIT_DATE
    ON PAYROLL.AUDIT_TRAIL (CHANGED_DATE);

-- ----------------------------------------------------------------
-- INSERT SAMPLE DATA INTO SALARY_STATS
-- ----------------------------------------------------------------
INSERT INTO PAYROLL.SALARY_STATS VALUES
                                     ('IT ', 'INFORMATION TECHNOLOGY', 85000.00, 45000.00, 150000.00, 25, CURRENT TIMESTAMP, 'SETUP'),
                                     ('HR ', 'HUMAN RESOURCES', 65000.00, 40000.00, 95000.00, 15, CURRENT TIMESTAMP, 'SETUP'),
                                     ('FIN', 'FINANCE', 75000.00, 50000.00, 120000.00, 20, CURRENT TIMESTAMP, 'SETUP'),
                                     ('MKT', 'MARKETING', 62000.00, 35000.00, 85000.00, 12, CURRENT TIMESTAMP, 'SETUP'),
                                     ('SAL', 'SALES', 58000.00, 30000.00, 95000.00, 18, CURRENT TIMESTAMP, 'SETUP'),
                                     ('OPS', 'OPERATIONS', 68000.00, 42000.00, 98000.00, 16, CURRENT TIMESTAMP, 'SETUP'),
                                     ('LEG', 'LEGAL', 72000.00, 55000.00, 105000.00, 8, CURRENT TIMESTAMP, 'SETUP'),
                                     ('ADM', 'ADMINISTRATION', 55000.00, 35000.00, 75000.00, 10, CURRENT TIMESTAMP, 'SETUP'),
                                     ('SEC', 'SECURITY', 69000.00, 45000.00, 92000.00, 12, CURRENT TIMESTAMP, 'SETUP');

-- ----------------------------------------------------------------
-- INSERT SAMPLE SALARY HISTORY DATA
-- ----------------------------------------------------------------
INSERT INTO PAYROLL.SALARY_HISTORY VALUES
                                       ('EMP0001001', '2024-01-15', 75000.00, 'B', 'ANNUAL REVIEW', 'MANAGER1', CURRENT TIMESTAMP),
                                       ('EMP0001002', '2023-03-10', 65000.00, 'B', 'PROMOTION', 'MANAGER2', CURRENT TIMESTAMP),
                                       ('EMP0001003', '2022-05-05', 85000.00, 'B', 'MARKET ADJUSTMENT', 'MANAGER3', CURRENT TIMESTAMP),
                                       ('EMP0001004', '2024-02-01', 68000.00, 'B', 'ANNUAL REVIEW', 'MANAGER1', CURRENT TIMESTAMP),
                                       ('EMP0001005', '2023-08-15', 72000.00, 'B', 'PROMOTION', 'MANAGER2', CURRENT TIMESTAMP);

-- ----------------------------------------------------------------
-- INSERT SAMPLE BENEFIT DEDUCTIONS
-- ----------------------------------------------------------------
INSERT INTO PAYROLL.BENEFIT_DEDUCTIONS VALUES
                                           ('EMP0001001', 'HLTH', 125.50, '2024-01-01', NULL, 'A'),
                                           ('EMP0001001', 'DENT', 45.75, '2024-01-01', NULL, 'A'),
                                           ('EMP0001001', 'RETIR', 375.00, '2024-01-01', NULL, 'A'),
                                           ('EMP0001002', 'HLTH', 125.50, '2024-01-01', NULL, 'A'),
                                           ('EMP0001002', 'RETIR', 325.00, '2024-01-01', NULL, 'A'),
                                           ('EMP0001003', 'HLTH', 185.25, '2024-01-01', NULL, 'A'),
                                           ('EMP0001003', 'DENT', 45.75, '2024-01-01', NULL, 'A'),
                                           ('EMP0001003', 'LIFE', 25.00, '2024-01-01', NULL, 'A'),
                                           ('EMP0001003', 'RETIR', 425.00, '2024-01-01', NULL, 'A');

-- ----------------------------------------------------------------
-- CREATE VIEWS FOR REPORTING
-- ----------------------------------------------------------------
CREATE VIEW PAYROLL.V_EMPLOYEE_SUMMARY AS
    SELECT
        SH.EMP_ID,
        SH.SALARY_AMOUNT AS CURRENT_SALARY,
        SS.DEPT_NAME,
        SS.AVG_SALARY AS DEPT_AVG_SALARY,
        CASE
            WHEN SH.SALARY_AMOUNT > SS.AVG_SALARY THEN 'ABOVE_AVERAGE'
            WHEN SH.SALARY_AMOUNT < SS.AVG_SALARY THEN 'BELOW_AVERAGE'
            ELSE 'AT_AVERAGE'
            END AS SALARY_POSITION
    FROM PAYROLL.SALARY_HISTORY SH
             JOIN PAYROLL.SALARY_STATS SS ON SUBSTR(SH.EMP_ID,4,3) = SS.DEPT_CODE
    WHERE SH.EFFECTIVE_DATE = (
        SELECT MAX(EFFECTIVE_DATE)
        FROM PAYROLL.SALARY_HISTORY SH2
        WHERE SH2.EMP_ID = SH.EMP_ID
    );

-- ----------------------------------------------------------------
-- GRANT PERMISSIONS
-- ----------------------------------------------------------------
GRANT SELECT, INSERT, UPDATE, DELETE ON PAYROLL.SALARY_STATS TO PAYROLL1;
GRANT SELECT, INSERT, UPDATE, DELETE ON PAYROLL.SALARY_HISTORY TO PAYROLL1;
GRANT SELECT, INSERT, UPDATE, DELETE ON PAYROLL.TAX_WITHHOLDING TO PAYROLL1;
GRANT SELECT, INSERT, UPDATE, DELETE ON PAYROLL.BENEFIT_DEDUCTIONS TO PAYROLL1;
GRANT SELECT, INSERT ON PAYROLL.AUDIT_TRAIL TO PAYROLL1;
GRANT SELECT ON PAYROLL.V_EMPLOYEE_SUMMARY TO PAYROLL1;

GRANT SELECT ON PAYROLL.SALARY_STATS TO PUBLIC;
GRANT SELECT ON PAYROLL.V_EMPLOYEE_SUMMARY TO PUBLIC;

-- ----------------------------------------------------------------
-- CREATE STORED PROCEDURES
-- ----------------------------------------------------------------
CREATE PROCEDURE PAYROLL.UPDATE_SALARY_STATS()
    LANGUAGE SQL
BEGIN
    -- Update department statistics based on current salary data
UPDATE PAYROLL.SALARY_STATS SET
                                AVG_SALARY = (
                                    SELECT AVG(SALARY_AMOUNT)
                                    FROM PAYROLL.SALARY_HISTORY SH
                                    WHERE SUBSTR(SH.EMP_ID,4,3) = SALARY_STATS.DEPT_CODE
                                      AND SH.EFFECTIVE_DATE = (
                                        SELECT MAX(EFFECTIVE_DATE)
                                        FROM PAYROLL.SALARY_HISTORY SH2
                                        WHERE SH2.EMP_ID = SH.EMP_ID
                                    )
                                ),
                                MIN_SALARY = (
                                    SELECT MIN(SALARY_AMOUNT)
                                    FROM PAYROLL.SALARY_HISTORY SH
                                    WHERE SUBSTR(SH.EMP_ID,4,3) = SALARY_STATS.DEPT_CODE
                                      AND SH.EFFECTIVE_DATE = (
                                        SELECT MAX(EFFECTIVE_DATE)
                                        FROM PAYROLL.SALARY_HISTORY SH2
                                        WHERE SH2.EMP_ID = SH.EMP_ID
                                    )
                                ),
                                MAX_SALARY = (
                                    SELECT MAX(SALARY_AMOUNT)
                                    FROM PAYROLL.SALARY_HISTORY SH
                                    WHERE SUBSTR(SH.EMP_ID,4,3) = SALARY_STATS.DEPT_CODE
                                      AND SH.EFFECTIVE_DATE = (
                                        SELECT MAX(EFFECTIVE_DATE)
                                        FROM PAYROLL.SALARY_HISTORY SH2
                                        WHERE SH2.EMP_ID = SH.EMP_ID
                                    )
                                ),
                                EMP_COUNT = (
                                    SELECT COUNT(DISTINCT EMP_ID)
                                    FROM PAYROLL.SALARY_HISTORY SH
                                    WHERE SUBSTR(SH.EMP_ID,4,3) = SALARY_STATS.DEPT_CODE
                                ),
                                LAST_UPDATED = CURRENT TIMESTAMP;
END;

-- ----------------------------------------------------------------
-- BIND PLANS FOR COBOL PROGRAMS
-- ----------------------------------------------------------------
-- Note: These would typically be run from TSO or batch
/*
BIND PLAN(SLRYCALC) -
     MEMBER(SLRYCALC) -
     LIBRARY('PAYROLL.DBRMLIB') -
     ACTION(REPLACE) -
     RETAIN -
     ISOLATION(CS) -
     RELEASE(COMMIT) -
     VALIDATE(BIND) -
     EXPLAIN(YES) -
     FLAG(I) -
     DEGREE(1) -
     REOPT(VARS) -
     CURRENTDATA(YES) -
     DEFER(PREPARE) -
     IMMEDWRITE(NO) -
     SQLRULES(DB2) -
     DISCONNECT(EXPLICIT) -
     ACQUIRE(USE) -
     CACHESIZE(1024);

BIND PACKAGE(PAYROLL.SLRYPARM) -
     MEMBER(SLRYPARM) -
     LIBRARY('PAYROLL.DBRMLIB') -
     ACTION(REPLACE) -
     ISOLATION(CS) -
     RELEASE(COMMIT) -
     VALIDATE(BIND);
*/

-- ----------------------------------------------------------------
-- CREATE TRIGGERS FOR AUDIT TRAIL
-- ----------------------------------------------------------------
CREATE TRIGGER PAYROLL.TR_SALARY_AUDIT
    AFTER UPDATE ON PAYROLL.SALARY_HISTORY
    REFERENCING OLD AS O NEW AS N
    FOR EACH ROW
    INSERT INTO PAYROLL.AUDIT_TRAIL
        (TABLE_NAME, OPERATION_TYPE, EMP_ID, OLD_VALUES, NEW_VALUES)
    VALUES
        ('SALARY_HISTORY', 'U', N.EMP_ID,
         'SALARY:' || O.SALARY_AMOUNT || ',DATE:' || O.EFFECTIVE_DATE,
         'SALARY:' || N.SALARY_AMOUNT || ',DATE:' || N.EFFECTIVE_DATE);

-- ----------------------------------------------------------------
-- COMMIT ALL CHANGES
-- ----------------------------------------------------------------
COMMIT;