*================================================================
      * SUBPROGRAM SLRYPARM.CBL - PARAMETER READER
      * Called by: SLRYCALC main program
      * Purpose: Read and validate system parameters
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. SLRYPARM.
       AUTHOR. PAYROLL-SYSTEM.
       DATE-WRITTEN. 01/15/2024.

       ENVIRONMENT DIVISION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT PARM-FILE ASSIGN TO PARMLIB
               ORGANIZATION IS SEQUENTIAL
               ACCESS MODE IS SEQUENTIAL
               FILE STATUS IS WS-PARM-STATUS.

       DATA DIVISION.
       FILE SECTION.
       FD  PARM-FILE
           RECORDING MODE IS F
           LABEL RECORDS ARE STANDARD
           RECORD CONTAINS 80 CHARACTERS
           DATA RECORD IS PARM-RECORD.

       01  PARM-RECORD.
           05  PARM-TYPE               PIC X(08).
           05  PARM-VALUE              PIC X(20).
           05  PARM-DESCRIPTION        PIC X(40).
           05  FILLER                  PIC X(12).

       WORKING-STORAGE SECTION.
       01  WS-PARM-STATUS              PIC XX.
       01  WS-EOF-FLAG                 PIC X VALUE 'N'.
           88  END-OF-PARMS            VALUE 'Y'.

       01  WS-PARAMETER-VALUES.
           05  WS-MIN-SALARY           PIC 9(07)V99 VALUE 20000.00.
           05  WS-MAX-SALARY           PIC 9(07)V99 VALUE 500000.00.
           05  WS-BONUS-FACTOR         PIC V9(4) VALUE .1500.
           05  WS-OVERTIME-RATE        PIC V9(4) VALUE .1500.

      * Dead code - parameters no longer used
       01  WS-OBSOLETE-PARAMETERS.
           05  WS-OLD-CALC-METHOD      PIC X(01) VALUE 'A'.
           05  WS-OLD-ROUNDING-RULE    PIC X(01) VALUE 'R'.
           05  WS-OLD-CURRENCY-CODE    PIC X(03) VALUE 'USD'.

       LINKAGE SECTION.
       01  LS-CALC-FIELDS.
           05  LS-GROSS-SALARY         PIC 9(07)V99.
           05  LS-NET-SALARY           PIC 9(07)V99.
           05  LS-TAX-AMOUNT           PIC 9(06)V99.
           05  LS-BONUS-AMOUNT         PIC 9(06)V99.

       PROCEDURE DIVISION USING LS-CALC-FIELDS.

       0000-MAIN-SECTION SECTION.
       0000-MAIN-PARA.
           PERFORM 1000-INITIALIZE
           PERFORM 2000-READ-PARAMETERS
           PERFORM 3000-SET-DEFAULTS
           PERFORM 9000-CLEANUP
           GOBACK.

       1000-INITIALIZE SECTION.
       1000-INIT-PARA.
           DISPLAY 'SLRYPARM: READING CALCULATION PARAMETERS'
           OPEN INPUT PARM-FILE
           IF WS-PARM-STATUS NOT = '00'
               DISPLAY 'WARNING: PARAMETER FILE NOT FOUND'
               DISPLAY 'USING DEFAULT VALUES'
               MOVE 8 TO RETURN-CODE
               GO TO 3000-SET-DEFAULTS
           END-IF.

       2000-READ-PARAMETERS SECTION.
       2000-READ-PARA.
           PERFORM 2100-READ-PARM-RECORD
           PERFORM 2200-PROCESS-PARAMETER
               UNTIL END-OF-PARMS.

       2100-READ-PARM-RECORD SECTION.
       2100-READ-PARA.
           READ PARM-FILE
           AT END
               SET END-OF-PARMS TO TRUE
           NOT AT END
               CONTINUE
           END-READ.

       2200-PROCESS-PARAMETER SECTION.
       2200-PROCESS-PARA.
           EVALUATE PARM-TYPE
               WHEN 'MINSALRY'
                   MOVE PARM-VALUE TO WS-MIN-SALARY
               WHEN 'MAXSALRY'
                   MOVE PARM-VALUE TO WS-MAX-SALARY
               WHEN 'BONUSFCT'
                   MOVE PARM-VALUE TO WS-BONUS-FACTOR
               WHEN 'OVERTIME'
                   MOVE PARM-VALUE TO WS-OVERTIME-RATE
               WHEN OTHER
                   DISPLAY 'UNKNOWN PARAMETER: ' PARM-TYPE
           END-EVALUATE.

       3000-SET-DEFAULTS SECTION.
       3000-DEFAULTS-PARA.
           MOVE ZERO TO LS-GROSS-SALARY
           MOVE ZERO TO LS-NET-SALARY  
           MOVE ZERO TO LS-TAX-AMOUNT
           MOVE ZERO TO LS-BONUS-AMOUNT
           MOVE 0 TO RETURN-CODE.

       9000-CLEANUP SECTION.
       9000-CLEANUP-PARA.
           IF WS-PARM-STATUS = '00'
               CLOSE PARM-FILE
           END-IF.