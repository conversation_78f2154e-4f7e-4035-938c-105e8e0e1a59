//MQSTART  JOB (ACCT),'START MQ',CLASS=A,MSGCLASS=H,
//         MSGLEVEL=(1,1),TIME=(0,15),REGION=2M,
//         NOTIFY=&SYSUID
//*================================================================
//* ACME CORPORATION PAYROLL SYSTEM
//* MQ SERIES STARTUP JOB
//* AUTHOR: MIDDLEWARE TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//*----------------------------------------------------------------
//* STEP010: START QUEUE MANAGER
//*----------------------------------------------------------------
//STEP010  EXEC PGM=CSQYASCP,PARM='START QMGR(PAYROLL.QM)'
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//         DD DSN=MQ.V9.SCSQAUTH,DISP=SHR
//CSQINP1  DD DSN=MQ.V9.PROCLIB(CSQ4MSTR),DISP=SHR
//CSQINP2  DD DSN=PAYROLL.MQ.PARM.LIB(PAYROLL),DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSUDUMP DD SYSOUT=*
//*
//*----------------------------------------------------------------
//* STEP020: START CHANNEL INITIATOR
//*----------------------------------------------------------------
//STEP020  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM',COND=(0,NE,STEP010)
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  START CHINIT
  DISPLAY CHINIT
/*
//*
//*----------------------------------------------------------------
//* STEP030: START LISTENERS
//*----------------------------------------------------------------
//STEP030  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM',COND=(0,NE,STEP010)
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  START LISTENER(PAYROLL.TCP.LISTENER)
  DISPLAY LSSTATUS(PAYROLL.TCP.LISTENER)
/*
//*
//*----------------------------------------------------------------
//* STEP040: START CHANNELS
//*----------------------------------------------------------------
//STEP040  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM',COND=(0,NE,STEP010)
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  START CHANNEL(PAYROLL.TO.REMOTE)
  START CHANNEL(PAYROLL.FROM.REMOTE)
  DISPLAY CHSTATUS(PAYROLL.TO.REMOTE)
  DISPLAY CHSTATUS(PAYROLL.FROM.REMOTE)
/*
//*
//*----------------------------------------------------------------
//* STEP050: VERIFY MQ SETUP
//*----------------------------------------------------------------
//STEP050  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM',COND=(0,NE,STEP010)
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  PING QMGR
  DISPLAY QMGR
  DISPLAY QUEUE(PAYROLL.*) CURDEPTH
  DISPLAY CONN(*) WHERE(APPLTAG EQ PAYROLL*)
/*