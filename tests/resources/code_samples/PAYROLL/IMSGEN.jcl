//IMSGEN   JOB (ACCT),'IMS GENERATION',CLASS=A,MSGCLASS=H,
//         MSGLEVEL=(1,1),TIME=(0,20),REGION=2M,
//         NOTIFY=&SYSUID
//*================================================================
//* ACME CORPORATION PAYROLL SYSTEM
//* IMS DATABASE AND PSB GENERATION JOB
//* AUTHOR: DATABASE ADMINISTRATION TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//*----------------------------------------------------------------
//* STEP010: GENERATE EMPLOYEE BENEFITS DBD
//*----------------------------------------------------------------
//STEP010  EXEC PGM=ASMBLR,PARM='OBJ,NODECK,XREF'
//SYSLIB   DD DSN=IMS.V15.MACLIB,DISP=SHR
//         DD DSN=SYS1.MACLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT2   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT3   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSGO    DD DSN=&&EMPBENOBJ,DISP=(NEW,PASS),UNIT=SYSDA,
//         SPACE=(TRK,(5,5)),DCB=(RECFM=FB,LRECL=80,BLKSIZE=0)
//SYSIN    DD DSN=PAYROLL.IMS.DBD.LIB(EMPBENDB),DISP=SHR
//*
//STEP011  EXEC PGM=DFSRRC00,PARM='DBDGEN'
//STEPLIB  DD DSN=IMS.V15.RESLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSIN    DD DSN=&&EMPBENOBJ,DISP=(OLD,DELETE)
//IMSDALIB DD DSN=IMS.V15.DBDLIB,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP020: GENERATE EMPLOYEE TAX DBD
//*----------------------------------------------------------------
//STEP020  EXEC PGM=ASMBLR,PARM='OBJ,NODECK,XREF'
//SYSLIB   DD DSN=IMS.V15.MACLIB,DISP=SHR
//         DD DSN=SYS1.MACLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT2   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT3   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSGO    DD DSN=&&EMPTAXOBJ,DISP=(NEW,PASS),UNIT=SYSDA,
//         SPACE=(TRK,(5,5)),DCB=(RECFM=FB,LRECL=80,BLKSIZE=0)
//SYSIN    DD DSN=PAYROLL.IMS.DBD.LIB(EMPTAXDB),DISP=SHR
//*
//STEP021  EXEC PGM=DFSRRC00,PARM='DBDGEN'
//STEPLIB  DD DSN=IMS.V15.RESLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSIN    DD DSN=&&EMPTAXOBJ,DISP=(OLD,DELETE)
//IMSDALIB DD DSN=IMS.V15.DBDLIB,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP030: GENERATE DEPARTMENT DBD
//*----------------------------------------------------------------
//STEP030  EXEC PGM=ASMBLR,PARM='OBJ,NODECK,XREF'
//SYSLIB   DD DSN=IMS.V15.MACLIB,DISP=SHR
//         DD DSN=SYS1.MACLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT2   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT3   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSGO    DD DSN=&&DEPTOBJ,DISP=(NEW,PASS),UNIT=SYSDA,
//         SPACE=(TRK,(5,5)),DCB=(RECFM=FB,LRECL=80,BLKSIZE=0)
//SYSIN    DD DSN=PAYROLL.IMS.DBD.LIB(DEPTDB),DISP=SHR
//*
//STEP031  EXEC PGM=DFSRRC00,PARM='DBDGEN'
//STEPLIB  DD DSN=IMS.V15.RESLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSIN    DD DSN=&&DEPTOBJ,DISP=(OLD,DELETE)
//IMSDALIB DD DSN=IMS.V15.DBDLIB,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP040: GENERATE PSB FOR SLRYCALC
//*----------------------------------------------------------------
//STEP040  EXEC PGM=ASMBLR,PARM='OBJ,NODECK,XREF'
//SYSLIB   DD DSN=IMS.V15.MACLIB,DISP=SHR
//         DD DSN=SYS1.MACLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT2   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSUT3   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSGO    DD DSN=&&PSBOBJ,DISP=(NEW,PASS),UNIT=SYSDA,
//         SPACE=(TRK,(5,5)),DCB=(RECFM=FB,LRECL=80,BLKSIZE=0)
//SYSIN    DD DSN=PAYROLL.IMS.PSB.LIB(SLRYCPSB),DISP=SHR
//*
//STEP041  EXEC PGM=DFSRRC00,PARM='PSBGEN'
//STEPLIB  DD DSN=IMS.V15.RESLIB,DISP=SHR
//SYSUT1   DD UNIT=SYSDA,SPACE=(TRK,(10,10))
//SYSPRINT DD SYSOUT=*
//SYSIN    DD DSN=&&PSBOBJ,DISP=(OLD,DELETE)
//IMSACB   DD DSN=IMS.V15.ACBLIB,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP050: GENERATE ACCESS CONTROL BLOCKS (ACBGEN)
//*----------------------------------------------------------------
//STEP050  EXEC PGM=DFSRRC00,PARM='UPB,ACBGEN'
//STEPLIB  DD DSN=IMS.V15.RESLIB,DISP=SHR
//IMS      DD DSN=IMS.V15.PARMLIB(DFSVSAMP),DISP=SHR
//IMSACB   DD DSN=IMS.V15.ACBLIB,DISP=SHR
//IMSDALIB DD DSN=IMS.V15.DBDLIB,DISP=SHR
//IMSPSB   DD DSN=IMS.V15.PSBLIB,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSTERM  DD SYSOUT=*
//SYSUDUMP DD SYSOUT=*
//SYSIN    DD *
  BUILD PSB=SLRYCPSB
  BUILD DBD=(EMPBENDB,EMPTAXDB,DEPTDB)
/*
//*
//*----------------------------------------------------------------
//* STEP060: LIST GENERATED OBJECTS
//*----------------------------------------------------------------
//STEP060  EXEC PGM=IEHLIST
//SYSPRINT DD SYSOUT=*
//DD1      DD DSN=IMS.V15.ACBLIB,DISP=SHR
//SYSIN    DD *
  LISTPDS DSNAME=IMS.V15.ACBLIB,FORMAT,MEMBER=(SLRYCPSB,EMPBENDB,EMPTAXDB,DEPTDB)
/*