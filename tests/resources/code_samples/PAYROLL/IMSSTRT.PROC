//IMSSTRT  PROC IMSID=IMS1,PARM=DBC,IMSGRP=IMS
//*================================================================
//* IMS STARTUP CATALOGED PROCEDURE
//* MEMBER: IMSSTRT.PROC
//* AUTHOR: SYSTEMS PROGRAMMING TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//*----------------------------------------------------------------
//* IMS CONTROL REGION STARTUP
//*----------------------------------------------------------------
//STEP1    EXEC PGM=DFSRRC00,REGION=4096K,
//         PARM='&PARM,&IMSID,,,,,,,,,,,N,N,N'
//STEPLIB  DD DSN=IMS.V15.RESLIB,DISP=SHR
//         DD DSN=IMS.V15.EXITLIB,DISP=SHR
//         DD DSN=PAYROLL.LOAD.LIB,DISP=SHR
//IMS      DD DSN=IMS.V15.PARMLIB(&IMSID),DISP=SHR
//IMSDALIB DD DSN=IMS.V15.DBDLIB,DISP=SHR
//IMSACB   DD DSN=IMS.V15.ACBLIB,DISP=SHR
//PROCLIB  DD DSN=IMS.V15.PROCLIB,DISP=SHR
//         DD DSN=PAYROLL.PROC.LIB,DISP=SHR
//DFSRESLB DD DSN=IMS.V15.RESLIB,DISP=SHR
//IMSLOG   DD DSN=IMS.V15.LOG.&IMSID,DISP=(NEW,CATLG),
//         SPACE=(CYL,(100,50)),UNIT=SYSDA,
//         DCB=(RECFM=FB,LRECL=4096,BLKSIZE=0)
//DFSWADS  DD DSN=IMS.V15.WADS,DISP=SHR
//DFSVSAMP DD DSN=IMS.V15.PARMLIB(DFSVSAMP),DISP=SHR
//*
//* DATABASE DATASETS
//EMPBEN01 DD DSN=PAYROLL.IMS.EMPBEN01,DISP=SHR
//EMPBEN02 DD DSN=PAYROLL.IMS.EMPBEN02,DISP=SHR
//EMPTAX01 DD DSN=PAYROLL.IMS.EMPTAX01,DISP=SHR
//EMPTAX02 DD DSN=PAYROLL.IMS.EMPTAX02,DISP=SHR
//DEPT01   DD DSN=PAYROLL.IMS.DEPT01,DISP=SHR
//*
//SYSPRINT DD SYSOUT=*
//SYSUDUMP DD SYSOUT=*
//        PEND