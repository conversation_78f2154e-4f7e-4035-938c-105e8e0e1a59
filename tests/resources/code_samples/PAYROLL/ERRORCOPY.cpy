*================================================================
      * COPYBOOK ERRORCOPY.CPY - ERROR HANDLING DEFINITIONS
      * Used by: All payroll programs for error management
      * Last Modified: 01/15/2024
      *================================================================
       01  ERROR-MESSAGE-TABLE.
           05  ERROR-MESSAGES OCCURS 50 TIMES INDEXED BY ERR-IDX.
               10  ERR-CODE            PIC X(04).
               10  ERR-SEVERITY        PIC X(01).
                   88  ERR-WARNING     VALUE 'W'.
                   88  ERR-ERROR       VALUE 'E'.
                   88  ERR-FATAL       VALUE 'F'.
                   88  ERR-INFO        VALUE 'I'.
               10  ERR-MESSAGE         PIC X(80).
               10  ERR-TIMESTAMP       PIC X(26).

       01  ERROR-CONSTANTS.
           05  ERR-FILE-NOT-FOUND      PIC X(04) VALUE 'E001'.
           05  ERR-INVALID-RECORD      PIC X(04) VALUE 'E002'.
           05  ERR-CALC-ERROR          PIC X(04) VALUE 'E003'.
           05  ERR-DB-CONNECTION       PIC X(04) VALUE 'E004'.
           05  ERR-VSAM-ACCESS         PIC X(04) VALUE 'E005'.
           05  ERR-MQ-ERROR            PIC X(04) VALUE 'E006'.
           05  ERR-IMS-ERROR           PIC X(04) VALUE 'E007'.
           05  ERR-SUBPROGRAM-CALL     PIC X(04) VALUE 'E008'.
           05  ERR-MEMORY-SHORTAGE     PIC X(04) VALUE 'E009'.
           05  ERR-SECURITY-VIOLATION  PIC X(04) VALUE 'E010'.

       01  WARNING-CONSTANTS.
           05  WARN-DUPLICATE-RECORD   PIC X(04) VALUE 'W001'.
           05  WARN-MISSING-DATA       PIC X(04) VALUE 'W002'.
           05  WARN-APPROX-CALC        PIC X(04) VALUE 'W003'.

       01  ERROR-COUNTERS.
           05  TOTAL-ERROR-COUNT       PIC 9(06) VALUE ZERO.
           05  FATAL-ERROR-COUNT       PIC 9(04) VALUE ZERO.
           05  WARNING-COUNT           PIC 9(04) VALUE ZERO.
           05  INFO-COUNT              PIC 9(04) VALUE ZERO.

      * Dead code - old error handling system  
       01  OBSOLETE-ERROR-FIELDS.
           05  OLD-ERROR-FLAG          PIC X VALUE 'N'.
           05  OLD-ERROR-MESSAGE       PIC X(50).
           05  OLD-ERROR-LINE-NUMBER   PIC 9(04).