* ================================================================
* ACME CORPORATION PAYROLL SYSTEM
* MQ SERIES SECURITY DEFINITIONS
* MEMBER: MQSEC.MQSC
* AUTHOR: SECURITY TEAM
* DATE: 01/15/2024
* ================================================================

* ----------------------------------------------------------------
* QUEUE MANAGER SECURITY
* ----------------------------------------------------------------

* Set Queue Manager Security
SET AUTHREC PROFILE('PAYROLL.QM') +
           OBJTYPE(QMGR) +
           PRINCIPAL('PAYROLL1') +
           AUTHADD(CONNECT,INQ,DSP,SETALL,SETID)

SET AUTHREC PROFILE('PAYROLL.QM') +
           OBJTYPE(QMGR) +
           PRINCIPAL('PAYGRP') +
           AUTHADD(CONNECT,INQ,DSP)

* ----------------------------------------------------------------
* QUEUE SECURITY DEFINITIONS
* ----------------------------------------------------------------

* Set Security for All Payroll Queues
SET AUTHREC PROFILE('PAYROLL.**') +
           OBJTYPE(QUEUE) +
           PRINCIPAL('PAYROLL1') +
           AUTHADD(BROWSE,GET,INQ,PUT,SET,DSP,CLR,DEL)

* Set Security for Payroll Application Group
SET AUTHREC PROFILE('PAYROLL.QUEUE') +
           OBJTYPE(QUEUE) +
           PRINCIPAL('PAYGRP') +
           AUTHADD(BROWSE,GET,INQ,PUT,DSP)

SET AUTHREC PROFILE('PAYROLL.SALARY.*') +
           OBJTYPE(QUEUE) +
           PRINCIPAL('PAYGRP') +
           AUTHADD(BROWSE,GET,INQ,PUT,DSP)

* Set Security for Error Queues
SET AUTHREC PROFILE('PAYROLL.ERROR.QUEUE') +
           OBJTYPE(QUEUE) +
           PRINCIPAL('PAYGRP') +
           AUTHADD(BROWSE,INQ,DSP)

* Set Security for Admin Users
SET AUTHREC PROFILE('PAYROLL.**') +
           OBJTYPE(QUEUE) +
           PRINCIPAL('MQADMIN') +
           AUTHADD(BROWSE,GET,INQ,PUT,SET,DSP,CLR,DEL,SETALL,SETID)

* ----------------------------------------------------------------
* CHANNEL SECURITY
* ----------------------------------------------------------------

* Set Channel Security for Payroll Channels
SET AUTHREC PROFILE('PAYROLL.**') +
           OBJTYPE(CHANNEL) +
           PRINCIPAL('PAYROLL1') +
           AUTHADD(CTL,DSP,CHG)

SET AUTHREC PROFILE('PAYROLL.SVRCONN') +
           OBJTYPE(CHANNEL) +
           PRINCIPAL('PAYGRP') +
           AUTHADD(DSP)

* Set Channel Security for Admin
SET AUTHREC PROFILE('PAYROLL.**') +
           OBJTYPE(CHANNEL) +
           PRINCIPAL('MQADMIN') +
           AUTHADD(CTL,DSP,CHG,SETALL,SETID)

* ----------------------------------------------------------------
* PROCESS SECURITY
* ----------------------------------------------------------------

* Set Process Security
SET AUTHREC PROFILE('PAYROLL.**') +
           OBJTYPE(PROCESS) +
           PRINCIPAL('PAYROLL1') +
           AUTHADD(DSP,CHG)

SET AUTHREC PROFILE('PAYROLL.**') +
           OBJTYPE(PROCESS) +
           PRINCIPAL('MQADMIN') +
           AUTHADD(DSP,CHG,SETALL,SETID)

* ----------------------------------------------------------------
* NAMELIST SECURITY (IF USED)
* ----------------------------------------------------------------

SET AUTHREC PROFILE('PAYROLL.**') +
           OBJTYPE(NAMELIST) +
           PRINCIPAL('PAYROLL1') +
           AUTHADD(DSP,CHG)

* ----------------------------------------------------------------
* TOPIC SECURITY (IF USING PUBLISH/SUBSCRIBE)
* ----------------------------------------------------------------

SET AUTHREC PROFILE('PAYROLL/**') +
           OBJTYPE(TOPIC) +
           PRINCIPAL('PAYROLL1') +
           AUTHADD(PUB,SUB,RESUME,DSP)

SET AUTHREC PROFILE('PAYROLL/**') +
           OBJTYPE(TOPIC) +
           PRINCIPAL('PAYGRP') +
           AUTHADD(PUB,SUB,DSP)

* ----------------------------------------------------------------
* CONNECTION SECURITY
* ----------------------------------------------------------------

* Restrict Connections to Authorized Users
SET AUTHREC PROFILE('PAYROLL.QM') +
           OBJTYPE(QMGR) +
           PRINCIPAL('*DEFAULT*') +
           AUTHDEL(CONNECT)

* Allow Only Specific Groups
SET AUTHREC PROFILE('PAYROLL.QM') +
           OBJTYPE(QMGR) +
           PRINCIPAL('PAYGRP') +
           AUTHADD(CONNECT)

SET AUTHREC PROFILE('PAYROLL.QM') +
           OBJTYPE(QMGR) +
           PRINCIPAL('MQADMIN') +
           AUTHADD(CONNECT)

* ----------------------------------------------------------------
* AUTHENTICATION INFORMATION (IF USING SSL/TLS)
* ----------------------------------------------------------------

DEFINE AUTHINFO('PAYROLL.AUTHINFO') +
       AUTHTYPE(CRLLDAP) +
       CONNAME('LDAP.ACME.COM(389)') +
       LDAPUSER('CN=MQSERVICE,OU=SERVICES,DC=ACME,DC=COM') +
       LDAPPWD('PASSWORD') +
       SHORTUSR('OU=USERS,DC=ACME,DC=COM') +
       DESCR('PAYROLL LDAP AUTHENTICATION') +
       REPLACE

* ----------------------------------------------------------------
* REFRESH SECURITY
* ----------------------------------------------------------------

REFRESH SECURITY TYPE(CONNAUTH)
REFRESH SECURITY TYPE(SSL)