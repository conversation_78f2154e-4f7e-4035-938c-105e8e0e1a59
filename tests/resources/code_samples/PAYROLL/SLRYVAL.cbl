*================================================================
      * SUBPROGRAM SLRYVAL.CBL - VALIDATION ROUTINE
      * Called by: SLRYCALC main program
      * Purpose: Validate employee record data
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. SLRYVAL.
       AUTHOR. VALIDATION-TEAM.
       DATE-WRITTEN. 01/15/2024.

       DATA DIVISION.
       WORKING-STORAGE SECTION.
       
       COPY ERRORCOPY.
       
       01  WS-VALIDATION-RESULT        PIC X VALUE 'Y'.
           88  VALID-RECORD            VALUE 'Y'.
           88  INVALID-RECORD          VALUE 'N'.

       01  WS-ERROR-TABLE.
           05  WS-ERROR-COUNT          PIC 9(03) VALUE ZERO.
           05  WS-ERROR-DETAIL OCCURS 10 TIMES.
               10  WS-ERROR-FIELD      PIC X(15).
               10  WS-ERROR-REASON     PIC X(50).

       01  WS-LOOP-CTR                 PIC 9(02).

      * Dead code section - old validation rules
       01  WS-OLD-VALIDATION-FIELDS.
           05  WS-OLD-MIN-SALARY       PIC 9(06) VALUE 015000.
           05  WS-OLD-MAX-SALARY       PIC 9(06) VALUE 200000.
           05  WS-OLD-GRADE-TABLE.
               10  FILLER              PIC X(01) VALUE 'A'.
               10  FILLER              PIC X(01) VALUE 'B'.
               10  FILLER              PIC X(01) VALUE 'C'.
               10  FILLER              PIC X(01) VALUE 'D'.
           05  WS-OLD-GRADE-ARRAY REDEFINES WS-OLD-GRADE-TABLE.
               10  WS-OLD-GRADES OCCURS 4 TIMES PIC X(01).

       01  WS-VALIDATION-CONSTANTS.
           05  WS-CURRENT-YEAR         PIC 9(04) VALUE 2024.
           05  WS-MIN-HIRE-YEAR        PIC 9(04) VALUE 1980.
           05  WS-MAX-NAME-LENGTH      PIC 9(02) VALUE 30.

       LINKAGE SECTION.
       COPY EMPRCOPY.

       01  LS-VALIDATION-STATUS        PIC X.

       PROCEDURE DIVISION USING EMPLOYEE-RECORD LS-VALIDATION-STATUS.

       0000-MAIN-VALIDATION SECTION.
       0000-VALIDATE-EMPLOYEE.
           SET VALID-RECORD TO TRUE
           MOVE ZERO TO WS-ERROR-COUNT
           
           PERFORM 1000-VALIDATE-EMPLOYEE-ID
           PERFORM 1100-VALIDATE-NAME  
           PERFORM 1200-VALIDATE-DEPARTMENT
           PERFORM 1300-VALIDATE-SALARY
           PERFORM 1400-VALIDATE-GRADE
           PERFORM 1500-VALIDATE-DATES
           PERFORM 1600-VALIDATE-STATUS
           
           IF INVALID-RECORD
               MOVE 'N' TO LS-VALIDATION-STATUS
               PERFORM 9000-LOG-VALIDATION-ERRORS
           ELSE
               MOVE 'Y' TO LS-VALIDATION-STATUS
           END-IF
           
           GOBACK.

       1000-VALIDATE-EMPLOYEE-ID SECTION.
       1000-VALIDATE-ID.
           IF EMP-ID = SPACES OR EMP-ID = LOW-VALUES
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-ID' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'EMPLOYEE ID CANNOT BE BLANK' 
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-IF
           
      * Using string literal in condition
           IF EMP-ID(1:3) NOT = 'EMP'
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-ID' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'EMPLOYEE ID FORMAT INVALID - MUST START WITH EMP' 
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-IF
           
           IF EMP-ID(4:7) NOT NUMERIC
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-ID' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'EMPLOYEE ID NUMERIC PORTION INVALID'
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-IF.

       1100-VALIDATE-NAME SECTION.
       1100-VALIDATE-NAME-PROC.
           IF EMP-NAME = SPACES
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-NAME' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'EMPLOYEE NAME CANNOT BE BLANK'
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-IF
           
           IF EMP-NAME(1:1) = ' '
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-NAME' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'EMPLOYEE NAME CANNOT START WITH SPACE'
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-IF.

       1200-VALIDATE-DEPARTMENT SECTION.
       1200-VALIDATE-DEPT.
           EVALUATE EMP-DEPT-CODE
               WHEN 'IT '
               WHEN 'HR '  
               WHEN 'FIN'
               WHEN 'MKT'
               WHEN 'SAL'
               WHEN 'OPS'
               WHEN 'LEG'
               WHEN 'ADM'
                   CONTINUE
               WHEN OTHER
                   SET INVALID-RECORD TO TRUE
                   ADD 1 TO WS-ERROR-COUNT
                   MOVE 'EMP-DEPT-CODE' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
                   MOVE 'INVALID DEPARTMENT CODE'
                     TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-EVALUATE.

       1300-VALIDATE-SALARY SECTION.
       1300-VALIDATE-SALARY-PROC.
           IF EMP-BASIC-SALARY NOT NUMERIC
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-BASIC-SALARY' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'SALARY MUST BE NUMERIC'
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           ELSE
               IF EMP-BASIC-SALARY < 20000 OR EMP-BASIC-SALARY > 500000
                   SET INVALID-RECORD TO TRUE
                   ADD 1 TO WS-ERROR-COUNT
                   MOVE 'EMP-BASIC-SALARY' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
                   MOVE 'SALARY OUT OF VALID RANGE (20K-500K)'
                     TO WS-ERROR-REASON(WS-ERROR-COUNT)
               END-IF
           END-IF.

       1400-VALIDATE-GRADE SECTION.
       1400-VALIDATE-GRADE-PROC.
           IF NOT (GRADE-EXECUTIVE OR GRADE-SENIOR OR 
                   GRADE-REGULAR OR GRADE-JUNIOR)
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-GRADE' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'INVALID EMPLOYEE GRADE (A/B/C/D ONLY)'
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-IF.

       1500-VALIDATE-DATES SECTION.
       1500-VALIDATE-DATE-PROC.
           IF EMP-HIRE-DATE NOT NUMERIC
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-HIRE-DATE' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'HIRE DATE FORMAT INVALID (YYYYMMDD)'
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           ELSE
               IF EMP-HIRE-DATE(1:4) < WS-MIN-HIRE-YEAR OR
                  EMP-HIRE-DATE(1:4) > WS-CURRENT-YEAR
                   SET INVALID-RECORD TO TRUE
                   ADD 1 TO WS-ERROR-COUNT
                   MOVE 'EMP-HIRE-DATE' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
                   MOVE 'HIRE DATE YEAR OUT OF RANGE'
                     TO WS-ERROR-REASON(WS-ERROR-COUNT)
               END-IF
           END-IF.

       1600-VALIDATE-STATUS SECTION.
       1600-VALIDATE-STATUS-PROC.
           IF NOT (EMP-ACTIVE OR EMP-INACTIVE OR EMP-TERMINATED)
               SET INVALID-RECORD TO TRUE
               ADD 1 TO WS-ERROR-COUNT
               MOVE 'EMP-STATUS' TO WS-ERROR-FIELD(WS-ERROR-COUNT)
               MOVE 'INVALID EMPLOYEE STATUS (A/I/T ONLY)'
                 TO WS-ERROR-REASON(WS-ERROR-COUNT)
           END-IF.

       9000-LOG-VALIDATION-ERRORS SECTION.
       9000-LOG-ERRORS.
           DISPLAY 'VALIDATION ERRORS FOR EMPLOYEE: ' EMP-ID
           PERFORM VARYING WS-LOOP-CTR FROM 1 BY 1
               UNTIL WS-LOOP-CTR > WS-ERROR-COUNT
               DISPLAY 'ERROR: ' WS-ERROR-FIELD(WS-LOOP-CTR)
                       ' - ' WS-ERROR-REASON(WS-LOOP-CTR)
           END-PERFORM.