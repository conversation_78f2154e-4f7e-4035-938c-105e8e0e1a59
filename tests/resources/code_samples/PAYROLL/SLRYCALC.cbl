      *================================================================
      * MAIN SALARY CALCULATION PROGRAM - SLRYCALC.CBL
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. SLRYCALC.
       AUTHOR. PAYROLL-SYSTEM.
       DATE-WRITTEN. 01/15/2024.
       DATE-COMPILED.
       SECURITY. CONFIDENTIAL.
       REMARKS. COMPREHENSIVE SALARY CALCULATION WITH ALL FEATURES.

       ENVIRONMENT DIVISION.
       CONFIGURATION SECTION.
       SOURCE-COMPUTER. IBM-390.
       OBJECT-COMPUTER. IBM-390.
       SPECIAL-NAMES.
           DECIMAL-POINT IS COMMA.

       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT EMPLOYEE-FILE ASSIGN TO EMPFILE
               ORGANIZATION IS SEQUENTIAL
               ACCESS MODE IS SEQUENTIAL
               FILE STATUS IS WS-EMP-FILE-STATUS.
           
           SELECT PAYROLL-OUTPUT ASSIGN TO PAYFILE
               ORGANIZATION IS SEQUENTIAL
               ACCESS MODE IS SEQUENTIAL
               FILE STATUS IS WS-PAY-FILE-STATUS.
           
           SELECT EMPLOYEE-VSAM ASSIGN TO EMPVSAM
               ORGANIZATION IS INDEXED
               ACCESS MODE IS DYNAMIC
               RECORD KEY IS EMP-ID-VSAM
               FILE STATUS IS WS-VSAM-STATUS.

       I-O-CONTROL.
           SAME RECORD AREA FOR EMPLOYEE-FILE PAYROLL-OUTPUT.

       DATA DIVISION.
       FILE SECTION.
       
       FD  EMPLOYEE-FILE
           RECORDING MODE IS F
           LABEL RECORDS ARE STANDARD
           RECORD CONTAINS 200 CHARACTERS
           BLOCK CONTAINS 0 RECORDS
           DATA RECORD IS EMPLOYEE-RECORD.
       
       COPY EMPRCOPY.

       FD  PAYROLL-OUTPUT
           RECORDING MODE IS F
           LABEL RECORDS ARE STANDARD
           RECORD CONTAINS 300 CHARACTERS
           BLOCK CONTAINS 0 RECORDS
           DATA RECORD IS PAYROLL-RECORD.
       
       01  PAYROLL-RECORD.
           05  PAY-EMP-ID              PIC X(10).
           05  PAY-EMP-NAME            PIC X(30).
           05  PAY-GROSS-SALARY        PIC 9(07)V99.
           05  PAY-NET-SALARY          PIC 9(07)V99.
           05  PAY-TAX-AMOUNT          PIC 9(06)V99.
           05  PAY-BONUS-AMOUNT        PIC 9(06)V99.
           05  FILLER                  PIC X(238).

       FD  EMPLOYEE-VSAM
           RECORD CONTAINS 150 CHARACTERS
           DATA RECORD IS EMPLOYEE-VSAM-REC.
       
       01  EMPLOYEE-VSAM-REC.
           05  EMP-ID-VSAM             PIC X(10).
           05  EMP-SALARY-HISTORY.
               10  EMP-PREV-SALARY OCCURS 12 TIMES PIC 9(07)V99.
           05  EMP-BENEFITS            PIC 9(06)V99.
           05  FILLER                  PIC X(32).

       WORKING-STORAGE SECTION.
       
       COPY WSCOPY.
       COPY TAXCOPY.
       COPY ERRORCOPY.
       
       01  WS-FILE-STATUS.
           05  WS-EMP-FILE-STATUS      PIC XX VALUE SPACES.
           05  WS-PAY-FILE-STATUS      PIC XX VALUE SPACES.
           05  WS-VSAM-STATUS          PIC XX VALUE SPACES.

       01  WS-COUNTERS.
           05  WS-RECORD-COUNT         PIC 9(06) VALUE ZERO.
           05  WS-ERROR-COUNT          PIC 9(04) VALUE ZERO.
           05  WS-LOOP-COUNT           PIC 9(03) VALUE ZERO.

       01  WS-CALCULATION-FIELDS.
           05  WS-GROSS-SALARY         PIC 9(07)V99 VALUE ZERO.
           05  WS-NET-SALARY           PIC 9(07)V99 VALUE ZERO.
           05  WS-TAX-AMOUNT           PIC 9(06)V99 VALUE ZERO.
           05  WS-BONUS-AMOUNT         PIC 9(06)V99 VALUE ZERO.
           05  WS-TEMP-AMOUNT          PIC 9(08)V99 VALUE ZERO.

       01  WS-SWITCHES.
           05  WS-EOF-SWITCH           PIC X VALUE 'N'.
               88  END-OF-FILE         VALUE 'Y'.
           05  WS-ERROR-SWITCH         PIC X VALUE 'N'.
               88  ERROR-OCCURRED      VALUE 'Y'.
           05  WS-PROCESS-SWITCH       PIC X VALUE 'Y'.
               88  PROCESS-RECORD      VALUE 'Y'.

       01  WS-REDEFINES-AREA.
           05  WS-NUMERIC-FIELD        PIC 9(10).
           05  WS-ALPHA-FIELD REDEFINES WS-NUMERIC-FIELD.
               10  WS-ALPHA-PART       PIC X(05).
               10  WS-NUMERIC-PART     PIC 9(05).

       01  WS-OCCURS-TABLE.
           05  WS-DEPARTMENT-TABLE OCCURS 10 TIMES
                                   INDEXED BY DEPT-IDX.
               10  WS-DEPT-CODE        PIC X(03).
               10  WS-DEPT-NAME        PIC X(20).
               10  WS-DEPT-BUDGET      PIC 9(08)V99.

       01  WS-LITERALS.
           05  WS-PROGRAM-NAME         PIC X(08) VALUE 'SLRYCALC'.
           05  WS-VERSION              PIC X(05) VALUE 'V1.00'.
           05  WS-COMPANY-CODE         PIC X(03) VALUE 'ABC'.

      * Dead code - unused variables
       01  WS-UNUSED-FIELDS.
           05  WS-OLD-FIELD1           PIC X(10) VALUE SPACES.
           05  WS-OLD-FIELD2           PIC 9(05) VALUE ZERO.
           05  WS-OBSOLETE-SWITCH      PIC X VALUE 'N'.

       01  WS-DB2-SQLCA.
           COPY SQLCA.

       01  WS-IMS-CONTROL-BLOCKS.
           COPY IMSCB.

       01  WS-MQ-FIELDS.
           05  WS-MQ-QUEUE-NAME        PIC X(48) VALUE 'PAYROLL.QUEUE'.
           05  WS-MQ-MESSAGE           PIC X(1000).
           05  WS-MQ-MESSAGE-LENGTH    PIC S9(9) COMP VALUE 1000.

       LINKAGE SECTION.
       01  LS-PARM-AREA.
           05  LS-PARM-LENGTH          PIC S9(4) COMP.
           05  LS-PARM-DATA            PIC X(100).

       PROCEDURE DIVISION USING LS-PARM-AREA.

       DECLARATIVES.
       FILE-ERROR-SECTION SECTION.
           USE AFTER STANDARD ERROR PROCEDURE ON EMPLOYEE-FILE.
       FILE-ERROR-DECLARATIVE.
           DISPLAY 'FILE ERROR ON EMPLOYEE-FILE: ' WS-EMP-FILE-STATUS.
           SET ERROR-OCCURRED TO TRUE.
           GO TO FILE-ERROR-EXIT.
       FILE-ERROR-EXIT.
           EXIT.

       VSAM-ERROR-SECTION SECTION.
           USE AFTER STANDARD ERROR PROCEDURE ON EMPLOYEE-VSAM.
       VSAM-ERROR-DECLARATIVE.
           DISPLAY 'VSAM ERROR: ' WS-VSAM-STATUS.
           PERFORM 9900-VSAM-ERROR-HANDLER.
       END DECLARATIVES.

       0000-MAIN-PROCESSING SECTION.
       0000-MAIN-LOGIC.
           PERFORM 1000-INITIALIZE
           PERFORM 2000-PROCESS-RECORDS
               UNTIL END-OF-FILE OR ERROR-OCCURRED
           PERFORM 8000-FINALIZE
           GOBACK.

       1000-INITIALIZE SECTION.
       1000-INIT-START.
           DISPLAY 'STARTING SALARY CALCULATION PROGRAM'
           DISPLAY 'PROGRAM: ' WS-PROGRAM-NAME ' VERSION: ' WS-VERSION
           
           PERFORM 1100-OPEN-FILES
           PERFORM 1200-INITIALIZE-TABLES
           PERFORM 1300-READ-CONTROL-PARAMETERS
           
      * String literal usage
           IF WS-COMPANY-CODE = 'ABC'
               DISPLAY 'PROCESSING FOR COMPANY ABC'
           END-IF.

       1100-OPEN-FILES SECTION.
       1100-OPEN-START.
           OPEN INPUT EMPLOYEE-FILE
           OPEN OUTPUT PAYROLL-OUTPUT
           OPEN I-O EMPLOYEE-VSAM
           
           IF WS-EMP-FILE-STATUS NOT = '00'
               DISPLAY 'ERROR OPENING EMPLOYEE FILE: ' WS-EMP-FILE-STATUS
               SET ERROR-OCCURRED TO TRUE
           END-IF.

       1200-INITIALIZE-TABLES SECTION.
       1200-INIT-TABLE-START.
           PERFORM VARYING DEPT-IDX FROM 1 BY 1
               UNTIL DEPT-IDX > 10
               MOVE SPACES TO WS-DEPT-CODE(DEPT-IDX)
               MOVE SPACES TO WS-DEPT-NAME(DEPT-IDX)
               MOVE ZERO TO WS-DEPT-BUDGET(DEPT-IDX)
           END-PERFORM.

       1300-READ-CONTROL-PARAMETERS SECTION.
       1300-READ-PARMS.
           CALL 'SLRYPARM' USING WS-CALCULATION-FIELDS
           
           IF RETURN-CODE NOT = 0
               DISPLAY 'ERROR CALLING SLRYPARM: ' RETURN-CODE
               SET ERROR-OCCURRED TO TRUE
           END-IF.

       2000-PROCESS-RECORDS SECTION.
       2000-PROCESS-START.
           PERFORM 2100-READ-EMPLOYEE-RECORD
           IF NOT END-OF-FILE AND NOT ERROR-OCCURRED
               PERFORM 2200-VALIDATE-RECORD
               IF PROCESS-RECORD
                   PERFORM 2300-CALCULATE-SALARY
                   PERFORM 2400-UPDATE-VSAM
                   PERFORM 2500-WRITE-PAYROLL
                   PERFORM 2600-SEND-MQ-MESSAGE
               END-IF
           END-IF.

       2100-READ-EMPLOYEE-RECORD SECTION.
       2100-READ-START.
           READ EMPLOYEE-FILE
           AT END
               SET END-OF-FILE TO TRUE
           NOT AT END
               ADD 1 TO WS-RECORD-COUNT
           END-READ.

       2200-VALIDATE-RECORD SECTION.
       2200-VALIDATE-START.
           CALL 'SLRYVAL' USING EMPLOYEE-RECORD WS-PROCESS-SWITCH.

       2300-CALCULATE-SALARY SECTION.
       2300-CALC-START.
           MOVE EMP-BASIC-SALARY TO WS-GROSS-SALARY
           
           PERFORM 2310-CALCULATE-BONUS
           PERFORM 2320-CALCULATE-TAX
           PERFORM 2330-CALCULATE-NET-SALARY
           
           PERFORM 2350-DB2-SALARY-LOOKUP
           PERFORM 2360-IMS-BENEFIT-LOOKUP.

       2310-CALCULATE-BONUS SECTION.
       2310-BONUS-START.
           EVALUATE EMP-GRADE
               WHEN 'A'
                   COMPUTE WS-BONUS-AMOUNT = WS-GROSS-SALARY * 0.15
               WHEN 'B'
                   COMPUTE WS-BONUS-AMOUNT = WS-GROSS-SALARY * 0.10
               WHEN 'C'
                   COMPUTE WS-BONUS-AMOUNT = WS-GROSS-SALARY * 0.05
               WHEN 'D'
                   COMPUTE WS-BONUS-AMOUNT = WS-GROSS-SALARY * 0.02
               WHEN OTHER
                   MOVE ZERO TO WS-BONUS-AMOUNT
           END-EVALUATE

           ADD WS-BONUS-AMOUNT TO WS-GROSS-SALARY.

       2320-CALCULATE-TAX SECTION.
       2320-TAX-START.
      * Progressive tax calculation using all defined brackets
           MOVE ZERO TO WS-TAX-AMOUNT

           IF WS-GROSS-SALARY > TAX-BRACKET-3
      * Executive rate on amount over 250K
               COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                   ((WS-GROSS-SALARY - TAX-BRACKET-3) * TAX-RATE-EXECUTIVE)
      * High rate on 100K to 250K bracket
               COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                   ((TAX-BRACKET-3 - TAX-BRACKET-2) * TAX-RATE-HIGH)
      * Medium rate on 50K to 100K bracket
               COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                   ((TAX-BRACKET-2 - TAX-BRACKET-1) * TAX-RATE-MED)
      * Low rate on first 50K
               COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                   (TAX-BRACKET-1 * TAX-RATE-LOW)
           ELSE
               IF WS-GROSS-SALARY > TAX-BRACKET-2
      * High rate on amount over 100K up to 250K
                   COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                       ((WS-GROSS-SALARY - TAX-BRACKET-2) * TAX-RATE-HIGH)
      * Medium rate on 50K to 100K bracket
                   COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                       ((TAX-BRACKET-2 - TAX-BRACKET-1) * TAX-RATE-MED)
      * Low rate on first 50K
                   COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                       (TAX-BRACKET-1 * TAX-RATE-LOW)
               ELSE
                   IF WS-GROSS-SALARY > TAX-BRACKET-1
      * Medium rate on amount over 50K up to 100K
                       COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                           ((WS-GROSS-SALARY - TAX-BRACKET-1) * TAX-RATE-MED)
      * Low rate on first 50K
                       COMPUTE WS-TAX-AMOUNT = WS-TAX-AMOUNT +
                           (TAX-BRACKET-1 * TAX-RATE-LOW)
                   ELSE
      * Low rate on entire amount (under 50K)
                       COMPUTE WS-TAX-AMOUNT =
                           WS-GROSS-SALARY * TAX-RATE-LOW
                   END-IF
               END-IF
           END-IF.

       2330-CALCULATE-NET-SALARY SECTION.
       2330-NET-START.
           COMPUTE WS-NET-SALARY = WS-GROSS-SALARY - WS-TAX-AMOUNT.

       2350-DB2-SALARY-LOOKUP SECTION.
       2350-DB2-START.
           EXEC SQL
               SELECT AVG_SALARY
               INTO :WS-TEMP-AMOUNT
               FROM SALARY_STATS
               WHERE DEPT_CODE = :EMP-DEPT-CODE
           END-EXEC
           
           IF SQLCODE NOT = 0
               DISPLAY 'DB2 ERROR: ' SQLCODE
           END-IF.

       2360-IMS-BENEFIT-LOOKUP SECTION.
       2360-IMS-START.
           CALL 'CBLTDLI' USING GU-FUNCTION
                                 EMP-BENEFITS-PCB
                                 EMP-BENEFITS-SEGMENT
                                 EMP-BENEFITS-SSA
           
           IF IMS-STATUS-CODE NOT = SPACES
               DISPLAY 'IMS ERROR: ' IMS-STATUS-CODE
           END-IF.

       2400-UPDATE-VSAM SECTION.
       2400-VSAM-START.
      * Validate employee ID before VSAM operation
           IF EMP-ID = SPACES OR EMP-ID = LOW-VALUES
               DISPLAY 'ERROR: INVALID EMPLOYEE ID FOR VSAM UPDATE'
               ADD 1 TO WS-ERROR-COUNT
               GO TO 2400-VSAM-EXIT
           END-IF

           MOVE EMP-ID TO EMP-ID-VSAM
           READ EMPLOYEE-VSAM

           IF WS-VSAM-STATUS = '00'
               PERFORM 2410-UPDATE-SALARY-HISTORY
               REWRITE EMPLOYEE-VSAM-REC
               IF WS-VSAM-STATUS NOT = '00'
                   DISPLAY 'VSAM REWRITE ERROR: ' WS-VSAM-STATUS
                   ADD 1 TO WS-ERROR-COUNT
               END-IF
           ELSE
               IF WS-VSAM-STATUS = '23'
                   PERFORM 2420-CREATE-VSAM-RECORD
                   WRITE EMPLOYEE-VSAM-REC
                   IF WS-VSAM-STATUS NOT = '00'
                       DISPLAY 'VSAM WRITE ERROR: ' WS-VSAM-STATUS
                       ADD 1 TO WS-ERROR-COUNT
                   END-IF
               ELSE
                   DISPLAY 'VSAM READ ERROR: ' WS-VSAM-STATUS
                   ADD 1 TO WS-ERROR-COUNT
               END-IF
           END-IF.

       2400-VSAM-EXIT.
           EXIT.

       2410-UPDATE-SALARY-HISTORY SECTION.
       2410-UPDATE-HIST-START.
           PERFORM VARYING WS-LOOP-COUNT FROM 12 BY -1
               UNTIL WS-LOOP-COUNT = 1
               MOVE EMP-PREV-SALARY(WS-LOOP-COUNT - 1) 
                 TO EMP-PREV-SALARY(WS-LOOP-COUNT)
           END-PERFORM
           
           MOVE WS-GROSS-SALARY TO EMP-PREV-SALARY(1).

       2420-CREATE-VSAM-RECORD SECTION.
       2420-CREATE-VSAM.
           MOVE EMP-ID TO EMP-ID-VSAM
           MOVE WS-GROSS-SALARY TO EMP-PREV-SALARY(1)
           PERFORM VARYING WS-LOOP-COUNT FROM 2 BY 1
               UNTIL WS-LOOP-COUNT > 12
               MOVE ZERO TO EMP-PREV-SALARY(WS-LOOP-COUNT)
           END-PERFORM
           MOVE ZERO TO EMP-BENEFITS.

       2500-WRITE-PAYROLL SECTION.
       2500-WRITE-START.
           MOVE EMP-ID TO PAY-EMP-ID
           MOVE EMP-NAME TO PAY-EMP-NAME
           MOVE WS-GROSS-SALARY TO PAY-GROSS-SALARY
           MOVE WS-NET-SALARY TO PAY-NET-SALARY
           MOVE WS-TAX-AMOUNT TO PAY-TAX-AMOUNT
           MOVE WS-BONUS-AMOUNT TO PAY-BONUS-AMOUNT
           
           WRITE PAYROLL-RECORD
           
           IF WS-PAY-FILE-STATUS NOT = '00'
               DISPLAY 'ERROR WRITING PAYROLL: ' WS-PAY-FILE-STATUS
               ADD 1 TO WS-ERROR-COUNT
           END-IF.

       2600-SEND-MQ-MESSAGE SECTION.
       2600-MQ-START.
      * Validate message content before MQ operation
           IF EMP-ID = SPACES OR WS-NET-SALARY = ZERO
               DISPLAY 'WARNING: INCOMPLETE DATA FOR MQ MESSAGE'
               GO TO 2600-MQ-EXIT
           END-IF

           STRING 'EMP:' EMP-ID ':SALARY:' WS-NET-SALARY
               DELIMITED BY SIZE
               INTO WS-MQ-MESSAGE
           END-STRING

           CALL 'MQPUT' USING WS-MQ-QUEUE-NAME
                              WS-MQ-MESSAGE
                              WS-MQ-MESSAGE-LENGTH

           IF RETURN-CODE NOT = 0
               DISPLAY 'MQ PUT ERROR: ' RETURN-CODE
               DISPLAY 'QUEUE: ' WS-MQ-QUEUE-NAME
               DISPLAY 'MESSAGE: ' WS-MQ-MESSAGE(1:50)
               ADD 1 TO WS-ERROR-COUNT
           END-IF.

       2600-MQ-EXIT.
           EXIT.

       3000-SPECIAL-PROCESSING SECTION.
       3000-SPECIAL-START.
      * Demonstrate PERFORM THROUGH
           PERFORM 3100-START-SPECIAL THROUGH 3199-END-SPECIAL
           
      * Example of GOTO usage (generally discouraged)
           GO TO 3000-SPECIAL-EXIT.

       3100-START-SPECIAL SECTION.
       3100-SPECIAL-CALC.
           CALL 'IDCAMS' USING 'LISTCAT ENTRIES(EMPLOYEE.VSAM.FILE)'
           
           IF RETURN-CODE = 0
               DISPLAY 'VSAM CATALOG OK'
           ELSE
               DISPLAY 'VSAM CATALOG ERROR: ' RETURN-CODE
           END-IF.

       3150-MIDDLE-SPECIAL SECTION.
       3150-MIDDLE-PROC.
           CALL 'IEBGENER' USING 'COPY INDD=SYSIN,OUTDD=SYSOUT'.

       3199-END-SPECIAL SECTION.
       3199-END-PROC.
           DISPLAY 'SPECIAL PROCESSING COMPLETE'.

       3000-SPECIAL-EXIT.
           EXIT.

       8000-FINALIZE SECTION.
       8000-FINALIZE-START.
           PERFORM 8100-CLOSE-FILES
           PERFORM 8200-PRINT-STATISTICS
           PERFORM 8300-CLEANUP.

       8100-CLOSE-FILES SECTION.
       8100-CLOSE-START.
           CLOSE EMPLOYEE-FILE
           CLOSE PAYROLL-OUTPUT  
           CLOSE EMPLOYEE-VSAM.

       8200-PRINT-STATISTICS SECTION.
       8200-STATS-START.
           DISPLAY 'PROCESSING STATISTICS:'
           DISPLAY 'RECORDS PROCESSED: ' WS-RECORD-COUNT
           DISPLAY 'ERRORS ENCOUNTERED: ' WS-ERROR-COUNT.

       8300-CLEANUP SECTION.
       8300-CLEANUP-START.
           CALL 'SLRYEXIT' USING WS-COUNTERS.

       9900-VSAM-ERROR-HANDLER SECTION.  
       9900-VSAM-ERROR.
           DISPLAY 'CRITICAL VSAM ERROR - PROCESSING HALTED'
           SET ERROR-OCCURRED TO TRUE.