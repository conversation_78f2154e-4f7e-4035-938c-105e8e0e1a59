# Detailed Workflow Description
## Phase 1: System Initialization (1000-INITIALIZE)
- File Operations Setup
    - Opens EMPLOYEE-FILE for input (sequential read)
    - Opens PAYROLL-OUTPUT for output (sequential write)
    - Opens EMPLOYEE-VSAM for I-O (indexed access)
- Validates all file status codes
- Table Initialization
    - Initializes department lookup table (10 entries)
- Clears all working storage counters
- Sets up indexed table structures
- Parameter Loading
    - Calls SLRYPARM subprogram to read system parameters
    - Loads calculation constants and validation rules
    - Sets default values if parameter file unavailable
## Phase 2: Record Processing Loop (2000-PROCESS-RECORDS)
For each employee record:
- Record Input (2100-READ-EMPLOYEE-RECORD)
    - Reads next employee record from sequential file
    - Increments record counter
    - Sets end-of-file flag when appropriate
- Data Validation (2200-VALIDATE-RECORD)
    - Calls SLRYVAL subprogram for comprehensive validation
    - Validates employee ID format (must start with 'EMP')
    - Checks salary range (20K - 500K)
    - Validates department codes, grades, and status
    - Returns validation status flag
- Salary Calculations (2300-CALCULATE-SALARY) 
    a) Bonus Calculation (2310-CALCULATE-BONUS)
    b) Progressive Tax Calculation (2320-CALCULATE-TAX)
    c) Net Salary Calculation (2330-CALCULATE-NET-SALARY)
- External Data Lookups 
    a) DB2 Salary Statistics (2350-DB2-SALARY-LOOKUP)
       - Executes SQL query to get department average salary
       - Uses embedded SQL with host variables
       - Handles SQL error codes
    b) IMS Benefits Lookup (2360-IMS-BENEFIT-LOOKUP)
       - Calls IMS DL/I interface (CBLTDLI)
       - Retrieves employee benefits information
       - Uses Program Communication Block (PCB)
- VSAM History Update (2400-UPDATE-VSAM)
    - Reads existing salary history by employee ID
        If found: Updates 12-month rolling history
        If not found: Creates new history record
    - Handles VSAM status codes (00=success, 23=not found)
- Output Generation 
    a) Payroll File Output (2500-WRITE-PAYROLL)
       - Formats payroll record with calculated values
       - Writes to sequential output file
       - Tracks write errors
    b) MQ Message (2600-SEND-MQ-MESSAGE)
       - Creates formatted message with employee ID and net salary
       - Sends to MQ queue for real-time processing
       - Handles MQ return codes
## Phase 3: Finalization (8000-FINALIZE)
- File Closure (8100-CLOSE-FILES)
    - Closes all opened files
- Ensures data integrity
- Statistics Reporting (8200-PRINT-STATISTICS)
    - Displays processing counters
    - Shows error counts
    - Cleanup Processing (8300-CLEANUP)
    - Calls SLRYEXIT subprogram
    - Generates detailed statistics report
    - Performs final cleanup operations
## Subprogram Details
SLRYPARM - Parameter Reader
    - Reads system parameters from PARMLIB file
    - Supports parameters: MINSALRY, MAXSALRY, BONUSFCT, OVERTIME
    - Provides default values if parameter file unavailable
    - Returns status code to main program
SLRYVAL - Record Validator
    - Comprehensive employee record validation
    - Validates ID format, name, department, salary, grade, dates, status
    - Accumulates multiple validation errors
    - Returns detailed error information
SLRYEXIT - Cleanup and Reporting
- Generates formatted statistics report
- Creates timestamped processing summary
- Writes statistics to external file
- Sets final return code based on error counts
- Error Handling Strategy
- File Status Monitoring
- All file operations check status codes
- Declaratives handle file errors automatically
- Database Error Handling
- DB2: SQLCODE checking after each SQL operation
- IMS: Status code validation after DL/I calls
- Validation Error Accumulation
- Continues processing after validation errors
- Accumulates error counts for reporting
- Recovery Procedures
- VSAM errors trigger specialized error handlers
- File errors can halt processing or continue with warnings
Data Flow Summary (see Augment)
- This comprehensive workflow demonstrates typical mainframe batch processing patterns with multiple data sources, complex business logic, and extensive error handling - making it an excellent test case for COBOL to Java conversion tools.
