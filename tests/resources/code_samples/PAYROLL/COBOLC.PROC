//COBOLC   PROC MEMBER=TEMPNAME,
//         CLIB='PAYROLL.COPY.LIB',
//         LKED='IGY.SIGYCOMP',
//         OBJLIB='PAYROLL.OBJ.LIB',
//         LOADLIB='PAYROLL.LOAD.LIB'
//*================================================================
//* CATALOGED PROCEDURE FOR COBOL COMPILATION
//* INCLUDES PRECOMPILE, COMPILE, AND LINK-EDIT STEPS
//* AUTHOR: SYSTEMS PROGRAMMING TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//*----------------------------------------------------------------
//* DB2 PRECOMPILE STEP (FOR PROGRAMS WITH EMBEDDED SQL)
//*----------------------------------------------------------------
//PRECOMP  EXEC PGM=DSNHPC,REGION=2048K,
//         PARM='HOST(COB2),APOST,STDSQL(YES)'
//STEPLIB  DD DSN=DB2.V12.SDSNLOAD,DISP=SHR
//SYSLIB   DD DSN=&CLIB,DISP=SHR
//         DD DSN=DB2.V12.SDSNMACS,DISP=SHR
//SYSIN    DD DSN=PAYROLL.SOURCE.LIB(&MEMBER),DISP=SHR
//SYSCIN   DD DSN=&&DSNHOUT,DISP=(NEW,PASS),UNIT=SYSDA,
//         SPACE=(CYL,(5,5)),
//         DCB=(RECFM=FB,LRECL=80,BLKSIZE=0)
//SYSPRINT DD SYSOUT=*
//SYSTERM  DD SYSOUT=*
//SYSUDUMP DD SYSOUT=*
//DBRMLIB  DD DSN=PAYROLL.DBRMLIB(&MEMBER),DISP=SHR
//*
//*----------------------------------------------------------------
//* COBOL COMPILE STEP
//*----------------------------------------------------------------
//COBOL    EXEC PGM=IGYCRCTL,REGION=2048K,
//         PARM='LIB,APOST,XREF,NOCOMPILE(S),MAP,LIST,OPTIMIZE'
//STEPLIB  DD DSN=&LKED,DISP=SHR
//SYSLIB   DD DSN=&CLIB,DISP=SHR
//         DD DSN=SYS1.COBCOPY,DISP=SHR
//         DD DSN=DB2.V12.SDSNMACS,DISP=SHR
//         DD DSN=IMS.V15.MACLIB,DISP=SHR
//         DD DSN=MQ.V9.SCSQMACS,DISP=SHR
//SYSLIN   DD DSN=&&LOADSET,DISP=(MOD,PASS),UNIT=SYSDA,
//         SPACE=(TRK,(3,3)),
//         DCB=(RECFM=FB,LRECL=80,BLKSIZE=0)
//SYSPRINT DD SYSOUT=*
//SYSTERM  DD SYSOUT=*
//SYSUT1   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT2   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT3   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT4   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT5   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT6   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT7   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT8   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT9   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT10  DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT11  DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT12  DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT13  DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT14  DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSUT15  DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSIN    DD DSN=&&DSNHOUT,DISP=(OLD,DELETE)
//*
//*----------------------------------------------------------------
//* LINK-EDIT STEP
//*----------------------------------------------------------------
//LKED     EXEC PGM=HEWLDRGO,REGION=1024K,
//         PARM='MAP,LIST,LET,XREF,RENT,REUS',
//         COND=(8,LT,COBOL)
//STEPLIB  DD DSN=&LKED,DISP=SHR
//SYSLIN   DD DSN=&&LOADSET,DISP=(OLD,DELETE)
//         DD DDNAME=SYSIN
//SYSLMOD  DD DSN=&LOADLIB(&MEMBER),DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSUT1   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
//SYSIN    DD *
  INCLUDE OBJECT(DSNELI)
  INCLUDE OBJECT(DSNHLI)
  INCLUDE OBJECT(DSNHLI2)
  INCLUDE OBJECT(CSQBSTUB)
  INCLUDE OBJECT(CSQBRRSI)
  MODE AMODE(31),RMODE(ANY)
  NAME &MEMBER(R)
/*
//        PEND