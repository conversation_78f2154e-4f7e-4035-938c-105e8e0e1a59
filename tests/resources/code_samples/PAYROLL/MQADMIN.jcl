//MQADMIN  JOB (ACCT),'MQ ADMIN',CLASS=A,MSGCLASS=H,
//         MSGLEVEL=(1,1),TIME=(0,10),REGION=1M,
//         NOTIFY=&SYSUID
//*================================================================
//* ACME CORPORATION PAYROLL SYSTEM
//* MQ SERIES ADMINISTRATION JOB
//* AUTHOR: MIDDLEWARE TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//*----------------------------------------------------------------
//* STEP010: EXECUTE MQ SCRIPT COMMANDS
//*----------------------------------------------------------------
//STEP010  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM'
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//         DD DSN=MQ.V9.SCSQAUTH,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(MQSCRIPT)
/*
//MQSCRIPT DD DSN=PAYROLL.MQ.MQSC.LIB(PAYROLL),DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP020: DISPLAY QUEUE MANAGER STATUS
//*----------------------------------------------------------------
//STEP020  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM'
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  DISPLAY QMGR
  DISPLAY QMGR TYPE(ALL)
  DISPLAY USAGE PSID(*)
  DISPLAY USAGE TYPE(ALL)
  PING QMGR
/*
//*
//*----------------------------------------------------------------
//* STEP030: DISPLAY QUEUE STATUS
//*----------------------------------------------------------------
//STEP030  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM'
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  DISPLAY QUEUE(PAYROLL.*) TYPE(QUEUE) ALL
  DISPLAY QSTATUS(PAYROLL.*) TYPE(QUEUE) ALL
  DISPLAY QUEUE(SYSTEM.*) TYPE(QUEUE) CURDEPTH
/*
//*
//*----------------------------------------------------------------
//* STEP040: DISPLAY CHANNEL STATUS
//*----------------------------------------------------------------
//STEP040  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM'
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  DISPLAY CHANNEL(*) TYPE(ALL)
  DISPLAY CHSTATUS(*) ALL
  DISPLAY LSSTATUS(*) ALL
/*
//*
//*----------------------------------------------------------------
//* STEP050: DISPLAY PROCESS AND SECURITY
//*----------------------------------------------------------------
//STEP050  EXEC PGM=CSQUTIL,PARM='PAYROLL.QM'
//STEPLIB  DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  COMMAND DDNAME(CMDIN)
/*
//CMDIN    DD *
  DISPLAY PROCESS(PAYROLL.*) TYPE(ALL)
  DISPLAY CONN(*) TYPE(CONN) ALL
  DISPLAY SECURITY TYPE(ALL)
/*