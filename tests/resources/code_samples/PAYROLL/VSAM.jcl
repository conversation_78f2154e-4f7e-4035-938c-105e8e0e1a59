//DEFVSAM  JOB (ACCT),'VSAM SETUP',CLASS=A,MSGCLASS=H,
//         MSGLEVEL=(1,1),TIME=(0,10),REGION=1M,
//         NOTIFY=&SYSUID
//*================================================================
//* ACME CORPORATION PAYROLL SYSTEM
//* VSAM CLUSTER DEFINITION AND UTILITIES
//* AUTHOR: DATABASE ADMINISTRATION TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//*----------------------------------------------------------------
//* STEP010: DELETE EXISTING VSAM CLUSTER (IF EXISTS)
//*----------------------------------------------------------------
//STEP010  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  DELETE PAYROLL.EMPLOYEE.VSAM CLUSTER PURGE
  DELETE PAYROLL.SALARY.HISTORY.VSAM CLUSTER PURGE
  DELETE PAYROLL.DEPARTMENT.VSAM CLUSTER PURGE
  SET MAXCC = 0
/*
//*
//*----------------------------------------------------------------
//* STEP020: DEFINE EMPLOYEE MASTER VSAM CLUSTER
//*----------------------------------------------------------------
//STEP020  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  DEFINE CLUSTER (NAME(PAYROLL.EMPLOYEE.VSAM) -
                 TRACKS(50 25) -
                 RECORDSIZE(150 150) -
                 KEYS(10 0) -
                 INDEXED -
                 REUSE -
                 FREESPACE(20 10) -
                 SHAREOPTIONS(2 3) -
                 SPEED -
                 BUFFERSPACE(32768)) -
         DATA (NAME(PAYROLL.EMPLOYEE.VSAM.DATA) -
               CONTROLINTERVALSIZE(4096) -
               KEYS(10 0)) -
         INDEX (NAME(PAYROLL.EMPLOYEE.VSAM.INDEX) -
                CONTROLINTERVALSIZE(1024))

  IF LASTCC = 0 THEN -
    LISTCAT ENTRIES(PAYROLL.EMPLOYEE.VSAM) ALL
/*
//*
//*----------------------------------------------------------------
//* STEP030: DEFINE SALARY HISTORY VSAM CLUSTER
//* NOTE: FOR FUTURE USE - NOT CURRENTLY REFERENCED IN COBOL
//*----------------------------------------------------------------
//STEP030  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  DEFINE CLUSTER (NAME(PAYROLL.SALARY.HISTORY.VSAM) -
                 TRACKS(100 50) -
                 RECORDSIZE(200 200) -
                 KEYS(18 0) -
                 INDEXED -
                 REUSE -
                 FREESPACE(15 5) -
                 SHAREOPTIONS(2 3) -
                 SPEED -
                 BUFFERSPACE(65536)) -
         DATA (NAME(PAYROLL.SALARY.HISTORY.VSAM.DATA) -
               CONTROLINTERVALSIZE(8192) -
               KEYS(18 0)) -
         INDEX (NAME(PAYROLL.SALARY.HISTORY.VSAM.INDEX) -
                CONTROLINTERVALSIZE(2048))

  IF LASTCC = 0 THEN -
    LISTCAT ENTRIES(PAYROLL.SALARY.HISTORY.VSAM) ALL
/*
//*
//*----------------------------------------------------------------
//* STEP040: DEFINE DEPARTMENT VSAM CLUSTER
//* NOTE: FOR FUTURE USE - NOT CURRENTLY REFERENCED IN COBOL
//*----------------------------------------------------------------
//STEP040  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  DEFINE CLUSTER (NAME(PAYROLL.DEPARTMENT.VSAM) -
                 TRACKS(10 5) -
                 RECORDSIZE(80 80) -
                 KEYS(3 0) -
                 INDEXED -
                 REUSE -
                 FREESPACE(30 15) -
                 SHAREOPTIONS(2 3) -
                 SPEED -
                 BUFFERSPACE(16384)) -
         DATA (NAME(PAYROLL.DEPARTMENT.VSAM.DATA) -
               CONTROLINTERVALSIZE(2048) -
               KEYS(3 0)) -
         INDEX (NAME(PAYROLL.DEPARTMENT.VSAM.INDEX) -
                CONTROLINTERVALSIZE(1024))

  IF LASTCC = 0 THEN -
    LISTCAT ENTRIES(PAYROLL.DEPARTMENT.VSAM) ALL
/*
//*
//*----------------------------------------------------------------
//* STEP050: LOAD DEPARTMENT REFERENCE DATA
//*----------------------------------------------------------------
//STEP050  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  REPRO INFILE(DEPTIN) OUTDATASET(PAYROLL.DEPARTMENT.VSAM)
/*
//DEPTIN   DD DSN=PAYROLL.DEPARTMENT.DATA,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP060: CREATE ALTERNATE INDEX FOR EMPLOYEE NAME
//*----------------------------------------------------------------
//STEP060  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  DEFINE ALTERNATEINDEX (NAME(PAYROLL.EMPLOYEE.AIX.NAME) -
                        RELATE(PAYROLL.EMPLOYEE.VSAM) -
                        KEYS(30 10) -
                        TRACKS(20 10) -
                        REUSE -
                        FREESPACE(20 10) -
                        SHAREOPTIONS(2 3) -
                        UPGRADE) -
         DATA (NAME(PAYROLL.EMPLOYEE.AIX.NAME.DATA)) -
         INDEX (NAME(PAYROLL.EMPLOYEE.AIX.NAME.INDEX))

  IF LASTCC = 0 THEN -
    DEFINE PATH (NAME(PAYROLL.EMPLOYEE.PATH.NAME) -
                PATHENTRY(PAYROLL.EMPLOYEE.AIX.NAME) -
                UPDATE)
/*
//*
//*----------------------------------------------------------------
//* STEP070: VERIFY VSAM CLUSTER INTEGRITY
//*----------------------------------------------------------------
//STEP070  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  VERIFY DATASET(PAYROLL.EMPLOYEE.VSAM)
  VERIFY DATASET(PAYROLL.SALARY.HISTORY.VSAM)
  VERIFY DATASET(PAYROLL.DEPARTMENT.VSAM)

  PRINT INFILE(DEPT) COUNT(10)
/*
//DEPT     DD DSN=PAYROLL.DEPARTMENT.VSAM,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP080: BACKUP VSAM CLUSTERS TO TAPE
//*----------------------------------------------------------------
//STEP080  EXEC PGM=IDCAMS
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  EXPORT PAYROLL.EMPLOYEE.VSAM OUTFILE(BACKUP1) TEMPORARY
  EXPORT PAYROLL.SALARY.HISTORY.VSAM OUTFILE(BACKUP2) TEMPORARY
  EXPORT PAYROLL.DEPARTMENT.VSAM OUTFILE(BACKUP3) TEMPORARY
/*
//BACKUP1  DD DSN=PAYROLL.EMPLOYEE.BACKUP(+1),
//         DISP=(NEW,CATLG,DELETE),
//         UNIT=TAPE,
//         DCB=(RECFM=FB,LRECL=4096,BLKSIZE=32760)
//BACKUP2  DD DSN=PAYROLL.SALARY.BACKUP(+1),
//         DISP=(NEW,CATLG,DELETE),
//         UNIT=TAPE,
//         DCB=(RECFM=FB,LRECL=4096,BLKSIZE=32760)
//BACKUP3  DD DSN=PAYROLL.DEPT.BACKUP(+1),
//         DISP=(NEW,CATLG,DELETE),
//         UNIT=TAPE,
//         DCB=(RECFM=FB,LRECL=4096,BLKSIZE=32760)