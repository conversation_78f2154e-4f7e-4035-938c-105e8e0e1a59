      *================================================================
      * PROC - CATALOGED PROCEDURE FOR COMPILATION
      *================================================================
      /*
      //COBOLC   PROC MEMBER=TEMPNAME,
      //         CLIB='PAYROLL.COPY.LIB',
      //         LKED='IGY.SIGYCOMP'
      //*
      //COBOL    EXEC PGM=IGYCRCTL,REGION=2048K,PARM='LIB,APOST'
      //STEPLIB  DD DSN=&LKED,DISP=SHR
      //SYSLIB   DD DSN=&CLIB,DISP=SHR
      //         DD DSN=SYS1.COBCOPY,DISP=SHR
      //SYSLIN   DD DSN=&&LOADSET,DISP=(MOD,PASS),UNIT=SYSDA,
      //         SPACE=(TRK,(3,3))
      //SYSPRINT DD SYSOUT=*
      //SYSTERM  DD SYSOUT=*
      //SYSUT1   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSUT2   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSUT3   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSUT4   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSUT5   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSUT6   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSUT7   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSIN    DD DSN=PAYROLL.SOURCE.LIB(&MEMBER),DISP=SHR
      //*
      //LKED     EXEC PGM=HEWLDRGO,REGION=1024K,PARM='MAP,LIST,LET',
      //         COND=(8,LT,COBOL)
      //STEPLIB  DD DSN=&LKED,DISP=SHR
      //SYSLIN   DD DSN=&&LOADSET,DISP=(OLD,DELETE)
      //         DD DDNAME=SYSIN
      //SYSLMOD  DD DSN=PAYROLL.LOAD.LIB(&MEMBER),DISP=SHR
      //SYSPRINT DD SYSOUT=*
      //SYSUT1   DD UNIT=SYSDA,SPACE=(CYL,(1,1))
      //SYSIN    DD DUMMY
      //        PEND
      /*

