*================================================================
* DBD - DATABASE DESCRIPTION FOR EMPLOYEE BENEFITS
* MEMBER: EMPBENDB.DBD
* AUTHOR: DAT<PERSON>ASE ADMINISTRATION TEAM
* DATE: 01/15/2024
*================================================================
DBD    NAME=EMPBENDB,ACCESS=HIDAM
DATASET DD1=EMPBEN01,DEVICE=3390,SIZE=4096
DATASET DD2=EMPBEN02,DEVICE=3390,SIZE=4096
*
* ROOT SEGMENT - EMPLOYEE BENEFITS
SEGM   NAME=EMPBEN,PARENT=0,BYTES=48,
       POINTER=TB,RULES=(LLL,DDD)
FIELD  NAME=(EMPBENKEY,SEQ,U),BYTES=10,START=1,TYPE=C
FIELD  NAME=EMPID,BYTES=10,START=1,TYPE=C
FIELD  NAME=HEALTHINS,BYTES=8,START=11,TYPE=P
FIELD  NAME=DENTALINS,BYTES=6,START=19,TYPE=P
FIELD  NAME=RETIREMENT,BYTES=8,START=25,TYPE=P
FIELD  NAME=LIFEINS,BYTES=6,START=33,TYPE=P
FIELD  NAME=EFFDATE,BYTES=8,START=39,TYPE=C
FIELD  NAME=STATUS,BYTES=1,START=47,TYPE=C
FIELD  NAME=FILLER,BYTES=1,START=48,TYPE=C
*
DBDGEN
FINISH
END