*================================================================
* DBD - DATABASE DESCRIPTION FOR EMPLOYEE TAX DATA
* MEMBER: EMPTAXDB.DBD
* AUTHOR: DATABASE ADMINISTRATION TEAM
* DATE: 01/15/2024
*================================================================
DBD    NAME=EMPTAXDB,ACCESS=HIDAM
DATASET DD1=EMPTAX01,DEVICE=3390,SIZE=8192
DATASET DD2=EMPTAX02,DEVICE=3390,SIZE=8192
*
* ROOT SEGMENT - EMPLOYEE TAX MASTER
SEGM   NAME=EMPTAX,PARENT=0,BYTES=32,
       POINTER=TB,RULES=(LLL,DDD)
FIELD  NAME=(EMPTAXKEY,SEQ,U),BYTES=12,START=1,TYPE=C
FIELD  NAME=EMPID,BYTES=10,START=1,TYPE=C
FIELD  NAME=TAXYEAR,BYTES=2,START=11,TYPE=P
FIELD  NAME=TOTALTAX,BYTES=8,START=13,TYPE=P
FIELD  NAME=TOTALDED,BYTES=8,START=21,TYPE=P
FIELD  NAME=LASTUPD,BYTES=4,START=29,TYPE=P
*
* CHILD SEGMENT - FEDERAL TAX DETAILS
SEGM   NAME=FEDTAX,PARENT=EMPTAX,BYTES=24,
       POINTER=TB,RULES=(HERE,LAST)
FIELD  NAME=(FEDTAXKEY,SEQ,U),BYTES=14,START=1,TYPE=C
FIELD  NAME=EMPID,BYTES=10,START=1,TYPE=C
FIELD  NAME=TAXYEAR,BYTES=2,START=11,TYPE=P
FIELD  NAME=TAXTYPE,BYTES=2,START=13,TYPE=C
FIELD  NAME=FEDRATE,BYTES=4,START=15,TYPE=P
FIELD  NAME=FEDAMT,BYTES=6,START=19,TYPE=P
*
* CHILD SEGMENT - STATE TAX DETAILS
SEGM   NAME=STTAX,PARENT=EMPTAX,BYTES=28,
       POINTER=TB,RULES=(HERE,LAST)
FIELD  NAME=(STTAXKEY,SEQ,U),BYTES=16,START=1,TYPE=C
FIELD  NAME=EMPID,BYTES=10,START=1,TYPE=C
FIELD  NAME=TAXYEAR,BYTES=2,START=11,TYPE=P
FIELD  NAME=STATECODE,BYTES=2,START=13,TYPE=C
FIELD  NAME=TAXTYPE,BYTES=2,START=15,TYPE=C
FIELD  NAME=STRATE,BYTES=4,START=17,TYPE=P
FIELD  NAME=STAMT,BYTES=6,START=21,TYPE=P
FIELD  NAME=EXEMPTIONS,BYTES=2,START=27,TYPE=P
*
DBDGEN
FINISH
END