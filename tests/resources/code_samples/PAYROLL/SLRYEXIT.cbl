*================================================================
      * SUBPROGRAM SLRYEXIT.CBL - CLEANUP ROUTINE
      * Called by: SLRYCALC main program
      * Purpose: Final cleanup and statistics reporting
      * Last Modified: 01/15/2024
      *================================================================
       IDENTIFICATION DIVISION.
       PROGRAM-ID. SLRYEXIT.
       AUTHOR. PAYROLL-SYSTEM.
       DATE-WRITTEN. 01/15/2024.

       ENVIRONMENT DIVISION.
       INPUT-OUTPUT SECTION.
       FILE-CONTROL.
           SELECT STATS-FILE ASSIGN TO STATSRPT
               ORGANIZATION IS SEQUENTIAL
               ACCESS MODE IS SEQUENTIAL
               FILE STATUS IS WS-STATS-STATUS.

       DATA DIVISION.
       FILE SECTION.
       FD  STATS-FILE
           RECORDING MODE IS F
           LABEL RECORDS ARE STANDARD
           RECORD CONTAINS 133 CHARACTERS
           DATA RECORD IS STATS-RECORD.

       01  STATS-RECORD                PIC X(133).

       WORKING-STORAGE SECTION.
       01  WS-STATS-STATUS             PIC XX.
       
       01  WS-REPORT-LINES.
           05  WS-HEADER-LINE1.
               10  FILLER              PIC X(50) VALUE
                   'ACME CORPORATION PAYROLL PROCESSING STATISTICS'.
               10  FILLER              PIC X(83) VALUE SPACES.
           05  WS-HEADER-LINE2.
               10  FILLER              PIC X(15) VALUE 'RUN DATE: '.
               10  WS-RUN-DATE         PIC X(10).
               10  FILLER              PIC X(10) VALUE '   TIME: '.
               10  WS-RUN-TIME         PIC X(08).
               10  FILLER              PIC X(90) VALUE SPACES.
           05  WS-DETAIL-LINE.
               10  WS-LABEL            PIC X(30).
               10  FILLER              PIC X(05) VALUE ': '.
               10  WS-VALUE            PIC Z,ZZZ,ZZ9.
               10  FILLER              PIC X(90) VALUE SPACES.

       01  WS-WORK-FIELDS.
           05  WS-CURRENT-DATE-TIME.
               10  WS-CURR-DATE        PIC 9(08).
               10  WS-CURR-TIME        PIC 9(06).
           05  WS-FORMATTED-DATE.
               10  WS-MONTH            PIC 99.
               10  FILLER              PIC X VALUE '/'.
               10  WS-DAY              PIC 99.
               10  FILLER              PIC X VALUE '/'.
               10  WS-YEAR             PIC 9999.

      * Dead code - old statistics format
       01  WS-OLD-STATS-FORMAT.
           05  WS-OLD-HEADER           PIC X(80).
           05  WS-OLD-DETAIL-FORMAT    PIC X(60).
           05  WS-OLD-TOTAL-LINE       PIC X(70).

       LINKAGE SECTION.
       01  LS-COUNTERS.
           05  LS-RECORD-COUNT         PIC 9(06).
           05  LS-ERROR-COUNT          PIC 9(04).
           05  LS-LOOP-COUNT           PIC 9(03).

       PROCEDURE DIVISION USING LS-COUNTERS.

       0000-MAIN-SECTION SECTION.
       0000-MAIN-PARA.
           PERFORM 1000-INITIALIZE
           PERFORM 2000-WRITE-STATISTICS
           PERFORM 3000-DISPLAY-SUMMARY
           PERFORM 9000-CLEANUP
           GOBACK.

       1000-INITIALIZE SECTION.
       1000-INIT-PARA.
           DISPLAY 'SLRYEXIT: FINAL CLEANUP AND REPORTING'
           
           ACCEPT WS-CURRENT-DATE-TIME FROM DATE-TIME
           
           MOVE WS-CURR-DATE(5:2) TO WS-MONTH
           MOVE WS-CURR-DATE(7:2) TO WS-DAY  
           MOVE WS-CURR-DATE(1:4) TO WS-YEAR
           MOVE WS-FORMATTED-DATE TO WS-RUN-DATE
           
           MOVE WS-CURR-TIME(1:2) TO WS-RUN-TIME(1:2)
           MOVE ':' TO WS-RUN-TIME(3:1)
           MOVE WS-CURR-TIME(3:2) TO WS-RUN-TIME(4:2)
           MOVE ':' TO WS-RUN-TIME(6:1)
           MOVE WS-CURR-TIME(5:2) TO WS-RUN-TIME(7:2)
           
           OPEN OUTPUT STATS-FILE
           IF WS-STATS-STATUS NOT = '00'
               DISPLAY 'WARNING: CANNOT OPEN STATISTICS FILE'
               DISPLAY 'STATUS: ' WS-STATS-STATUS
           END-IF.

       2000-WRITE-STATISTICS SECTION.
       2000-WRITE-STATS-PARA.
           IF WS-STATS-STATUS = '00'
               PERFORM 2100-WRITE-HEADERS
               PERFORM 2200-WRITE-COUNTS
           END-IF.

       2100-WRITE-HEADERS SECTION.
       2100-HEADERS-PARA.
           WRITE STATS-RECORD FROM WS-HEADER-LINE1
           WRITE STATS-RECORD FROM WS-HEADER-LINE2
           MOVE SPACES TO STATS-RECORD
           WRITE STATS-RECORD.

       2200-WRITE-COUNTS SECTION.
       2200-COUNTS-PARA.
           MOVE 'RECORDS PROCESSED' TO WS-LABEL
           MOVE LS-RECORD-COUNT TO WS-VALUE
           WRITE STATS-RECORD FROM WS-DETAIL-LINE
           
           MOVE 'ERRORS ENCOUNTERED' TO WS-LABEL
           MOVE LS-ERROR-COUNT TO WS-VALUE
           WRITE STATS-RECORD FROM WS-DETAIL-LINE
           
           MOVE 'PROCESSING LOOPS' TO WS-LABEL
           MOVE LS-LOOP-COUNT TO WS-VALUE
           WRITE STATS-RECORD FROM WS-DETAIL-LINE.

       3000-DISPLAY-SUMMARY SECTION.
       3000-SUMMARY-PARA.
           DISPLAY 'FINAL PROCESSING SUMMARY:'
           DISPLAY 'RECORDS PROCESSED: ' LS-RECORD-COUNT
           DISPLAY 'ERRORS ENCOUNTERED: ' LS-ERROR-COUNT
           DISPLAY 'PROCESSING LOOPS: ' LS-LOOP-COUNT
           
           IF LS-ERROR-COUNT > 0
               DISPLAY 'WARNING: ERRORS OCCURRED DURING PROCESSING'
               MOVE 4 TO RETURN-CODE
           ELSE
               DISPLAY 'PROCESSING COMPLETED SUCCESSFULLY'
               MOVE 0 TO RETURN-CODE
           END-IF.

       9000-CLEANUP SECTION.
       9000-CLEANUP-PARA.
           IF WS-STATS-STATUS = '00'
               CLOSE STATS-FILE
           END-IF
           
           DISPLAY 'SLRYEXIT: CLEANUP COMPLETE'.