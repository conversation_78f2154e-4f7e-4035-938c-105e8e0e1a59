//SLRYCALC JOB (ACCT),'SALARY CALC',CLASS=A,MSGCLASS=H,
//         MSGLEVEL=(1,1),TIME=(0,30),REGION=4M,
//         NOTIFY=&SYSUID
//*================================================================
//* ACME CORPORATION PAYROLL SYSTEM
//* MAIN SALARY CALCULATION JOB
//* AUTHOR: PAYROLL TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//JOBLIB   DD DSN=PAYROLL.LOAD.LIB,DISP=SHR
//*
//*----------------------------------------------------------------
//* STEP010: MAIN SALARY CALCULATION PROCESSING
//*----------------------------------------------------------------
//STEP010  EXEC PGM=SLRYCALC,PARM='PROD'
//STEPLIB  DD DSN=PAYROLL.LOAD.LIB,DISP=SHR
//         DD DSN=DB2.V12.SDSNLOAD,DISP=SHR
//         DD DSN=IMS.V15.RESLIB,DISP=SHR
//         DD DSN=MQ.V9.SCSQLOAD,DISP=SHR
//         DD DSN=CEE.SCEERUN,DISP=SHR
//EMPFILE  DD DSN=PAYROLL.EMPLOYEE.DATA,DISP=SHR
//PAYFILE  DD DSN=PAYROLL.OUTPUT.DATA(+1),
//         DISP=(NEW,CATLG,DELETE),
//         SPACE=(TRK,(10,5),RLSE),
//         DCB=(RECFM=FB,LRECL=300,BLKSIZE=0),
//         UNIT=SYSDA
//EMPVSAM  DD DSN=PAYROLL.EMPLOYEE.VSAM,DISP=SHR
//PARMLIB  DD DSN=PAYROLL.PARMS.DATA,DISP=SHR
//STATSRPT DD DSN=PAYROLL.STATS.REPORT(+1),
//         DISP=(NEW,CATLG,DELETE),
//         SPACE=(TRK,(5,2),RLSE),
//         DCB=(RECFM=FB,LRECL=133,BLKSIZE=0),
//         UNIT=SYSDA
//SYSOUT   DD SYSOUT=*
//SYSPRINT DD SYSOUT=*
//SYSUDUMP DD SYSOUT=*
//SYSTSPRT DD SYSOUT=*
//SYSTSIN  DD DUMMY
//*
//*----------------------------------------------------------------
//* STEP020: ARCHIVE PAYROLL OUTPUT
//*----------------------------------------------------------------
//STEP020  EXEC PGM=IEBGENER,COND=(0,NE,STEP010)
//SYSPRINT DD SYSOUT=*
//SYSUT1   DD DSN=PAYROLL.OUTPUT.DATA(0),DISP=SHR
//SYSUT2   DD DSN=PAYROLL.ARCHIVE.DATA(+1),
//         DISP=(NEW,CATLG,DELETE),
//         LIKE=PAYROLL.OUTPUT.DATA,
//         UNIT=TAPE
//SYSIN    DD DUMMY
//*
//*----------------------------------------------------------------
//* STEP030: SORT PAYROLL OUTPUT BY DEPARTMENT AND NAME
//*----------------------------------------------------------------
//STEP030  EXEC PGM=SORT,COND=(0,NE,STEP010)
//SYSPRINT DD SYSOUT=*
//SORTIN   DD DSN=PAYROLL.OUTPUT.DATA(0),DISP=SHR
//SORTOUT  DD DSN=PAYROLL.OUTPUT.SORTED(+1),
//         DISP=(NEW,CATLG,DELETE),
//         SPACE=(TRK,(15,5),RLSE),
//         DCB=(RECFM=FB,LRECL=300,BLKSIZE=0),
//         UNIT=SYSDA
//SORTWORK DD UNIT=SYSDA,SPACE=(CYL,(10,10))
//SYSIN    DD *
  SORT FIELDS=(1,10,CH,A,41,30,CH,A)
  OUTREC FIELDS=(1,300)
/*
//*
//*----------------------------------------------------------------
//* STEP040: GENERATE DEPARTMENT SUMMARY REPORT
//*----------------------------------------------------------------
//STEP040  EXEC PGM=ICEMAN,COND=(0,NE,STEP030)
//SYSPRINT DD SYSOUT=*
//SORTIN   DD DSN=PAYROLL.OUTPUT.SORTED(0),DISP=SHR
//SORTOUT  DD DSN=PAYROLL.DEPT.SUMMARY(+1),
//         DISP=(NEW,CATLG,DELETE),
//         SPACE=(TRK,(5,2),RLSE),
//         DCB=(RECFM=FB,LRECL=133,BLKSIZE=0)
//SYSIN    DD *
  SORT FIELDS=(41,3,CH,A)
  SUM FIELDS=(71,9,ZD,80,9,ZD,89,8,ZD,97,8,ZD)
  OUTREC FIELDS=(1C'DEPT: ',41,3,5C' ',
                 15C'GROSS TOTAL: ',71,9,ZD,M11,LENGTH=11,
                 15C' NET TOTAL: ',80,9,ZD,M11,LENGTH=11)
/*
//*
//*----------------------------------------------------------------
//* STEP050: CLEANUP TEMPORARY DATASETS
//*----------------------------------------------------------------
//STEP050  EXEC PGM=IDCAMS,COND=(0,NE,STEP010)
//SYSPRINT DD SYSOUT=*
//SYSIN    DD *
  DELETE PAYROLL.TEMP.WORK1 PURGE
  DELETE PAYROLL.TEMP.WORK2 PURGE
  SET MAXCC = 0
/*
//*
//*----------------------------------------------------------------
//* STEP060: SEND COMPLETION NOTIFICATION
//*----------------------------------------------------------------
//STEP060  EXEC PGM=IEBGENER,COND=(0,NE,STEP010)
//SYSPRINT DD SYSOUT=*
//SYSUT1   DD *
PAYROLL PROCESSING COMPLETED SUCCESSFULLY
RUN DATE: &LYYMMDD
RUN TIME: &LTIME
RECORDS PROCESSED: SEE STATISTICS REPORT
/*
//SYSUT2   DD SYSOUT=(,INTRDR)
//SYSIN    DD DUMMY
