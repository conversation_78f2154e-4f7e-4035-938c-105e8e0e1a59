//COBOLCMP JOB (ACCT),'COMPILE PAYROLL',CLASS=A,MSGCLASS=H,
//         MSGLEVEL=(1,1),TIME=(0,15),REGION=2M,
//         NOTIFY=&SYSUID
//*================================================================
//* ACME CORPORATION PAYROLL SYSTEM
//* COBOL COMPILATION JOB FOR ALL MODULES
//* AUTHOR: DEVELOPMENT TEAM
//* DATE: 01/15/2024
//*================================================================
//*
//PROCLIB  DD DSN=PAYROLL.PROC.LIB,DISP=SHR
//*
//*----------------------------------------------------------------
//* COMPILE MAIN SALARY CALCULATION PROGRAM
//*----------------------------------------------------------------
//COMP001  EXEC PROC=COBOLC,MEMBER=SLRYCALC,
//         CLIB='PAYROLL.COPY.LIB'
//*
//*----------------------------------------------------------------
//* COMPILE PARAMETER READING SUBPROGRAM
//*----------------------------------------------------------------
//COMP002  EXEC PROC=COBOLC,MEMBER=SLRYPARM,
//         CLIB='PAYROLL.COPY.LIB'
//*
//*----------------------------------------------------------------
//* COMPILE VALIDATION SUBPROGRAM
//*----------------------------------------------------------------
//COMP003  EXEC PROC=COBOLC,MEMBER=SLRYVAL,
//         CLIB='PAYROLL.COPY.LIB'
//*
//*----------------------------------------------------------------
//* COMPILE CLEANUP/EXIT SUBPROGRAM
//*----------------------------------------------------------------
//COMP004  EXEC PROC=COBOLC,MEMBER=SLRYEXIT,
//         CLIB='PAYROLL.COPY.LIB'
//*
//*----------------------------------------------------------------
//* COMPILE UTILITY PROGRAMS
//*----------------------------------------------------------------
//COMP005  EXEC PROC=COBOLC,MEMBER=SLRYUTIL,
//         CLIB='PAYROLL.COPY.LIB'
//COMP006  EXEC PROC=COBOLC,MEMBER=SLRYREPT,
//         CLIB='PAYROLL.COPY.LIB'
//*
//*----------------------------------------------------------------
//* UPDATE LOAD LIBRARY DIRECTORY
//*----------------------------------------------------------------
//LISTMOD  EXEC PGM=IEHLIST,COND=(8,LT)
//SYSPRINT DD SYSOUT=*
//DD1      DD DSN=PAYROLL.LOAD.LIB,DISP=SHR
//SYSIN    DD *
  LISTPDS DSNAME=PAYROLL.LOAD.LIB,FORMAT
/*
//*
//*----------------------------------------------------------------
//* BACKUP COMPILED LOAD MODULES
//*----------------------------------------------------------------
//BACKUP   EXEC PGM=IEBCOPY,COND=(8,LT)
//SYSPRINT DD SYSOUT=*
//IN       DD DSN=PAYROLL.LOAD.LIB,DISP=SHR
//OUT      DD DSN=PAYROLL.LOAD.BACKUP(+1),
//         DISP=(NEW,CATLG,DELETE),
//         LIKE=PAYROLL.LOAD.LIB,
//         UNIT=TAPE
//SYSIN    DD *
  COPY INDD=IN,OUTDD=OUT
/*