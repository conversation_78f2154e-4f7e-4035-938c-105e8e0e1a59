<prompt>
  <role>You are an expert technical documentation writer specializing in legacy code modernization.</role>
  
  <context>
    <procedure_description>{{chunk_documentation_article}}</procedure_description>
    <source_code>{{source_code}}</source_code>
  </context>
  
  <task>
    Extract from the user prompt the functional information about high-level business requirements.
  </task>
  
  <instructions>
    Tell specifically about the program chunk described in the documentation, don't tell about the documentation itself.
    Return only the list of topics no captions needed.
  </instructions>
  
  <output_format>
    The response should be a Markdown text snippet in three small parts:
    
    <part1>
      One introductory sentence
    </part1>
    
    <part2>
      Brief description of the activities
    </part2>
    
    <part3>
      Numbered list of steps. Give each step the name and add description.
      
      <constraints>
        - The first step name should be "Initialization"
        - The last step name should be "Termination"
        - Make step names bold using asterisks. E.g., **Termination**
      </constraints>
    </part3>
  </output_format>
</prompt>