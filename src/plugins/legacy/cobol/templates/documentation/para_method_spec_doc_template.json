{"doc_root": {"document_divisions": [{"level_1": {"caption": "{{chunk_name}}", "content": "{{recap_table}}"}}, {"level_1": {"caption": "Table of Contents", "content": "{{document_contents}}"}}, {"level_1": {"caption": "Business description", "content": "{{business_description}}"}}, {"level_1": {"caption": "Function purpose", "content": "{{function_purpose}}"}}, {"level_1": {"caption": "Incoming Calls", "content": "{{incoming_calls_table}}"}}, {"level_1": {"caption": "Input parameters", "content": "{{input_parameters_table}}"}}, {"level_1": {"caption": "Outgoing Calls", "document_divisions": [{"level_2": {"caption": "Programs", "content": "{{outgoing_calls_table}}"}}, {"level_2": {"caption": "Functions", "content": "{{outgoing_performs_table}}"}}]}}, {"level_1": {"caption": "Output Parameters", "content": "{{output_parameters_table}}"}}, {"level_1": {"caption": "Database Interactions", "document_divisions": [{"level_2": {"caption": "DB2", "content": "{{all_db2_tables_table}}"}}, {"level_2": {"caption": "IMS", "content": "{{all_ims_tables_table}}"}}]}}, {"level_1": {"caption": "Business Process Flow", "content": "{{process_flow_step_sequence}}"}}, {"level_1": {"caption": "Step-by-Step Logic Flow", "content": "{{algorithm}}"}}, {"level_1": {"caption": "Validation Rules", "content": "{{validation_rules_table}}"}}, {"level_1": {"caption": "Erro<PERSON>", "content": "{{error_handling_table}}"}}, {"level_1": {"caption": "Test Cases", "content": "{{test_cases_table}}"}}]}}