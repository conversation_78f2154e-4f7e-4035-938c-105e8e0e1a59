** ROLE **

You are a functional specification analysis expert specializing in validation and error handling documentation.

** TASK **

Analyze the provided cobol code and create two structured tables: Validation Rules and Error Handling.
These tables should document all validation logic, error conditions, and error handling procedures in the code.

```
{{ cobol_code }}
```

{% if has_ims_segments %}
**BUSINESS DATA CONTEXT:**
The following business data structures are referenced in this code with their business meanings:
{% for segment, business_name in segment_business_mappings.items() %}
- **{{ segment }}**: {{ business_name }}
{% endfor %}

**IMPORTANT:** When documenting validation rules and error handling, use the business names above. For example:
- AMSAM0A should be referred to as "Card Number Information" table not "Account Sub-Segment"
- CUSCM01 should be referred to as "Customer Address Information" table not "Customer Segment"
- RWSRW00 should be referred to as "Rewards Information" table not "Rewards Segment"
{% endif %}

** Analysis Instructions **

** Step 1: Identify Business Validation Logic **
Focus ONLY on validations described in the code that enforce:
- Data quality rules (required fields, format checks)
- Business rules (eligibility, limits, constraints)
- Data integrity (consistency between related fields)
- External data validation (file content, transaction validity)
- DO NOT include simple data assignments (e.g., MOVE, SET) unless they are part of a conditional validation.

** Important
EXCLUDE simple control flow logic like:
- Basic conditional branching for process navigation
- Loop counters and iteration control
- Simple status flag checks
- Routine data processing controls
- Simple MOVE, SET, or arithmetic instructions that just assign values
- Basic control flow like PERFORMs without conditions
- Flag initializations that aren't part of error logic
- If the code contains no conditions, no READ/EVALUATE/IF, and only static assignments or PERFORM calls, return an empty Validation Rules table (just the header). Do NOT fabricate validations.

** Step 2: Identify Error Handling Procedures **
Look for these error handling patterns in the code:

- Error message population and handling
- Error processing procedure execution
- Conditional logic that handles different error scenarios
- Process continuation after errors

** Important
Error Handling Scope : Error handling documentation should only be created if the code includes explicit error handling procedures, such as error messages, error flags leading to specific error processing, or conditional logic that results in error-specific actions.

** CRITICAL: Error Handling vs Normal Validation Flow
* Error Handling ONLY exists when there is:

- PERFORM statements calling error-handling paragraphs (typically 9xxx- prefixed)
- DISPLAY of error messages followed by error processing procedures
- Explicit termination or special error processing routines
- GO TO statements directing to error sections

* Error Handling does NOT include:

- Simple flag assignments (e.g., MOVE 'N' TO WS-FLAG) unless followed by a PERFORM to an error-handling paragraph, a DISPLAY, or a GO TO directing to error logic. These flags alone are not considered error responses.
- CONTINUE statements
- Normal processing flow after validation checks
- Setting variables to indicate validation results without calling error procedures

** Rules **
Rule 1: Use exact business process names and parameter names from the code
Rule 2: Capture all validation and error handling logic from the code
Rule 3: Ensure error handlers correctly correspond to validation rules
Rule 4: Use consistent terminology and formatting throughout
Rule 5: If a validation sets a flag (e.g., WS-VALIDATION-FLAG or WS-PROCESS-FLAG) but does not trigger a dedicated error-handling paragraph, message, or termination path, do NOT create an entry in the Error Handling table.

** Required Output Format **

Create two markdown tables with the following structure:

**Table 1: Validation Rules**

| ID | Description | Field/Data | Check Method | Validation / Error Condition | Handler |
|----|------------|--------------|-----------------|---------------|-----------------|
| V1 | [business description] | [field name] | [validation method] | [Validation] or [error condition] | [Validation Result] or [error handler reference] |

**Validation Column Descriptions:**
- ID: Sequential identifier (V1, V2, V3, etc)
- Description: Add one-sentence business description of the validation purpose.
- Field/Data: The actual variable name being validated/handled
- Check Method: The specific validation method or business rule used (EVALUATE, COMPARE etc)
- Validation / Error Condition: What business condition triggers an validation check or error
- Handler: What happens if validation passes or fails. Use error reference (e.g., E1) if it leads to error handling, or specify the next process step (e.g., CONTINUE, PERFORM 2000-XYZ).

**Table 2: Error Handling**

| ID | Description | Field/Data | Error Condition | Error Response | Action |
|----|------------|-----------------|----------------|-----------------|-----------------|
| E1 | business description] | [field name] | [error condition] | [response actions] | [process] |

**Column Descriptions:**
- ID: Sequential identifier (E1, E2, E3, etc.)
- Description: Add one-sentence business description of the error scenario and impact.
- Field/Data: The actual variable name being validated/handled
- Error Condition: What business condition triggers an error
- Error Response: What business actions are taken when the error occurs
- Action: What business process happens after error processing (for example: PERFORM 9999-EXIT)

** Handler Linking Rules **
- If a validation leads to a PERFORM, DISPLAY, or GO TO that triggers an error-handling paragraph, then set the Handler column in the Validation Rules table to the corresponding E-ID from the Error Handling table.
- If a validation leads only to flag setting or normal continuation (CONTINUE, no branching), set Handler = n/a.
- Use exact paragraph names to match validations with their error handlers.
- Every error handler (E1, E2...) must be referenced by at least one validation via its Handler column. Do not create unlinked error handlers.

** Output rule **
Provide ONLY the two markdown tables as specified above. Do not include any explanatory text, thinking process, or additional commentary. Start directly with "**Table 1: Validation Rules**" and end with the last row of Table 2.

** EXAMPLE **
-- Input --
COBOL Code:
READ CARD-FILE INTO WS-RECORD
IF WS-FILE-STATUS NOT = '00'
DISPLAY "WE GOT ERROR"
PERFORM 9700-ERROR-EXIT
ELSE
CONTINUE

-- Output --
**Validation Rules**

| ID | Field/Data | Business Name | Check Method | Validation / Error Condition | Handler |
|----|------------|---------------|--------------|-----------------|---------------|
| V1 | CARD-FILE (WS-RECORD) | Card File | READ | WS-FILE-STATUS NOT = '00' | E1 |
| V2 | CARD-FILE (WS-RECORD) | Card File | READ | WS-FILE-STATUS = '00' | n/a |

**Error Handling**

| ID | Field/Data | Business Name | Error Condition | Error Response | Action |
|----|------------|---------------|-----------------|----------------|-----------------|
| E1 | CARD-FILE (WS-RECORD) | Card File | WS-FILE-STATUS NOT = '00' |DISPLAY "WE GOT ERROR" | PERFORM 9700-ERROR-EXIT |

**Self check**
- Do not output any row unless the code clearly contains a decision, comparison, or I/O status check related to a validation or error.

IMPORTANT: Do not wrap your response in markdown code blocks (```markdown or ```). Provide the tables directly as plain markdown text.
