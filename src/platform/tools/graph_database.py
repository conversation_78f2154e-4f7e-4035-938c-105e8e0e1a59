import logging
from src.platform.tools.data.kuzu import KuzuConnector
from typing import Any, List, Dict

class GraphNode(Any):
    """Abstract interface for graph database nodes"""
    pass

class GraphDatabase:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger("documentation_gen_agent")

    def __init__(self):
        self.connector = KuzuConnector()

    def connect(self) -> bool:
        return self.connector.connect()

    def disconnect(self) -> None:
        self.connector.disconnect()

    # def get_callable_leaves(self) -> list[dict[str, Any]] | None:
    def get_callable_leaves(self) -> list[dict[str, Any]] | None:
        if not self.connect():
            self.logger.error("Can't connect to the  database")
            return None
        result = self.connector.get_callable_leaves()
        self.disconnect()

        return result

    def get_direct_callers_and_performers_to_all(self, callee_uuids: list[str]) -> list[dict[str, Any]] | None:
        if not self.connect():
            self.logger.error("Can't connect to the  database")
            return None
        result = self.connector.get_direct_subjects("CALLS|PERFORMS", callee_uuids)
        self.disconnect()

        return result

    def get_direct_callers_and_performers_to_one(self, to_uuid: str):

        if not self.connect():
            self.logger.error("Can't connect to the  database")
            return None
        result = self.connector.get_direct_subjects("CALLS|PERFORMS", [to_uuid])
        self.disconnect()

        return result

    def get_direct_calls_from_one(self, from_uuid: str):

        if not self.connect():
            self.logger.error("Can't connect to the  database")
            return None
        result = self.connector.get_direct_objects("CALLS", [from_uuid])
        self.disconnect()

        return result

    def get_direct_performs_from_one(self, from_uuid: str):

        if not self.connect():
            self.logger.error("Can't connect to the  database")
            return None
        result = self.connector.get_direct_objects("PERFORMS", [from_uuid])
        self.disconnect()

        return result

    def get_node_by_uuid(self, uuid: str):
        if not self.connect():
            self.logger.error("Can't connect to the  database")
            return None

        result = self.connector.get_node_by_uuid(uuid)
        self.disconnect()

        return result
