"""
Core KnowledgeMinerAgent implementation.
Main orchestration and coordination logic for knowledge mining process.
"""
from typing import Dict, List, Any, Optional

from src.platform.agents.base_agent import BaseAgent, AgentInput, AgentOutput
from src.plugins.legacy.cobol.tools.dependency_analyzer import DependencyAnalyzer
from src.platform.tools.knowledge_database import KnowledgeDatabase
from src.platform.tools.utils.template_manager import get_template_manager
import llm_settings

from .parameter_extractor import ParameterExtractor
from .business_analyzer import BusinessAnalyzer
from .spec_generator import SpecGenerator
from .utils import KnowledgeMinerUtils


class KnowledgeMinerAgent(BaseAgent):
    """
    Agent responsible for extracting business meaning from COBOL code chunks,
    identifying variables, business logic, and annotating them with business context.
    Uses dependency-based analysis: builds dependency graph from JSON files, sorts topologically,
    and analyzes from leaves to root, accumulating business knowledge inductively.
    """

    def __init__(self):
        super().__init__("knowledge_miner")
        self.knowledge_db = KnowledgeDatabase()
        self.json_analyzer = DependencyAnalyzer()
        self.template_manager = get_template_manager()

        # Initialize specialized components
        self.parameter_extractor = ParameterExtractor(self.template_manager)
        self.business_analyzer = BusinessAnalyzer(self.template_manager)
        self.spec_generator = SpecGenerator(self.template_manager)
        self.utils = KnowledgeMinerUtils(self.knowledge_db)

    def set_up(self, config: dict) -> None:
        """
        Set up the agent with configuration

        Args:
            config: Configuration dictionary
        """
        pass

    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Process COBOL code chunks using dependency-based analysis:
        1. Build dependency graph from JSON files
        2. Sort topologically (leaves first)
        3. Analyze procedures in order, accumulating business knowledge

        Args:
            input_data: Input data containing working directory and knowledge base

        Returns:
            AgentOutput: Knowledge mining results
        """
        self.logger.info("Starting dependency-based knowledge mining process")

        try:
            # Initialize counters and trackers
            total_programs = 0
            total_chunks = 0
            analyzed_chunks = 0
            error_chunks = 0
            skipped_chunks = 0
            failed_chunks = []
            generated_docs = []

            # Get all COBOL programs from the database
            programs = self.utils.get_all_cobol_programs()
            total_programs = len(programs)
            self.logger.info(f"Found {total_programs} COBOL programs to analyze")

            # Process each program
            for program_idx, program in enumerate(programs):
                program_id = program["program_id"]
                self.report_progress(
                    f"Processing program {program_idx+1}/{total_programs}: {program_id}",
                    progress=(program_idx / total_programs)
                )

                # Get procedure chunks sorted by dependencies from JSON
                sorted_procedure_chunks = self.json_analyzer.get_sorted_chunks_for_program(
                    program_id, input_data.working_directory
                )
                total_chunks += len(sorted_procedure_chunks)

                if not sorted_procedure_chunks:
                    self.logger.info(f"No procedure chunks found for program {program_id}")
                    continue

                # Get all variables for this program
                program_variables = self.utils.get_program_variables(program_id)
                variable_dict = {var["name"]: var for var in program_variables}

                self.logger.info(f"Processing {len(sorted_procedure_chunks)} chunks for program {program_id} in topological order")

                # Analyze chunks in dependency order (already sorted)
                chunk_analysis_cache = {}  # Cache for analyzed chunks
                for sort_idx, chunk in enumerate(sorted_procedure_chunks):
                    chunk_name = chunk["chunk_name"]

                    self.report_progress(
                        f"Analyzing chunk {sort_idx+1}/{len(sorted_procedure_chunks)} in program {program_id}: {chunk_name}",
                        progress=((program_idx + (sort_idx / len(sorted_procedure_chunks))) / total_programs)
                    )

                    # Skip if already analyzed (check database)
                    existing_chunk = self.utils.get_chunk_from_database(program_id, chunk_name)
                    if existing_chunk and existing_chunk.get("analysis_status") in ["complete", "skipped"]:
                        if existing_chunk.get("analysis_status") == "skipped":
                            skipped_chunks += 1
                        else:
                            analyzed_chunks += 1
                            # Load existing analysis into cache
                            existing_analysis = self.utils.get_analysis_from_database(program_id, chunk_name)
                            if existing_analysis:
                                chunk_analysis_cache[chunk_name] = existing_analysis
                        continue

                    try:
                        # Extract code content from the chunk
                        code_content = chunk["code"]

                        # Find variables used in this chunk
                        used_variables = self.utils.find_variables_in_code(code_content, variable_dict)

                        # Check if it's an exit-only chunk
                        if self.utils.is_exit_only_chunk(code_content):
                            self.utils.mark_chunk_as_skipped(program_id, chunk_name, "Contains only EXIT logic")
                            self.logger.info(f"Skipping chunk {chunk_name} in program {program_id} - contains only EXIT logic")
                            skipped_chunks += 1
                            continue

                        # Get called procedures and their analysis from cache
                        referenced_chunks_analysis = self._extract_referenced_chunks_analysis(
                            code_content, chunk_analysis_cache, chunk_language
                        )

                        # Get language from chunk data or default to cobol for legacy knowledge miner
                        # TODO: In the future, get language from knowledge base/file info
                        chunk_language = chunk.get("language", "cobol")

                        # Analyze the chunk with dependency context
                        analysis_result = self._analyze_chunk_with_dependencies(
                            program_id=program_id,
                            chunk_name=chunk_name,
                            code_content=code_content,
                            used_variables=used_variables,
                            referenced_chunks_analysis=referenced_chunks_analysis,
                            language=chunk_language
                        )

                        # Save to database
                        self.utils.save_analysis_to_database(
                            program_id=program_id,
                            chunk_name=chunk_name,
                            analysis_result=analysis_result,
                            status="complete"
                        )

                        # Cache the analysis for dependent chunks
                        chunk_analysis_cache[chunk_name] = analysis_result

                        # Generate markdown documentation
                        doc_path = self.save_analysis_to_markdown(
                            input_data=input_data,
                            program_id=program_id,
                            chunk_name=chunk_name,
                            analysis_result=analysis_result
                        )
                        if doc_path:
                            generated_docs.append(doc_path)

                        analyzed_chunks += 1

                    except Exception as e:
                        self.logger.error(f"Error analyzing chunk {chunk_name} in program {program_id}: {str(e)}")
                        error_chunks += 1
                        failed_chunks.append({
                            "program_id": program_id,
                            "chunk_name": chunk_name,
                            "reason": str(e)
                        })

            # Build knowledge base from the database
            knowledge_base_updates = self.utils.build_knowledge_base_from_database()

            # Add statistics
            knowledge_base_updates["stats"] = {
                "total_programs": total_programs,
                "total_chunks": total_chunks,
                "analyzed_chunks": analyzed_chunks,
                "skipped_chunks": skipped_chunks,
                "errors": error_chunks,
                "failed_chunks": failed_chunks,
                "generated_docs": generated_docs
            }

            # Generate index markdown file for all documentation
            index_path = self.utils.generate_documentation_index(input_data, generated_docs)
            if index_path:
                knowledge_base_updates["stats"]["documentation_index"] = index_path

            self.report_progress("Knowledge mining completed", 1.0)

            return AgentOutput(
                success=True,
                message=f"Successfully completed dependency-based analysis: {analyzed_chunks} chunks analyzed across {total_programs} programs with {error_chunks} errors and {skipped_chunks} skipped chunks. Generated {len(generated_docs)} documentation files.",
                knowledge_base_updates=knowledge_base_updates
            )

        except Exception as e:
            self.logger.exception(f"Error during knowledge mining: {str(e)}")
            return AgentOutput(
                success=False,
                message=f"Knowledge mining failed: {str(e)}",
                errors=[str(e)]
            )

    def _analyze_chunk_with_dependencies(self, program_id: str, chunk_name: str, code_content: str,
                                         used_variables: List[Dict[str, Any]],
                                         referenced_chunks_analysis: List[Dict[str, Any]],
                                         language: str = "cobol") -> Dict[str, Any]:
        """
        Analyze a code chunk with dependency context.
        Uses analysis of called procedures to build up business understanding.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            used_variables: List of variables used in the code
            referenced_chunks_analysis: List of analysis results for called procedures
            language: Pre-detected language (from package analyzer)

        Returns:
            Dict[str, Any]: Analysis results
        """
        # Initialize the result dictionary with default values
        analysis_result = self.utils.initialize_analysis_result(program_id, chunk_name)

        # Prepare context about variables
        variable_context = self.utils.prepare_variable_context(used_variables)

        # Prepare context about called procedures and their business logic
        dependency_context = self.utils.prepare_dependency_context(referenced_chunks_analysis)

        # Aggregate business rules from called procedures
        aggregated_business_rules = self.utils.aggregate_business_rules(referenced_chunks_analysis)

        # Combine contexts
        context = variable_context
        if dependency_context:
            context += "\n\n" + dependency_context
        if aggregated_business_rules:
            context += "\n\n" + aggregated_business_rules

        try:
            # 1. Extract INPUT parameters
            self.logger.info(f"Analyzing input parameters for {program_id}.{chunk_name}")
            input_parameters = self.parameter_extractor.extract_input_parameters(
                program_id, chunk_name, code_content, context
            )
            analysis_result["input_parameters"] = input_parameters

            # 2. Extract OUTPUT parameters
            self.logger.info(f"Analyzing output parameters for {program_id}.{chunk_name}")
            output_parameters = self.parameter_extractor.extract_output_parameters(
                program_id, chunk_name, code_content, context
            )
            analysis_result["output_parameters"] = output_parameters

            # Save extracted parameters as variables to the database
            self._save_parameters_to_database(program_id, chunk_name, input_parameters, output_parameters)

            # 3. Extract business name
            self.logger.info(f"Determining business name for {program_id}.{chunk_name}")
            business_name = self.business_analyzer.extract_business_name(
                program_id, chunk_name, code_content, context, language
            )
            if business_name:
                analysis_result["business_name"] = business_name

            # 4. Extract business description
            self.logger.info(f"Creating business description for {program_id}.{chunk_name}")
            business_description = self.business_analyzer.extract_business_description(
                program_id, chunk_name, code_content, context, language
            )
            if business_description:
                analysis_result["business_description"] = business_description

            # 5. Extract business logic rules (including aggregated rules)
            self.logger.info(f"Extracting business logic for {program_id}.{chunk_name}")
            business_logic = self.business_analyzer.extract_business_logic_with_aggregation(
                program_id, chunk_name, code_content, context, referenced_chunks_analysis, language
            )
            if business_logic:
                analysis_result["business_logic"] = business_logic

            # 6. Generate functional specification
            self.logger.info(f"Creating functional specification for {program_id}.{chunk_name}")
            functional_spec = self.spec_generator.generate_functional_specification(
                program_id, chunk_name, code_content, context, language
            )
            if functional_spec:
                analysis_result["functional_spec"] = functional_spec

            return analysis_result

        except Exception as e:
            self.logger.error(f"Error in analysis process for {program_id}.{chunk_name}: {str(e)}")
            # Return the partially completed analysis result
            return analysis_result

    def save_analysis_to_markdown(self, input_data: AgentInput, program_id: str,
                                  chunk_name: str, analysis_result: Dict[str, Any]) -> Optional[str]:
        """
        Save analysis results to markdown file.

        Args:
            input_data: Input data containing working directory
            program_id: ID of the program
            chunk_name: Name of the chunk
            analysis_result: Analysis results to save

        Returns:
            Optional[str]: Path to saved markdown file, or None if failed
        """
        return self.utils.save_analysis_to_markdown(
            input_data, program_id, chunk_name, analysis_result
        )

    def _save_parameters_to_database(self, program_id: str, chunk_name: str,
                                   input_parameters: List[Dict[str, Any]],
                                   output_parameters: List[Dict[str, Any]]) -> None:
        """
        Save extracted parameters as variables to the database.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            input_parameters: List of input parameters
            output_parameters: List of output parameters
        """
        try:
            # Combine input and output parameters
            all_parameters = []

            # Process input parameters
            for param in input_parameters:
                param_copy = param.copy()
                param_copy['program_id'] = program_id
                param_copy['chunk_name'] = chunk_name
                param_copy['parameter_type'] = 'input'
                param_copy['business_name'] = param.get('business_name', param.get('name', ''))
                all_parameters.append(param_copy)

            # Process output parameters
            for param in output_parameters:
                param_copy = param.copy()
                param_copy['program_id'] = program_id
                param_copy['chunk_name'] = chunk_name
                param_copy['parameter_type'] = 'output'
                param_copy['business_name'] = param.get('business_name', param.get('name', ''))
                all_parameters.append(param_copy)

            # Save to database if we have parameters
            # NOTE: Parameters should NOT be saved as regular variables to prevent duplicates
            # They should be saved to a separate parameters table or marked distinctly
            if all_parameters:
                self.logger.info(f"Extracted {len(all_parameters)} parameters for {program_id}.{chunk_name} - NOT saving to variables table to prevent duplicates")
                # TODO: Implement proper parameter storage separate from variables table

        except Exception as e:
            self.logger.error(f"Error saving parameters to database for {program_id}.{chunk_name}: {str(e)}")
