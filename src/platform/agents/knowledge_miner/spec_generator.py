"""
Functional specification generation for KnowledgeMinerAgent.
Handles creation of detailed functional specifications from COBOL code.
"""
import logging
import re
import time
from typing import Dict, List, Any

from langchain.schema import HumanMessage, SystemMessage
from config.constants import COBOL_ANALYST_SYSTEM_MSG
from llm_settings import invoke_llm
from src.platform.tools.ims_segment_mapper import get_ims_segment_mapper


class SpecGenerator:
    """
    Handles generation of functional specifications from COBOL code chunks.
    Uses LLM with templates to create detailed technical specifications.
    """

    def __init__(self, template_manager):
        """
        Initialize the spec generator.

        Args:
            template_manager: Template manager for rendering prompts
        """

        self.template_manager = template_manager
        self.logger = logging.getLogger(__name__)
        self.ims_mapper = get_ims_segment_mapper()

    def generate_functional_specification(self, program_id: str, chunk_name: str,
                                          code_content: str, context: str, language: str = None) -> str:
        """
        Generate functional specification from code using a two-phase approach:
        Phase 1: Generate core specification (purpose and algorithm) from COBOL code
        Phase 2: Generate test cases and validation rules from the functional specification

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            context: Combined context about variables and dependencies
            language: Pre-detected language (from package analyzer)

        Returns:
            str: Complete functional specification with all 4 sections
        """
        try:
            self.logger.info(f"Starting functional specification generation for {program_id}.{chunk_name}")
            start_time = time.time()

            # Get language plugin for template access
            language_plugin, detected_language = self._get_language_plugin(code_content, language, program_id, chunk_name)

            # Phase 1: Generate core functional specification from COBOL code
            self.logger.info(f"Phase 1: Generating core specification from COBOL code for {program_id}.{chunk_name}")
            purpose_section = self._generate_function_purpose(
                program_id, chunk_name, code_content, context, language_plugin, detected_language
            )

            algorithm_section = self._generate_algorithm(
                program_id, chunk_name, code_content, context, language_plugin, detected_language
            )

            # Create intermediate functional specification for Phase 2
            intermediate_spec = f"""# FUNCTIONAL SPECIFICATION

{purpose_section}

{algorithm_section}
"""

            # Phase 2: Generate test cases and validation rules from functional specification
            self.logger.info(f"Phase 2: Generating test cases and validation rules from functional specification for {program_id}.{chunk_name}")
            validation_error_section = self._generate_validation_and_error_handling_from_spec(
                program_id, chunk_name, intermediate_spec, context, language_plugin, detected_language, code_content
            )

            test_cases_section = self._generate_test_cases_from_spec(
                program_id, chunk_name, intermediate_spec, context, language_plugin, detected_language
            )

            # Aggregate all sections into final specification
            complete_spec = self._aggregate_spec_sections(
                purpose_section, algorithm_section, validation_error_section, test_cases_section
            )

            total_time = time.time() - start_time
            self.logger.info(f"Completed functional specification generation for {program_id}.{chunk_name} in {total_time:.2f}s")

            return complete_spec

        except Exception as e:
            self.logger.error(f"Error generating functional specification for {program_id}.{chunk_name}: {str(e)}")
            raise e

    def _get_language_specific_template(self, language_plugin, language: str, template_type: str, context: dict) -> str:
        """
        Get language-specific template content using the appropriate plugin.

        Args:
            language_plugin: The language plugin instance
            language: The detected language
            template_type: Type of template (functional_spec, etc.)
            context: Template context variables

        Returns:
            str: Rendered template content
        """
        try:
            # Try to get template manager from plugin (generic approach)
            if hasattr(language_plugin, 'get_template_manager'):
                template_manager = language_plugin.get_template_manager()
                template_name = f"knowledge_miner/{template_type}.j2"
                return template_manager.render_template(template_name, context)
            else:
                # Fallback: try to dynamically import language-specific template manager
                try:
                    module_path = f"src.plugins.legacy.{language}.tools.template_manager"
                    module = __import__(module_path, fromlist=[f"get_{language}_template_manager"])
                    get_template_manager_func = getattr(module, f"get_{language}_template_manager")
                    template_manager = get_template_manager_func()
                    template_name = f"knowledge_miner/{template_type}.j2"
                    return template_manager.render_template(template_name, context)
                except (ImportError, AttributeError) as import_error:
                    raise Exception(f"No template manager available for language: {language}. {import_error}")
        except Exception as e:
            self.logger.warning(f"Language-specific template loading failed for {language}: {e}")
            raise

    def _get_language_specific_system_message(self, language_plugin, language: str) -> str:
        """
        Get language-specific system message from plugin.

        Args:
            language_plugin: The language plugin instance
            language: Language name

        Returns:
            str: System message content
        """
        try:
            # Try to get template manager from plugin (generic approach)
            if hasattr(language_plugin, 'get_template_manager'):
                template_manager = language_plugin.get_template_manager()
                template_name = "system_messages/cobol_analyst.j2"
                return template_manager.render_template(template_name, {})
            else:
                # Fallback: try to dynamically import language-specific template manager
                try:
                    module_path = f"src.plugins.legacy.{language}.tools.template_manager"
                    module = __import__(module_path, fromlist=[f"get_{language}_template_manager"])
                    get_template_manager_func = getattr(module, f"get_{language}_template_manager")
                    template_manager = get_template_manager_func()
                    template_name = "system_messages/cobol_analyst.j2"
                    return template_manager.render_template(template_name, {})
                except Exception as e:
                    self.logger.warning(f"Could not load language-specific template manager for {language}: {e}")
                    raise e
        except Exception as e:
            self.logger.error(f"Error loading system message for language {language}: {e}")
            raise e

    def _get_language_plugin(self, code_content: str, language: str, program_id: str, chunk_name: str):
        """
        Get language plugin and detected language.

        Args:
            code_content: Code content for language detection
            language: Pre-detected language (optional)
            program_id: Program ID for logging
            chunk_name: Chunk name for logging

        Returns:
            tuple: (language_plugin, detected_language)
        """
        try:
            from src.platform.plugins.plugin_loader import get_plugin_loader

            plugin_loader = get_plugin_loader()

            # Use pre-detected language or fallback to detection if not provided
            if language:
                detected_language = language
                self.logger.info(f"Using pre-detected language: {detected_language} for {program_id}.{chunk_name}")
            else:
                from src.platform.tools.language_detector import detect_language
                detected_language = detect_language(code_content)
                self.logger.warning(f"Language not provided, detected: {detected_language} for {program_id}.{chunk_name}")

            language_plugin = plugin_loader.get_language_plugin(detected_language)

            if not language_plugin:
                raise Exception(f"No plugin available for language: {detected_language}")

            return language_plugin, detected_language

        except Exception as e:
            self.logger.error(f"Error getting language plugin: {e}")
            raise e

    def _generate_function_purpose(self, program_id: str, chunk_name: str, code_content: str,
                                   context: str, language_plugin, detected_language: str) -> str:
        """
        Generate the FUNCTION PURPOSE section using LLM.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            context: Combined context about variables and dependencies
            language_plugin: Language plugin instance
            detected_language: Detected language

        Returns:
            str: Function purpose section
        """
        try:
            start_time = time.time()
            self.logger.info(f"Generating function purpose for {program_id}.{chunk_name}")

            # Get IMS segment context
            ims_context = self.ims_mapper.get_segments_for_template_context(code_content)

            # Get template content with IMS context
            template_context = {
                "program_id": program_id,
                "chunk_name": chunk_name,
                "code_content": code_content,
                "context": context
            }
            template_context.update(ims_context)

            purpose_prompt = self._get_language_specific_template(
                language_plugin, detected_language, "functional_spec_purpose",
                template_context
            )

            # Get system message
            system_message = self._get_language_specific_system_message(language_plugin, detected_language)

            # Make LLM call
            messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=purpose_prompt)
            ]

            response = invoke_llm(messages)
            result = response.strip()

            elapsed_time = time.time() - start_time
            self.logger.info(f"Generated function purpose for {program_id}.{chunk_name} in {elapsed_time:.2f}s")

            return result

        except Exception as e:
            self.logger.error(f"Error generating function purpose for {program_id}.{chunk_name}: {str(e)}")
            raise e

    def _generate_algorithm(self, program_id: str, chunk_name: str, code_content: str,
                           context: str, language_plugin, detected_language: str) -> str:
        """
        Generate the ALGORITHM section using LLM.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            code_content: Code content
            context: Combined context about variables and dependencies
            language_plugin: Language plugin instance
            detected_language: Detected language

        Returns:
            str: Algorithm section
        """
        try:
            start_time = time.time()
            self.logger.info(f"Generating algorithm for {program_id}.{chunk_name}")

            # Get IMS segment context
            ims_context = self.ims_mapper.get_segments_for_template_context(code_content)

            # Get template content with IMS context
            template_context = {
                "program_id": program_id,
                "chunk_name": chunk_name,
                "code_content": code_content,
                "context": context
            }
            template_context.update(ims_context)

            algorithm_prompt = self._get_language_specific_template(
                language_plugin, detected_language, "functional_spec_algorithm",
                template_context
            )

            self.logger.debug(f"Prompt for algorithm generation for {program_id}.{chunk_name}: {algorithm_prompt}")

            # Get system message
            system_message = self._get_language_specific_system_message(language_plugin, detected_language)

            # Make LLM call
            messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=algorithm_prompt)
            ]

            response = invoke_llm(messages)
            result = response.strip()

            elapsed_time = time.time() - start_time
            self.logger.info(f"Generated algorithm for {program_id}.{chunk_name} in {elapsed_time:.2f}s")

            return result

        except Exception as e:
            self.logger.error(f"Error generating algorithm for {program_id}.{chunk_name}: {str(e)}")
            raise e

    def _generate_validation_and_error_handling_from_spec(self, program_id: str, chunk_name: str, functional_spec: str,
                                                         context: str, language_plugin, detected_language: str, code_content: str) -> str:
        """
        Generate the VALIDATION RULES and ERROR HANDLING sections from functional specification.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            functional_spec: Functional specification content
            context: Combined context about variables and dependencies
            language_plugin: Language plugin instance
            detected_language: Detected language

        Returns:
            str: Combined validation rules and error handling sections
        """
        try:
            start_time = time.time()
            self.logger.info(f"Generating validation and error handling from functional spec for {program_id}.{chunk_name}")

            # Get IMS segment context from functional spec
            ims_context = self.ims_mapper.get_segments_for_template_context(functional_spec)

            # Get template content with IMS context
            template_context = {
                "program_id": program_id,
                "chunk_name": chunk_name,
                "functional_spec": functional_spec,
                "context": context,
                "cobol_code": code_content
            }
            template_context.update(ims_context)

            validation_error_prompt = self._get_language_specific_template(
                language_plugin, detected_language, "functional_spec_validation_error_handling",
                template_context
            )

            # Get system message
            system_message = self._get_language_specific_system_message(language_plugin, detected_language)

            # Make LLM call
            messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=validation_error_prompt)
            ]

            response = invoke_llm(messages)
            result = response.strip()

            # Clean up nested markdown blocks that can break structure
            result = self._clean_nested_markdown(result)

            elapsed_time = time.time() - start_time
            self.logger.info(f"Generated validation and error handling from functional spec for {program_id}.{chunk_name} in {elapsed_time:.2f}s")

            return result

        except Exception as e:
            self.logger.error(f"Error generating validation and error handling from functional spec for {program_id}.{chunk_name}: {str(e)}")
            raise e

    def _generate_test_cases_from_spec(self, program_id: str, chunk_name: str, functional_spec: str,
                                      context: str, language_plugin, detected_language: str, code_content: str) -> str:
        """
        Generate the TEST CASES section from functional specification.

        Args:
            program_id: ID of the program
            chunk_name: Name of the chunk
            functional_spec: Functional specification content
            context: Combined context about variables and dependencies
            language_plugin: Language plugin instance
            detected_language: Detected language

        Returns:
            str: Test cases section
        """
        try:
            start_time = time.time()
            self.logger.info(f"Generating test cases from functional spec for {program_id}.{chunk_name}")

            # Get IMS segment context from functional spec
            ims_context = self.ims_mapper.get_segments_for_template_context(functional_spec)

            # Get template content with IMS context
            template_context = {
                "program_id": program_id,
                "chunk_name": chunk_name,
                "functional_spec": functional_spec,
                "context": context,
                "cobol_code": code_content
            }
            template_context.update(ims_context)

            test_cases_prompt = self._get_language_specific_template(
                language_plugin, detected_language, "functional_spec_test_cases",
                template_context
            )

            # Get system message
            system_message = self._get_language_specific_system_message(language_plugin, detected_language)

            # Make LLM call
            messages = [
                SystemMessage(content=system_message),
                HumanMessage(content=test_cases_prompt)
            ]

            response = invoke_llm(messages)
            result = response.strip()

            # Clean up nested markdown blocks that can break structure
            result = self._clean_nested_markdown(result)

            elapsed_time = time.time() - start_time
            self.logger.info(f"Generated test cases from functional spec for {program_id}.{chunk_name} in {elapsed_time:.2f}s")

            return result

        except Exception as e:
            self.logger.error(f"Error generating test cases from functional spec for {program_id}.{chunk_name}: {str(e)}")
            raise e

    def _aggregate_spec_sections(self, purpose_section: str, algorithm_section: str,
                                validation_error_section: str, test_cases_section: str) -> str:
        """
        Aggregate the 4 specification sections into a complete functional specification.

        Args:
            purpose_section: Function purpose section
            algorithm_section: Algorithm section
            validation_error_section: Combined validation rules and error handling section
            test_cases_section: Test cases section

        Returns:
            str: Complete functional specification document
        """
        try:
            self.logger.info("Aggregating functional specification sections")

            # Combine all sections with proper formatting
            complete_spec = f"""# FUNCTIONAL SPECIFICATION

{purpose_section}

{algorithm_section}

## VALIDATION RULES AND ERROR HANDLING
{validation_error_section}

## TEST CASES
{test_cases_section}
"""

            self.logger.info("Successfully aggregated functional specification sections")
            return complete_spec

        except Exception as e:
            self.logger.error(f"Error aggregating specification sections: {str(e)}")
            raise e

    def _clean_nested_markdown(self, content: str) -> str:
        """
        Clean up nested markdown blocks that can break document structure.

        This method removes nested markdown code block markers (```markdown and ```)
        that appear inside other markdown content, which can cause parsing issues.

        Args:
            content: Raw content that may contain nested markdown blocks

        Returns:
            str: Cleaned content with nested markdown blocks removed
        """
        try:
            lines = content.split('\n')
            cleaned_lines = []
            markdown_block_count = 0

            for line in lines:
                stripped_line = line.strip()

                # Check if this is a markdown code block start
                if stripped_line == '```markdown':
                    markdown_block_count += 1
                    continue  # Skip this line

                # Check if this is a code block end
                elif stripped_line == '```':
                    # If we have open markdown blocks, this closes one
                    if markdown_block_count > 0:
                        markdown_block_count -= 1
                        continue  # Skip this line
                    else:
                        # This is a legitimate code block end, keep it
                        cleaned_lines.append(line)

                # Keep all other lines
                else:
                    cleaned_lines.append(line)

            result = '\n'.join(cleaned_lines)

            # Log if we made any changes
            if result != content:
                self.logger.debug("Cleaned nested markdown blocks from LLM response")

            return result

        except Exception as e:
            self.logger.warning(f"Error cleaning nested markdown: {str(e)}, returning original content")
            return content
