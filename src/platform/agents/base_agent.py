from abc import ABC, abstractmethod
from typing import Dict, Any, List
import os
from pydantic import BaseModel
import logging

from config.constants import OUT_DIR

# Configure logging
os.makedirs(os.environ.get('OUT_DIR', './out'), exist_ok=True)
logging.basicConfig(
    filename=OUT_DIR / "ram20.log",
    level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

class AgentInput(BaseModel):
    """Base model for agent input data"""
    working_directory: str
    knowledge_base: Dict[str, Any]


class AgentOutput(BaseModel):
    """Base model for agent output data"""
    success: bool
    message: str
    knowledge_base_updates: Dict[str, Any] = {}
    artifacts: Dict[str, Any] = {}
    errors: List[str] = []


class BaseAgent(ABC):
    """Base class for all agents in the conversion pipeline"""

    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"agent.{name}")
        self.tools = []  # Tools available to this agent

        # Set up out directory path
        self.out_dir = OUT_DIR
        # Check if out directory exists and is writable
        if not os.path.exists(self.out_dir):
            try:
                os.makedirs(self.out_dir, exist_ok=True)
                self.logger.info(f"Created output directory: {self.out_dir}")
            except Exception as e:
                self.logger.warning(f"Cannot access output directory {self.out_dir}: {str(e)}")
                self.out_dir = None

    @abstractmethod
    def process(self, input_data: AgentInput) -> AgentOutput:
        """
        Process the input data and return results

        Args:
            input_data: Input data for the agent

        Returns:
            AgentOutput: Results of processing
        """
        pass

    @abstractmethod
    def set_up(self, config: dict) -> None:
        """
        Set up current Agent using config gathered from a user's input

        Args:
            config: Configuration provided from a user interface
        """
        pass

    def add_tool(self, tool):
        """Add a tool to the agent's toolbox"""
        self.tools.append(tool)

    def get_tools(self):
        """Get all tools available to this agent"""
        return self.tools

    def ask_user(self, question: str) -> str:
        """
        Ask the user a question and get their response

        In a real implementation, this would connect to the chat interface
        For now, we'll just log the question and return a placeholder

        Args:
            question: The question to ask the user

        Returns:
            str: The user's response
        """
        self.logger.info(f"Agent {self.name} would ask user: {question}")
        # In a real implementation, this would wait for user input
        return "User response placeholder"

    def report_progress(self, message: str, progress: float) -> None:
        """
        Report progress to the UI

        Args:
            message: Status message
            progress: Progress as a float between 0 and 1
        """
        # Ensure progress is between 0 and 1
        progress = max(0.0, min(1.0, progress))

        # Log progress with agent name and percentage
        self.logger.info(f"Agent {self.name} - Progress: {int(progress * 100)}% - {message}")

        # This log message is captured by the LogCapture handler in the orchestrator
        # and displayed in the UI through the conversion_state["logs"] field

    def ensure_out_directory(self, subdir: str = None) -> str | None:
        """
        Ensure the out directory exists and create a subdirectory if needed

        Args:
            subdir: Optional subdirectory name

        Returns:
            str: Path to the directory
        """
        if not self.out_dir:
            return None

        if subdir:
            dir_path = os.path.join(self.out_dir, subdir)
        else:
            dir_path = self.out_dir

        try:
            os.makedirs(dir_path, exist_ok=True)
            return dir_path
        except Exception as e:
            self.logger.warning(f"Cannot create directory {dir_path}: {str(e)}")
            return None
