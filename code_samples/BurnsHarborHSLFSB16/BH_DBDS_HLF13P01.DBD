*--------------------------------------------------------------------*          
*                                                                    *          
*  L F R P    O R D E R    S T A T U S    D A T A    B A S E         *          
*                                                                    *          
*--------------------------------------------------------------------*          
*     4/28/14  MN   ADD CDC EXIT ROUTINE                             *          
*                                                                    *          
*     9/03/20  BB   CHANGED RMNAME PARAMETER                         *          
*              FROM RMNAME=(DFSHDC40,1,15304,5000)                   *          
*              TO   RMNAME=(DFSHDC40,2,33300)                        *          
*    10/31/21  BB   CHANGED RMNAME PARAMETER AND BLOCK SIZE          *          
*              RMNAME (,2,33300) TO (,5,22590)                       *          
*              SIZE 8192 TO 16384                                    *          
*--------------------------------------------------------------------*          
       DBD     NAME=HLF13P01,ACCESS=(HDAM,OSAM),                       X        
               RMNAME=(DFSHDC40,5,22590)                                        
       DATASET DD1=HLF13P01,DEVICE=3380,SIZE=16384,SCAN=0                       
*                                                                               
*                 HLF1301A IS THE ORDER SEGMENT                                 
*                 THE KEY IS PRODUCTION ORDER NUMBER                            
*                                                                               
       SEGM    NAME=HLF1301A,PARENT=0,BYTES=448,FREQ=25000,            X        
               POINTER=TWIN,                                           X        
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                         
       FIELD   NAME=(A1301KEY,SEQ,U),BYTES=9,START=1,TYPE=C                     
       FIELD   NAME=A1301CST,BYTES=7,START=10,TYPE=C                            
       FIELD   NAME=A1301DST,BYTES=2,START=17,TYPE=C                            
       FIELD   NAME=A1301EXP,BYTES=5,START=350,TYPE=C                           
       FIELD   NAME=A1301GRD,BYTES=7,START=405,TYPE=C                           
       FIELD   NAME=A1301WID,BYTES=3,START=412,TYPE=C                           
       FIELD   NAME=A1301LEN,BYTES=3,START=415,TYPE=C                           
*                                                                               
*     SECONDARY INDEX ON EXPEDITER, CUSTOMER, DESTINATION, AND ORDER            
*                                                                               
       LCHILD  NAME=(HLF13AGA,HLF13XAG),PTR=INDX                                
       XDFLD   NAME=HLF13XAG,SEGMENT=HLF1301A,                         X        
               SRCH=(A1301EXP,A1301CST,A1301DST,A1301KEY),             X        
               EXTRTN=LF13XAGX                                                  
*                                                                               
*     SECONDARY INDEX ON CUSTOMER SHORTY, DESTINATION, AND ORDER                
*                                                                               
       LCHILD  NAME=(HLF13AIA,HLF13XAI),PTR=INDX                                
       XDFLD   NAME=HLF13XAI,SEGMENT=HLF1301A,                         X        
               SRCH=(A1301CST,A1301DST,A1301KEY)                                
*                                                                               
*     SECONDARY INDEX ON GRADE, WIDTH, LENGTH, AND ORDER                        
*                                                                               
       LCHILD  NAME=(HLF13AJA,HLF13XAJ),PTR=INDX                                
       XDFLD   NAME=HLF13XAJ,SEGMENT=HLF1301A,                         X        
               SRCH=(A1301GRD,A1301WID,A1301LEN,A1301KEY),             X        
               EXTRTN=LF13XAJX                                                  
*                                                                               
*                 HLF1301B IS THE SPLIT SEGMENT                                 
*                 THE KEY IS SPLIT                                              
*                                                                               
       SEGM    NAME=HLF1301B,BYTES=776,FREQ=2,POINTER=TWIN,            X        
               PARENT=((HLF1301A,SNGL)),                               X        
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                         
       FIELD   NAME=(B1301KEY,SEQ,U),BYTES=6,START=1,TYPE=C                     
       FIELD   NAME=B1301PON,BYTES=9,START=7,TYPE=C                             
       FIELD   NAME=B1301SWC,BYTES=4,START=32,TYPE=C                            
       FIELD   NAME=B1301SCW,BYTES=4,START=36,TYPE=C                            
       FIELD   NAME=B1301PRI,BYTES=2,START=44,TYPE=C                            
       FIELD   NAME=B1301SOW,BYTES=2,START=57,TYPE=C                            
       FIELD   NAME=B1301SOK,BYTES=7,START=57,TYPE=C                            
       FIELD   NAME=B1301SGA,BYTES=5,START=59,TYPE=C                            
       FIELD   NAME=B1301SGT,BYTES=5,START=66,TYPE=C                            
       FIELD   NAME=B1301SGC,BYTES=5,START=73,TYPE=C                            
       FIELD   NAME=B1301HMB,BYTES=4,START=89,TYPE=C                            
       FIELD   NAME=B1301USC,BYTES=2,START=499,TYPE=C                           
       FIELD   NAME=B1301CMP,BYTES=2,START=766,TYPE=C                           
       FIELD   NAME=B1301MLW,BYTES=4,START=771,TYPE=C                           
       LCHILD  NAME=(HLF13AAA,HLF13XAA),PTR=INDX                                
       XDFLD   NAME=HLF13XAA,SEGMENT=HLF1301B,                         X        
               SRCH=(B1301SCW,B1301PRI,B1301PON,B1301KEY),             X        
               EXTRTN=LF13XAAX                                                  
       LCHILD  NAME=(HLF13ABA,HLF13XAB),PTR=INDX                                
       XDFLD   NAME=HLF13XAB,SEGMENT=HLF1301B,                         X        
               SRCH=(B1301SGA,B1301SOW,B1301SCW,B1301PON,B1301KEY),    X        
               EXTRTN=LF13XABX                                                  
       LCHILD  NAME=(HLF13ACA,HLF13XAC),PTR=INDX                                
       XDFLD   NAME=HLF13XAC,SEGMENT=HLF1301B,                         X        
               SRCH=(B1301SCW,B1301HMB,B1301PON,B1301KEY),             X        
               EXTRTN=LF13XACX                                                  
       LCHILD  NAME=(HLF13ADA,HLF13XAD),PTR=INDX                                
       XDFLD   NAME=HLF13XAD,SEGMENT=HLF1301B,                         X        
               SRCH=(B1301CMP,B1301SGT,B1301MLW,B1301PON,B1301KEY),    X        
               EXTRTN=LF13XADX                                                  
       LCHILD  NAME=(HLF13AEA,HLF13XAE),PTR=INDX                                
       XDFLD   NAME=HLF13XAE,SEGMENT=HLF1301B,                         X        
               SRCH=(B1301SWC,B1301SGC,B1301SCW,B1301PON,B1301KEY),    X        
               EXTRTN=LF13XAEX                                                  
       LCHILD  NAME=(HLF13AFA,HLF13XAF),PTR=INDX                                
       XDFLD   NAME=HLF13XAF,SEGMENT=HLF1301B,                         X        
               SRCH=(B1301SCW,B1301USC,B1301HMB,B1301PON,B1301KEY),    X        
               EXTRTN=LF13XAFX                                                  
*                                                                               
*               HLF1301C IS THE APPLICATION SEGMENT                             
*                 THE KEY IS HEAT INGOT CUT OR                                  
*                           COIL NUMBER                                         
*                                                                               
       SEGM    NAME=HLF1301C,BYTES=88,FREQ=5.41,POINTER=TB,            X        
               PARENT=((HLF1301B,SNGL)),                               X        
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                         
       FIELD   NAME=(C1301KEY,SEQ,U),BYTES=14,START=1,TYPE=C                    
*                                                                               
*                HLF1301D IS THE STEEL ORDER SEGMENT                            
*                THE KEY IS STEEL ORDER NUMBER                                  
*                                                                               
       SEGM    NAME=HLF1301D,BYTES=48,FREQ=1,POINTER=TWIN,             X        
               PARENT=((HLF1301B,SNGL)),                               X        
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                         
       FIELD   NAME=(D1301KEY,SEQ,U),BYTES=5,START=1,TYPE=C                     
*                                                                               
*                HLF1301E IS THE HEAT SEGMENT                                   
*                THE KEY IS HEAT NUMBER                                         
*                                                                               
       SEGM    NAME=HLF1301E,BYTES=104,FREQ=1,POINTER=TWIN,            X        
               PARENT=((HLF1301D,SNGL))                                         
       FIELD   NAME=(E1301KEY,SEQ,U),BYTES=9,START=1,TYPE=C                     
*                                                                               
*                HLF1301F IS THE MANIFEST SEGMENT                               
*                THE KEY IS MANIFEST NUMBER                                     
*                                                                               
       SEGM    NAME=HLF1301F,BYTES=16,FREQ=1,POINTER=TWIN,             X        
               PARENT=((HLF1301B,SNGL))                                         
       FIELD   NAME=(F1301KEY,SEQ,U),BYTES=8,START=1,TYPE=C                     
*                                                                               
*                HLF1301G IS THE APPLICATION REMARKS                            
*                THE KEY IS LINE NUMBER                                         
*                                                                               
       SEGM    NAME=HLF1301G,BYTES=48,FREQ=1,POINTER=TWIN,             X        
               PARENT=((HLF1301B,SNGL))                                         
       FIELD   NAME=(G1301KEY,SEQ,U),BYTES=3,START=1,TYPE=C                     
*                                                                               
*                HLF1301H IS THE GROUPING SEGMENT                               
*                THE KEY IS GROUP ORDER/SPLIT NUMBER                            
*                                                                               
       SEGM    NAME=HLF1301H,BYTES=24,FREQ=1,POINTER=TWIN,             X        
               PARENT=((HLF1301B,SNGL))                                         
       FIELD   NAME=(H1301KEY,SEQ,U),BYTES=15,START=1,TYPE=C                    
*                                                                               
*                HLF1301I IS THE TRACKING SEGMENT                               
*                THE KEY IS CHANGE DATE/CHANGE TIME                             
*                                                                               
       SEGM    NAME=HLF1301I,BYTES=120,FREQ=1,POINTER=TWIN,            X        
               PARENT=((HLF1301B,SNGL))                                         
       FIELD   NAME=(I1301KEY,SEQ,U),BYTES=12,START=1,TYPE=C                    
       FIELD   NAME=I1301ORD,BYTES=9,START=15,TYPE=C                            
       FIELD   NAME=I1301SPL,BYTES=6,START=24,TYPE=C                            
       LCHILD  NAME=(HLF13AHA,HLF13XAH),PTR=INDX                                
       XDFLD   NAME=HLF13XAH,SEGMENT=HLF1301I,                         X        
               SRCH=(I1301ORD,I1301SPL,I1301KEY)                                
       DBDGEN                                                                   
       FINISH                                                                   
       END                                                                      
