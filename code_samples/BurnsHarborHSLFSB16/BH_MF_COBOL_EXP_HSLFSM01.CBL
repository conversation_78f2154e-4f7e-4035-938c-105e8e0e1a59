000100 IDENTIFICATION DIVISION.                                                 
000200 PROGRAM-ID. HSLFSM01.                                                    
000300 AUTHOR.      HP                                                          
000400 INSTALLATION. BURNS HARBOR.                                              
000500 DATE-WRITTEN. 2010.                                                      
000600 DATE-COMPILED.                                                           
000700******************************************************************        
000800*DESCRIPTION:   THIS PROGRAM IS PASSED A NON BH HEAT AND SLAB             
000900*               RETURNED IS BH HEAT AND SLAB                              
001000*  CONVERSION RULES:                                                      
001100*  IN WEST AND           HEAT: 10 3102   SLAB: 40310-2827                 
001200*  IN EAST                    5||P||||0                ||A                
001300*                             |                                           
001400*                             PLANT (5) WEST                              
001500*                                   (1) EAST                              
001600*                                                                         
001700* EXAMPLE: 103102-2827 ==> 510P31020-27A                                  
001800******************************************************************        
001900*  CLEVELAND           HEAT: 412 3197   SLAB:4123197011                   
002000*                            7||P||||0              ||A                   
002100*                            |                                            
002200*                            PLANT (7) CLEV                               
002300*                                                                         
002400* EXAMPLE: 4123197 011 ==> 712P31970-01A                                  
002500*                                                                         
002600*NOTE:                                                                    
002700*  MEX WAS A ONE TIME INSTANCE NEEDS TO REVIEWED NEXT TIME                
002800*  SHIPMENTS ARE EXPECTED                                                 
002900*                                                                         
003000*  ALREADY IN BH FORMAT (NO CONVERSION)                                   
003100*  SPARROW POINT, LEXINGTON, COATESVILLE, MANUALS PLATE STAINLESS         
003200******************************************************************        
003300*                    C H A N G E  H I S T O R Y                           
003400*                                                                         
003500*  DATE    REQ #    INIT.   CHANGE DESCRIPTION                            
003600* 09/15/10 LF0R1507 HISRP INITIAL INSTALL                                 
003700*                                                                         
003800* 10/30/17 QA7R7554 HISS6 OBTAIN HEAT YEAR FROM COPY HLF13K91             
003900******************************************************************        
004000 ENVIRONMENT DIVISION.                                                    
004100 CONFIGURATION SECTION.                                                   
004200 SOURCE-COMPUTER. IBM-370.                                                
004300 OBJECT-COMPUTER. IBM-370.                                                
004400 INPUT-OUTPUT SECTION.                                                    
004500 FILE-CONTROL.                                                            
004600                                                                          
004700 DATA DIVISION.                                                           
004800 FILE SECTION.                                                            
004900                                                                          
005000 WORKING-STORAGE SECTION.                                                 
005100 01  MODULE-ID                 PIC X(8)  VALUE 'HSLFSM01'.                
005200                                                                          
005300 01  SWITCHES.                                                            
005400     05  HLF13K91-ERROR-SW     PIC X(01)  VALUE SPACES.                   
005500         88 HLF13K91-ERROR                VALUE 'Y'.                      
005600                                                                          
005700 01  CONTROL-AREAS.                                                       
005800     05  CNTL-FOUND-CCYYMM     PIC X(01)  VALUE SPACES.                   
005900         88  FOUND-CCYYMM                 VALUE 'Y'.                      
006000                                                                          
006100 01  WORK-AREA.                                                           
006200     05  ABEND-4049            PIC 9(04)  VALUE 4049  COMP.               
006300     05  WS-PLANT              PIC X.                                     
006400           88  C-CLEVELAND         VALUE 'C'.                             
006500           88  C-COATESVILLE       VALUE 'D'.                             
006600           88  C-DELETE            VALUE 'Q'.                             
006700           88  C-MANUAL            VALUE 'O'.                             
006800           88  C-IN-WEST           VALUE 'H'.                             
006900           88  C-IN-EAST           VALUE 'I'.                             
007000           88  C-SPARROWS-POINT    VALUE 'P' '2'.                         
007100           88  C-MEX               VALUE 'S'.                             
007200           88  C-LEX               VALUE 'L'.                             
007300*          BELOW PLANTS NOT ACTIVE                                        
007400           88  C-USX               VALUE 'V'.                             
007500           88  C-USX-GARY          VALUE '4'.                             
007600           88  C-AKSTEEL           VALUE '0'.                             
007700           88  C-BETA              VALUE 'Z'.                             
007800           88  C-ELLWOOD           VALUE 'E'.                             
007900           88  C-CST               VALUE '1'.                             
008000                                                                          
008100     05  WS-YEAR-MONTH.                                                   
008200         10  WS-YEAR-CCYY      PIC X(04)  VALUE SPACES.                   
008300         10  WS-YEAR-MM        PIC X(02)  VALUE SPACES.                   
008400                                                                          
008500     05  WS-YYMM.                                                         
008600         10  WS-YY             PIC 9(02)  VALUE ZEROS.                    
008700         10  WS-YY-R REDEFINES WS-YY                                      
008800                               PIC X(02).                                 
008900         10  WS-MM             PIC 9(02)  VALUE ZEROS.                    
009000         10  WS-MM-R REDEFINES WS-MM                                      
009100                               PIC X(02).                                 
009200                                                                          
009300                                                                          
009400     05  WS-DATE.                                                         
009500         10  WS-YEAR           PIC X(02)  VALUE SPACES.                   
009600         10  WS-MONTH          PIC X(02)  VALUE SPACES.                   
009700         10  WS-DAY            PIC X(02)  VALUE SPACES.                   
009800                                                                          
009900     05  WS-YEAR-CODE          PIC X(01)  VALUE SPACES.                   
010000                                                                          
010100*  FORMAT CONVERSION SCHEMES                                              
010200     05  WS-HEAT-CLEVELAND.                                               
010300         10  WS-CLEV-7-HEAT1   PIC X    VALUE '7'.                        
010400         10  WS-CLEV-HEAT2     PIC XX   VALUE SPACES.                     
010500         10  WS-CLEV-HEAT4     PIC X    VALUE SPACES.                     
010600         10  WS-CLEV-HEAT5     PIC X(4) VALUE SPACES.                     
010700         10  WS-CLEV-HEAT9     PIC X    VALUE '0'.                        
010800                                                                          
010900     05  WS-HEAT-INEAST.                                                  
011000         10  WS-INEA-1-HEAT1   PIC X    VALUE '1'.                        
011100         10  WS-INEA-HEAT2     PIC XX   VALUE SPACES.                     
011200         10  WS-INEA-HEAT4     PIC X    VALUE SPACES.                     
011300         10  WS-INEA-HEAT5     PIC X(4) VALUE SPACES.                     
011400         10  WS-INEA-HEAT9     PIC X    VALUE '0'.                        
011500                                                                          
011600     05  WS-HEAT-IMEX.                                                    
011700         10  WS-IMEX-1-HEAT1   PIC X    VALUE '3'.                        
011800         10  WS-IMEX-HEAT2     PIC XX   VALUE SPACES.                     
011900         10  WS-IMEX-HEAT3     PIC X    VALUE SPACES.                     
012000         10  WS-IMEX-HEAT4     PIC X(4) VALUE SPACES.                     
012100         10  WS-IMEX-HEAT9     PIC X    VALUE '0'.                        
012200                                                                          
012300     05  WS-HEAT-COAT.                                                    
012400         10  WS-COAT-1-HEAT1   PIC X    VALUE '6'.                        
012500         10  WS-COAT-HEAT2     PIC XX   VALUE SPACES.                     
012600         10  WS-COAT-HEAT4     PIC X    VALUE SPACES.                     
012700         10  WS-COAT-HEAT5     PIC X(4) VALUE SPACES.                     
012800         10  WS-COAT-HEAT9     PIC X    VALUE '0'.                        
012900                                                                          
013000     05  WS-HEAT-INWEST.                                                  
013100         10  WS-INWE-1-HEAT1   PIC X    VALUE '5'.                        
013200         10  WS-INWE-HEAT2     PIC XX   VALUE SPACES.                     
013300         10  WS-INWE-HEAT4     PIC X    VALUE SPACES.                     
013400         10  WS-INWE-HEAT5     PIC X(4) VALUE SPACES.                     
013500         10  WS-INWE-HEAT9     PIC X    VALUE '0'.                        
013600                                                                          
013700     05  WS-WORK-TIME-NUM             PIC 9(6).                           
013800     05  WS-WORK-TIME REDEFINES WS-WORK-TIME-NUM.                         
013900         10  WS-WORK-HOURS            PIC 9(02).                          
014000         10  WS-WORK-MINS             PIC 9(02).                          
014100         10  WS-WORK-SECS             PIC 9(02).                          
014200                                                                          
014300 01  WORK-CPU-TIME.                                                       
014400     05  WORK-CPU-TIME-HOURS     PIC 99.                                  
014500     05  WORK-CPU-TIME-MINUTES   PIC 99.                                  
014600     05  WORK-CPU-TIME-SECONDS   PIC 99.                                  
014700                                                                          
014800******************************************************************        
014900*            WORK COPY FOR HEAT YEAR CODE                                 
015000******************************************************************        
015100 COPY HLF13K91.                                                           
000100******************************************************************        
000200*                                                                *        
000300*   HLF13K91 COPY IS USED BY MANY MODULES                                 
000400*   TO DETERMINE CORRECT YEAR CODE FOR THE CURRENT QUARTER       *        
000500*   AND YEAR.  COPY IS 312 BYTES LONG.                           *        
000600*                                                                *        
000700*   LETTERS TO BE USED (IN ALPHABETICAL SEQUENCE) ARE:           *        
000800*     A B C D E     H   J K L M N   P     S T U V W X Y Z                 
000900* NOT USED ==>  F G   I           O   Q R      <== NOT USED               
000910*                                                                *        
001000*   F,G,I,O,Q,R ARE NOT TO BE USED BECAUSE THEY CAN BE EASILY             
001100*   CONFUSED WITH OTHER NUMBERS/LETTERS.                                  
001200*                                                                *        
001300*   WHENEVER THIS COPY IS CHANGED, ABOVE MODULES AND ALL DRIVERS *        
001400*   WILL HAVE TO BE RECOMPILED TO PICK UP THE NEW VERSION OF     *        
001500*   THIS COPY.  A. OLSON  10/03/90                               *        
001600*                                                                *        
001700*   REMOVED 1991 AND ADDED 1993.   AO 12/09/92                   *        
001800*                                                                *        
001900*   04/08/1994 AWI RGS ADDED YEARS 1995 THRU 1998 TO COPY AFTER  *        
002000*            FIXING CODES FOR 1994.                                       
002100*                                                                *        
002200*   04/26/1996 JWL ADDED CENTURY FOR THE YEAR 2000.              *        
002300*                                                                *        
002400*   07/27/1998 ROYCE: REMOVED YEARS 1993 & 1994 AND ADDED 1999   *        
002500*                     AND 2000. SEE ADDITIONAL NOTES ON LETTER   *        
002600*                     USAGE ABOVE.                               *        
002700*                                                                *        
002800*   05/05/199 SASKO:  REMOVED YEARS 1995, 1996, & 1997 AND ADDED *        
002900*                     2001, 2002, & 2003. SEE ADDITIONAL NOTES   *        
003000*                     ON LETTER USAGE ABOVE.                     *        
003100*                                                                *        
003200*   01/02/04  HISRP:  UPDATED YEARS 2004 THRU 2007                        
003300*   12/08/08  HISRP:  UPDATED YEARS 2009 THRU 2013                        
003400*   LF8P8218                                                              
003500*   11/08/13  HISRP:  UPDATED YEARS 2014 THRU 2018                        
003600*   LF3R3819                                                              
003700*   10/23/18  HISRP:  UPDATED YEARS 2019 THRU 2023                        
003800*   PS8R8390                                                              
003810*   12/07/23  HISRP:  UPDATED YEARS 2024 THRU 2028                        
003820*   LF3R3364                                                              
003900******************************************************************        
004000 01  HLF13K91.                                                            
004100     05  HLF13K91-YEAR-CODE-TABLE.                                        
004200         10  HLF13K91-TABLE-VALUES.                                       
004210             15  FILLER-1-1         PIC 9(06) VALUE 202301.               
004220             15  FILLER-1-2         PIC 9(06) VALUE 202303.               
004230             15  FILLER-1-3         PIC X(01) VALUE 'A'.                  
004240             15  FILLER-2-1         PIC 9(06) VALUE 202304.               
004250             15  FILLER-2-2         PIC 9(06) VALUE 202306.               
004260             15  FILLER-2-3         PIC X(01) VALUE 'B'.                  
004270             15  FILLER-3-1         PIC 9(06) VALUE 202307.               
004280             15  FILLER-3-2         PIC 9(06) VALUE 202309.               
004290             15  FILLER-3-3         PIC X(01) VALUE 'C'.                  
004291             15  FILLER-4-1         PIC 9(06) VALUE 202310.               
004292             15  FILLER-4-2         PIC 9(06) VALUE 202312.               
004293             15  FILLER-4-3         PIC X(01) VALUE 'D'.                  
004300             15  FILLER-5-1         PIC 9(06) VALUE 202401.               
004400             15  FILLER-5-2         PIC 9(06) VALUE 202403.               
004500             15  FILLER-5-3         PIC X(01) VALUE 'E'.                  
004600             15  FILLER-6-1         PIC 9(06) VALUE 202404.               
004700             15  FILLER-6-2         PIC 9(06) VALUE 202406.               
004800             15  FILLER-6-3         PIC X(01) VALUE 'H'.                  
004900             15  FILLER-7-1         PIC 9(06) VALUE 202407.               
005000             15  FILLER-7-2         PIC 9(06) VALUE 202409.               
005100             15  FILLER-7-3         PIC X(01) VALUE 'J'.                  
005200             15  FILLER-8-1         PIC 9(06) VALUE 202410.               
005300             15  FILLER-8-2         PIC 9(06) VALUE 202412.               
005400             15  FILLER-8-3         PIC X(01) VALUE 'K'.                  
005500             15  FILLER-9-1         PIC 9(06) VALUE 202501.               
005600             15  FILLER-9-2         PIC 9(06) VALUE 202503.               
005700             15  FILLER-9-3         PIC X(01) VALUE 'L'.                  
005800             15  FILLER-10-1        PIC 9(06) VALUE 202504.               
005900             15  FILLER-10-2        PIC 9(06) VALUE 202506.               
006000             15  FILLER-10-3        PIC X(01) VALUE 'M'.                  
006100             15  FILLER-11-1        PIC 9(06) VALUE 202507.               
006200             15  FILLER-11-2        PIC 9(06) VALUE 202509.               
006300             15  FILLER-11-3        PIC X(01) VALUE 'N'.                  
006400             15  FILLER-12-1        PIC 9(06) VALUE 202510.               
006500             15  FILLER-12-2        PIC 9(06) VALUE 202512.               
006600             15  FILLER-12-3        PIC X(01) VALUE 'P'.                  
006700             15  FILLER-13-1        PIC 9(06) VALUE 202601.               
006800             15  FILLER-13-2        PIC 9(06) VALUE 202603.               
006900             15  FILLER-13-3        PIC X(01) VALUE 'S'.                  
007000             15  FILLER-14-1        PIC 9(06) VALUE 202604.               
007100             15  FILLER-14-2        PIC 9(06) VALUE 202606.               
007200             15  FILLER-14-3        PIC X(01) VALUE 'T'.                  
007300             15  FILLER-15-1        PIC 9(06) VALUE 202607.               
007400             15  FILLER-15-2        PIC 9(06) VALUE 202609.               
007500             15  FILLER-15-3        PIC X(01) VALUE 'U'.                  
007600             15  FILLER-16-1        PIC 9(06) VALUE 202610.               
007700             15  FILLER-16-2        PIC 9(06) VALUE 202612.               
007800             15  FILLER-16-3        PIC X(01) VALUE 'V'.                  
007900             15  FILLER-17-1        PIC 9(06) VALUE 202701.               
008000             15  FILLER-17-2        PIC 9(06) VALUE 202703.               
008100             15  FILLER-17-3        PIC X(01) VALUE 'W'.                  
008200             15  FILLER-18-1        PIC 9(06) VALUE 202704.               
008300             15  FILLER-18-2        PIC 9(06) VALUE 202706.               
008400             15  FILLER-18-3        PIC X(01) VALUE 'X'.                  
008500             15  FILLER-19-1        PIC 9(06) VALUE 202707.               
008600             15  FILLER-19-2        PIC 9(06) VALUE 202709.               
008700             15  FILLER-19-3        PIC X(01) VALUE 'Y'.                  
008800             15  FILLER-20-1        PIC 9(06) VALUE 202710.               
008900             15  FILLER-20-2        PIC 9(06) VALUE 202712.               
009000             15  FILLER-20-3        PIC X(01) VALUE 'Z'.                  
009100             15  FILLER-21-1        PIC 9(06) VALUE 202801.               
009200             15  FILLER-21-2        PIC 9(06) VALUE 202803.               
009300             15  FILLER-21-3        PIC X(01) VALUE 'A'.                  
009400             15  FILLER-22-1        PIC 9(06) VALUE 202804.               
009500             15  FILLER-22-2        PIC 9(06) VALUE 202806.               
009600             15  FILLER-22-3        PIC X(01) VALUE 'B'.                  
009700             15  FILLER-23-1        PIC 9(06) VALUE 202807.               
009800             15  FILLER-23-2        PIC 9(06) VALUE 202809.               
009900             15  FILLER-23-3        PIC X(01) VALUE 'C'.                  
010000             15  FILLER-24-1        PIC 9(06) VALUE 202810.               
010100             15  FILLER-24-2        PIC 9(06) VALUE 202812.               
010200             15  FILLER-24-3        PIC X(01) VALUE 'D'.                  
011500         10  HLF13K91-TABLE-ENTRIES                                       
011600                  REDEFINES HLF13K91-TABLE-VALUES                         
011700                     OCCURS 24 TIMES  INDEXED BY TABLE-INDX.              
011800             15  HLF13K91-LOW-CCYYMM PIC 9(06).                           
011900             15  HLF13K91-MAX-CCYYMM PIC 9(06).                           
012000             15  HLF13K91-YEAR-CODE  PIC X(01).                           
015200                                                                          
015300 LINKAGE SECTION.                                                         
015400 01  HSLFSK01.                                                            
015500     05  K01-INPUT.                                                       
015600         10  K01-PLANT               PIC X.                               
015700         10  K01-NON-BH-HEAT         PIC X(20).                           
015800         10  K01-NON-BH-SLAB         PIC X(20).                           
015900         10  K01-NON-BH-MELT-DATE.                                        
016000             15  K01-NON-BH-MELT-MM  PIC XX.                              
016100             15  K01-NON-BH-MELT-DD  PIC XX.                              
016200             15  K01-NON-BH-MELT-YY  PIC XX.                              
016300     05  K01-OUTPUT.                                                      
016400         10  K01-BH-HEAT             PIC X(9).                            
016500         10  K01-BH-SLAB.                                                 
016600             15  K01-BH-SLAB-HEAT    PIC X(9).                            
016700             15  K01-BH-SLAB-ING     PIC X(2).                            
016800             15  K01-BH-SLAB-CUT     PIC X.                               
016900                                                                          
017000*-----------------------------------------------------------------        
017100*-                                                                        
017200*-----------------------------------------------------------------        
017300 <USER> <GROUP> USING  HSLFSK01.                                      
017400                                                                          
017500     MOVE K01-PLANT              TO WS-PLANT                              
017600                                                                          
017700     MOVE SPACES                 TO CNTL-FOUND-CCYYMM                     
017800     PERFORM CHECK-DATES                                                  
017900                                                                          
018000     PERFORM GET-YEAR-CODE                                                
018100       VARYING TABLE-INDX FROM 1 BY 1                                     
018200         UNTIL TABLE-INDX > 24                                            
018300            OR FOUND-CCYYMM                                               
018400                                                                          
018500     IF FOUND-CCYYMM                                                      
018600         PERFORM CONVERT-TO-BH-FORMAT                                     
018700     ELSE                                                                 
018800         SET HLF13K91-ERROR TO TRUE                                       
018900         CALL 'DUMPER' USING ABEND-4049                                   
019000     END-IF                                                               
019100                                                                          
019200     GOBACK.                                                              
019300                                                                          
019400*---------------------------------------------------------------          
019500*---------------------------------------------------------------          
019600 CHECK-DATES.                                                             
019700                                                                          
019800     MOVE K01-NON-BH-MELT-MM     TO WS-MM-R                               
019900     MOVE K01-NON-BH-MELT-YY     TO WS-YY-R                               
020000                                                                          
020100     ACCEPT WS-DATE FROM DATE                                             
020200                                                                          
020300     IF  WS-MM  IS  NUMERIC                                               
020400     AND WS-YY  IS  NUMERIC                                               
020500         MOVE '20'               TO WS-YEAR-CCYY (1:2)                    
020600         MOVE K01-NON-BH-MELT-YY TO WS-YEAR-CCYY (3:2)                    
020700         MOVE K01-NON-BH-MELT-MM TO WS-YEAR-MM                            
020800     ELSE                                                                 
020900         MOVE '20'               TO WS-YEAR-CCYY (1:2)                    
021000         MOVE WS-YEAR            TO WS-YEAR-CCYY (3:2)                    
021100         MOVE WS-MONTH           TO WS-YEAR-MM                            
021200     END-IF.                                                              
021300                                                                          
021400*---------------------------------------------------------------          
021500*---------------------------------------------------------------          
021600 GET-YEAR-CODE.                                                           
021700                                                                          
021800     IF WS-YEAR-MONTH < HLF13K91-MAX-CCYYMM (TABLE-INDX) OR               
021900        WS-YEAR-MONTH = HLF13K91-MAX-CCYYMM (TABLE-INDX)                  
022000         MOVE 'Y'                TO CNTL-FOUND-CCYYMM                     
022100         MOVE HLF13K91-YEAR-CODE (TABLE-INDX)                             
022200                                 TO WS-YEAR-CODE                          
022300         IF TABLE-INDX > 24                                               
022400             CONTINUE                                                     
022500         ELSE                                                             
022600             SET TABLE-INDX UP BY 1                                       
022700         END-IF                                                           
022800     END-IF.                                                              
022900                                                                          
023000*---------------------------------------------------------------          
023100*---------------------------------------------------------------          
023200 CONVERT-TO-BH-FORMAT.                                                    
023300                                                                          
023400     EVALUATE TRUE                                                        
023500        WHEN C-CLEVELAND                                                  
023600            MOVE K01-NON-BH-HEAT(2:2) TO WS-CLEV-HEAT2                    
023700            MOVE WS-YEAR-CODE         TO WS-CLEV-HEAT4                    
023800            MOVE K01-NON-BH-HEAT(4:4) TO WS-CLEV-HEAT5                    
023900            MOVE WS-HEAT-CLEVELAND    TO K01-BH-HEAT                      
024000                                         K01-BH-SLAB-HEAT                 
024100            MOVE K01-NON-BH-SLAB(1:2) TO K01-BH-SLAB-ING                  
024200            MOVE 'A'                  TO K01-BH-SLAB-CUT                  
024300        WHEN C-IN-WEST                                                    
024400            MOVE K01-NON-BH-HEAT(1:2) TO WS-INWE-HEAT2                    
024500*           MOVE 'M'                  TO WS-INWE-HEAT4                    
024600            MOVE WS-YEAR-CODE         TO WS-INWE-HEAT4                    
024700            MOVE K01-NON-BH-HEAT(3:4) TO WS-INWE-HEAT5                    
024800            MOVE WS-HEAT-INWEST       TO K01-BH-HEAT                      
024900                                         K01-BH-SLAB-HEAT                 
025000            MOVE K01-NON-BH-SLAB(4:2) TO K01-BH-SLAB-ING                  
025100            MOVE 'A'                  TO K01-BH-SLAB-CUT                  
025200        WHEN C-IN-EAST                                                    
025300            MOVE K01-NON-BH-HEAT(1:2) TO WS-INEA-HEAT2                    
025400            MOVE WS-YEAR-CODE         TO WS-INEA-HEAT4                    
025500            MOVE K01-NON-BH-HEAT(3:4) TO WS-INEA-HEAT5                    
025600            MOVE WS-HEAT-INEAST       TO K01-BH-HEAT                      
025700                                         K01-BH-SLAB-HEAT                 
025800            MOVE K01-NON-BH-SLAB(4:2) TO K01-BH-SLAB-ING                  
025900** MLK 07/20/2006 ASSIGN CUT BASED ON WHICH IHE STRAND                    
026000**                THE SLAB WAS CAST ON.  ELIMINATE DUPS                   
026100**                PER SPANGLER 3 = SINGLE STRAND CASTER                   
026200**                             8 = STRAND 1 OF 2 STRAND CASTER            
026300**                             9 = STRAND 2 OF 2 STRAND CASTER            
026400            IF K01-NON-BH-SLAB(1:1) = '9'                                 
026500               MOVE 'B'               TO K01-BH-SLAB-CUT                  
026600            ELSE                                                          
026700               MOVE 'A'               TO K01-BH-SLAB-CUT                  
026800            END-IF                                                        
026900        WHEN C-MEX                                                        
027000***          MEX WAS A ONE TIME INSTANCE NEEDS TO REVIEWED                
027100***          WHEN SHIPMENTS ARE EXPECTED                                  
027200            MOVE K01-NON-BH-HEAT(1:2) TO WS-IMEX-HEAT2                    
027300            MOVE WS-YEAR-CODE         TO WS-IMEX-HEAT3                    
027400            MOVE K01-NON-BH-HEAT(3:4) TO WS-IMEX-HEAT4                    
027500            MOVE WS-HEAT-IMEX         TO K01-BH-HEAT                      
027600                                         K01-BH-SLAB-HEAT                 
027700            MOVE K01-NON-BH-SLAB(1:1) TO K01-BH-SLAB-ING(1:1)             
027800            MOVE K01-NON-BH-SLAB(3:1) TO K01-BH-SLAB-ING(2:1)             
027900            MOVE 'A'                  TO K01-BH-SLAB-CUT                  
028000     END-EVALUATE.                                                        
028100                                                                          
028200     IF K01-BH-SLAB-ING = '00'                                            
028300        MOVE '01'                     TO K01-BH-SLAB-ING                  
028400        IF K01-BH-SLAB-CUT = 'A'                                          
028500           MOVE 'C'                   TO K01-BH-SLAB-CUT                  
028600        ELSE                                                              
028700           MOVE 'D'                   TO K01-BH-SLAB-CUT                  
028800        END-IF                                                            
028900     END-IF.                                                              
029000                                                                          
