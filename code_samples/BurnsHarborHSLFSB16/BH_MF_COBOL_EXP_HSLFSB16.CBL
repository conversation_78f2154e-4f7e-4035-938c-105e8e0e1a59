000100 IDENTIFICATION DIVISION.                                         00010019
000200 PROGRAM-ID.    HSLFSB16.                                         00020019
000300 AUTHOR.                                                          00030019
000400 INSTALLATION.  BURNS HARBOR.                                     00040019
000500 DATE-WRITTEN.  10/2005                                           00050019
000600 DATE-COMPILED.                                                   00060019
000700*-----------------------------------------------------------------00070019
000800* PROGRAM FUNCTIONS:                                              00080019
000900* 1) CONVERTS NON BH SLABS TO BH SLAB FORMAT.                     00090019
001000* 2) GETS THE BH GRADE FROM H311 SEGMENT.                         00100019
001100* 3) IF NO H311, SLAB RECORD IS WRITTEN TO 'NOGRADE' FILE TO BE   00110019
001200*    PROCESSED AGAIN UNTIL H311 GRADE EXITS.                      00120019
001300* 4) IF GRADE IS FOUND SLAB RECORD IS WRITTEN TO A FILE TO BE     00130019
001400*    ADDED TO INVENTORY.                                          00140019
001500* 5) BASE ON A COND CODE FROM SENDING PLANT                       00150019
001600*    THE SLAB MAY BE ASSIGNED A DROWGRADED GRADE BECAUSE OF       00160019
001700*    THE SURFACE CONDITION.                                       00170019
001800* 6) FOR PLATE MATERIAL DEPENDING ON TUNDISH IND THE SLAB         00180019
001900*    6TH,7TH POS OF THE GRADE IS CHANGED.                         00190019
002000* 7) A DB2 TABLE (TGSI101) IS UPDATED FOR LOGGING INQUIRY.        00200019
002100*                                                                 00210019
002200* FOR SOME PLANTS SLABS ARE PROCESSED TWICE, THE 1ST SLAB REC     00220019
002300* IS PROCESSED WHEN THE SENDING PLANT MELTS THE HEAT CALLED       00230019
002400* 'PRE ASN' THE SLAB IS ADDED TO INVENTORY WITH A 'CW' STATUS.    00240019
002500* WHEN THE ACTUAL SLAB IS SHIPPED TO BH A SLAB REC IS PROCESSED   00250019
002600* A 2ND TIME 'ASN'.                                               00260019
002700*                                                                 00270019
002800* AT ONE TIME A NOTIFY EMAIL WAS GENERATED, CURRENTLY DISABLED.   00280019
002900*                                                                 00290019
003000* INPUT FILES:                                                    00300019
003100*     SLABS TO BE CONVERTD TO BH FORMAT                           00310019
003200*  SLABIN   DD DSN=H.MISD.INFGNSTL                                00320019
003300*     SLABS WITH NOGRADE XREF TO BH                               00330019
003400*  NOGRDOUT DD DSN=H.MISD.HSLFSB16                                00340019
003500*                                                                 00350019
003600* OUTPUT FILES:                                                   00360019
003700*        SLABS CONVERTED TO BH FORMAT                             00370019
003800*  SLABOUT  DD DSN=H.MISD.SPFGNSTL                                00380019
003900*                                                                 00390019
004000*        INLAND SLABS STATUS UPDATE OW                            00400019
004100*  SLABOW   DD DSN=H.MISD.OWINLAND                                00410019
004200*                                                                 00420019
004300*        SLABS WITH NOGRADE XREF TO BH                            00430019
004400*  NOGRDOUT DD DSN=H.MISD.HSLFSB16                                00440019
004500*                                                                 00450019
004600*        STRIP SLABS FOR SAS REPORTS                              00460019
004700*  HIST     DD DSN=H.ICGN.HSLFSP14                                00470019
004800*        PLATE SLABS FOR SAS REPORTS                              00480019
004900*  HIST     DD DSN=H.ICGN.HSLFSP15                                00490019
005000*                                                                 00500019
005100*        INLAND SLABS STATUS UPDATE OW                            00510019
005200*        FOR SAS ACCTING FGNSTEEL FILE                            00520019
005300*        USED IN JOB HSLFSUNL.HSLFSS10                            00530019
005400*  FGNOW    DD DSN=H.G1SL.HSLFSB16                                00540019
005500*                                                                 00550019
005600*        (PLATE MATERIAL ONLY)                                    00560019
005700*        PRE-ASN SLAB-ORDER AND HIC SP9R0783                      00570019
005800*        USED TO UPDATE BH ORDERING STATUS                        00580019
005900*  OUTPRASN DD DSN=H.G4PL.SLFSB16A                                00590019
006000*                                                                 00600019
006100*-----------------------------------------------------------------00610019
006200*                PROGRAM MODIFICATIONS                            00620019
006300*    NOTE: ALSO CONVERTS HEATS AND SLABS FROM COATES AND          00630019
006400* 03/2005  CLEVELAND TO BH HEAT AND SLAB. FOR STRIP CLEVELAND     00640019
006500*          SLABS ARE WRITTEN TO A HISTORY FILE FOR SAS REPORTING. 00650019
006600**   5/22/01  JTS  ONLY COMPARE FIRST 5 BYTES OF GRADE TO TABLE.  00660019
006700**   5/29/01  JTS  ONLY COMPARE FIRST  AND THIRD THRU FIFTH ON    00670019
006800**                 SPARROWS POINT SLABS.                          00680019
006900**   8/30/01  JTS  ADD AK STEEL.                                  00690019
007000**  10/10/01  JTS  DO NOT MODIFY 2ND POS OF COATES SLABS PER      00700019
007100*                  BUCKSBARG                                      00710019
007200**   2/08/02  JTS  ONLY COMPARE FIRST  AND THIRD THRU FORTH ON    00720019
007300**                 SPARROWS POINT SLABS PER BUCKSBARG.            00730019
007400**   4/22/02  JTS  CHANGE 2ND POS OF COATES HEAT NUMBERS TO       00740019
007500**                 1 WHEN CAST (C IN 6TH POS OF GRADE) PER BUCKSBA00750019
007600**   4/23/02  JTS  CHANGE THICK FROM 9 TO 8.6 ON COATES      SLABS00760019
007700**                 WHEN CAST (C IN 6TH POS OF GRADE) PER BUCKSBARG00770019
007800**   5/09/02  JTS  CHANGE L TO K IN MET GRADE 2ND POSITION PER    00780019
007900**                 KLAIBER                                        00790019
008000**  10/25/02  JTS  EXPAND TABLES.                                 00800019
008100*                                                                 00810019
008200**  03/04/05  HISRP ADD LOGIC TO CONVERT CLEVELAND HEAT & SLABS   00820019
008300**  05/04/05  HISRP ADD LOGIC TO CONVERT INLAND (IND HARBOR EAST) 00830019
008400*                   HEAT AND SLABS                                00840019
008500**  05/23/05  HISRP FOR INLAND IF INGOT IS 00 THEN 01 AND CUT IS X00850019
008600**            PER KLAIBER                                         00860019
008700**  06/30/05  HISRP ADD LOGIC TO CONVERT (IND HARBOR WEST)        00870019
008800*                   HEAT AND SLABS                                00880019
008900**  07/06/05  HISRP INCREASED TABLE ENTRIES                       00890019
009000**  08/22/05  HISRP ADD PO TO HIST FILE AND SUL CHECK FOR PLATE   00900019
009100**  08/23/05  HISRP USE CURRENT DATE FOR SLAB DATE                00910019
009200**  08/24/05  HISRP USE SAME MFST FOR ALL PLATE PER B.SPANGLER    00920019
009300**  09/21/05  HISRP IF NO CHEM FILE PROCESSED WITH SLAB FILE      00930019
009400**            TRY TO XREF THE GRADE FROM SLAB FILE TO THE         00940019
009500**            XREF GRADE FILE                                     00950019
009600**  10/14/05  HISRP IF CHEM FILE HAS PLANT CODE FROM USX          00960019
009700**            THEN DO NOT USE GRADE FROM GRADE XREF FILE          00970019
009800**  10/17/05  HISRP - REMOVE THE YEAR CODE LOOK UP                00980019
009900**  10/20/05  HISRP - BECAUSE SLABS ARE SENT BEFORE THE CHEM      00990019
010000**                    RECORD, ADD LOGIC TO GET THE GRADE          01000019
010100**                    FROM THE HEAT(H311) FOR THE SLAB            01010019
010200**                    IF NOT FOUND THEN WRITE TO A FILE           01020019
010300**                    TO PROCESS LATER WHEN THE CHEM IS SENT      01030019
010400**  12/06/05  HISRP - CORRECT WRITING TO HISTORY FILE FOR STRIP   01040019
010500**                    WHEN PRCESSING NO GRADE SLABS               01050019
010600**  04/06/05  HISRP - ADD DB2 TABLE FOR PROCESSING PAYMENT OF     01060019
010700**                    FREIGHT FOR PURCHASED SLABS.                01070019
010800**  06/05/06  HISRP - ADD SPPOW PT & USX TO DB2 FILE & SEND EMAIL 01080019
010900**                    FOR DUPLICATE ERROR                         01090019
011000**  06/23/06  HISRP - ADD COATSVILLE PROCESSING                   01100019
011100**  06/29/06  HISRP - ADD IMEXSA PROCESSING - OP6V0118            01110019
011200**  07/20/06  KIZER - ADD LOGIC FOR MULTI STRAND CASTER AT IHE    01120019
011300*                     IDM# SP6V0099                               01130019
011400**  07/27/06  HISRP - ADD LOGIC TO PROCESS INLAND SLABS           01140019
011500*   INLAND WILL NOW SEND 2 TRANSMISSIONS THE FIRST IS A PRE ASN,  01150019
011600*   THE SLAB IS ADDED TO IMS WITH A 'CS' STATUS. PRE ASN'S ARE    01160019
011700*   NOT ADDED TO THE FREIGHT TABLE. WHEN SHIPMENT ASN IS SENT     01170019
011800*   THEN THE ACCT. TABLE IS UPDATED AND A RECORD IS WRITTEN       01180019
011900*   TO OUTPUT FILE FOR HPLSIB02 TO CHANGE THE SLAB STATUS FROM    01190019
012000*   'CS' TO 'OW'. - SP6V0100                                      01200019
012100**  08/02/06  HISRP - ADD BH SLAB ID TO EMAIL MESSAGE             01210019
012200**  08/29/06  HISRP - EDIT FOR INCORRECT SLAB DATA FROM IN EAST   01220019
012300*   SP6V0109          AND SKIP PROCESSING                         01230019
012400**  08/30/06  HISRP - CHANGE COATES THICKNESS TO 8.5              01240019
012500*   SP6V0107                                                      01250019
012600**  08/30/06  HISRP - ADD SUL CHECK FOR <= .003                   01260019
012700*   SP6V0110                                                      01270019
012800**  09/13/06  HISRP - CORRECT FGN SLAB ID WHEN REPROCESSING GRADE 01280019
012900*   SP6V0116                                                      01290019
013000**  10/06/06  HISRP - CORRECT FGN SLAB ID WHEN PROCESSING SPOINT  01300019
013100*   SP6V0123                                                      01310019
013200**  10/30/06  HISRP - ADD INLAND EAST STRIP TO TGSI101 TABLE      01320019
013300*   LF6V0148                                                      01330019
013400**  10/30/06  HISRP - FOR GRADES WITH 'R' 2ND POS DO NOT OVERRIDE 01340019
013500*   LF6P5955          'N' IF SUL IS < .003                        01350019
013600**  12/06/06  HISRP - CORRECT FGN HEAT ID FOR SAS HISTORY FILE    01360019
013700**  01/09/07  HISRP - IF CARBON IS 0 DEFAULT TO .001              01370019
013800*   LF7P7045                                                      01380019
013900**  01/12/07  HISRP - USE ALL 7 BYTES OF GRADE FROM XREF FILE     01390019
014000*   LF7P7236                                                      01400019
014100**  05/01/07  HISRP - ADD FILE OF SLABS OW STATUS CHANGES         01410019
014200*   LF7R0413          FOR UPDATE ACCTINGS SAS H.ICGN.FGNSTEEL FILE01420019
014300**  09/19/07 HISRP FOR IND EAST CHECK SLAB-COND-CODE FOR          01430019
014400*   QA7R0641 DOWNGRADING                                          01440019
014500**  10/09/07 HISRP REMOVE EMAILING                                01450019
014600*   LF7R0812                                                      01460019
014700**  10/11/07 HISRP ADD LEX SLABS TO TGSI101 FOR UNLOADED REPORTING01470019
014800*   LF7R0812                                                      01480019
014900**  04/07/08 HISRP USE POS 71:2 FOR CLEV INGOT NUMBER             01490019
015000*   LF8P2771                                                      01500019
015100**  06/02/08 HISRP CORRECT CLEVL RAIL CAR NUMBER IN HIST FILE     01510019
015200*   LF8R0263                                                      01520019
015300**  01/05/09 HISRP DEFAULT SLAB THICKNESS                         01530019
015400*   LF9P8595                                                      01540019
015500**  08/17/09 HISRP CHG DOWNGRADE COND CODE CRITERIA               01550019
015600*   LF9P0267                                                      01560019
015700**  09/04/09 HISRP CHG DOWNGRADE COND CODE CRITERIA               01570019
015800*   LF9P0267 ADD GRADE 125 TO DOWNGRADE LIST                      01580019
015900**  12/08/09 UPDATE H311 CHEM IF FROM SHIPMENT EDI TRANSMISSION   01590019
016000*   OP9P6515                                                      01600019
016100*   12/15/09 HISRP REPLACE GRADE XREF FILE WITH DB2 TABLE &       01610019
016200*   LF9R0933 PROCESS SPECIAL SLABS TO BE SCARF THEN RETURNED TO   01620019
016300*            NON BH PLANT PO-NUM BEGIN WITH 871, INCREASE IN FILE 01630019
016400*            LRECL=300                                            01640019
016500*   03/23/10 SRK  ADD FIELDS TO HLF11K0D AND HSLABADD.            01650019
016600*   SP9R0783      ADD FIELDS TO DB2 TABLE TGSI101.                01660019
016700*                 ON PRE-ASN OUTPUT A RECORD FOR HPLSIB69.        01670019
016800*                 CHANGE 6TH & 7TH DIGIT OF GRADE BASED ON        01680019
016900*                 TUNDISH INDICATOR.                              01690019
017000*   03/29/10 HISRP PER QA UPDATE 2ND ALPHA GRADE CRITERIA         01700019
017100*   LF0R1226 FOR SUL WHEN CALSIL TREATED AND USE                  01710019
017200*            GRADE FROM PREVIOUS H311 HEAT IF RECEIVED            01720019
017300*   04/13/10 REMOVED ALL CHEM PROCESSING. HMTHAB06 WILL           01730019
017400*   LF0R1156 PROCESS THE CHEM RECORDS. ALSO ADDED CHECK           01740019
017500*            TUNDISH IND TO DETERMINE 6TH, 7TH POS OF GRADE       01750019
017600*            FOR PLATE MATERIAL                                   01760019
017700*   04/22/10 HISRP FOR CLEV CHECK SLAB-COND-CODES FOR             01770019
017800*   LF0R1156 DOWNGRADING                                          01780019
017900**  05/20/08 HISRP USE POS 79:2 FOR IN WEST INGOT NUMBER          01790019
018000*   LF0P7714                                                      01800019
018100**  06/21/10 HISRP FOR IND WEST CHECK SLAB-COND-CODES FOR         01810019
018200*   SP0P0693 DOWNGRADING AND PROCESS ASN CREATED FROM HPLSOB15    01820019
018300* 08/25/10 HISRP INWEST DUP HEAT ISSUE FOR TEMP FIX USE 'M' AS    01830019
018400* QA0R1480       YEAR CODE                                        01840019
018500* 09/15/10 HISRP USE SUB ROUTINE TO CONVERT TO BH HEAT/SLAB ID    01850019
018600* LF0R1507       IN WEST AND EAST NOW ADHER TO SAME NAMING OF HEAT01860019
018700*                AND SLAB ID                                      01870019
018800* 10/20/10 HISRP REMOVE COND CODE 40 FROM IN EAST DOWNGRADE EDIT  01880019
018900* QA1R2446                                                        01890019
019000* 06/17/13 HISRP FOR MEX SLABS SET IND TO P SO THEY ARE ADDED     01900019
019100* LF3R3563       WITH CW SLAB STATUS                              01910019
019200* 03/03/14 HISRP FOR MEX SLABS ADD BAY LOC AND SLAB STATUS        01920019
019300* LF3R3563       TO INPUT FILE                                    01930019
019400* 10/20/14 HISRP USE WRKDB TO ASSIGN UNIQUE INGOT NUMBER          01940019
019500* LF4R4664       FOR STRIP IHE SLABS                              01950019
019600* 02/24/16 HISJA INITIALIZE HIST-FGN-SLAB-ID TO PREVENT EXTRA     01960019
019700* LF6R6112       CHARACTERS SHOWING UP IN SLAB ID.                01970019
019800* 05/03/16 HISJM CREATE MTS TABLE ENTRIES IN TGSI101. CREATE NEW  01980019
019900* LF6R6133       EMAIL ERROR DISTRIB FOR MTS SLABS WO VALID ORDER.01990019
020000* 05/31/16 HISJA SWAP FOR COATSVILLE SLABS SWAP LENGTH FOR WIDTH. 02000019
020100* SP6R6270                                                        02010019
020200* 07/27/16 HISJA CREATE SLABS FOR CANCELED AND CLOSED ORDERS AND  02020019
020300* LF6R6427       SEND ERROR MESSAGE FOR ONLY ORDERS NOT FOUND.    02030019
020400* 07/28/16 HISJA INCLUDE ORDERS NOT FOUND IN BUILDING STOCK SLABS 02040019
020500* LF6R6427                                                        02050019
020600* 08/11/16 HISJM PASS NEW K0D-FGN-COND-CODE AND QUALITY-LEVELTHRU 02060019
020700* QA6R6440       TGSI101 TO THE SLAB INVENTORY DATABASE           02070019
020800* 08/31/16 HISS6 ADD LOGIC TO BYPASS INSERTING ROWS IN TGSI101 IF 02080019
020900* LF6R6403       THE FOREIGN SLAB KEY ALREADY EXISTS IN THE TABLE 02090019
021000* 12/16/16 HISRP COATES DOES NOT PROVIDE FGN SLAB ID SKIP DUP EDIT02100019
021100* SP6R6721       FOR TGSI101                                      02110019
021200* 10/25/17 HISP7 COATES SLAB THICKNESS FROM THE INPUT RECORD WILL 02120019
021300* SP6R6721       USED INSTEAD OF A DEFAULT SIZE.  IF INPUT SLAB TH02130019
021400*                ZEROS DEFAULT TO 8.7.                            02140019
021500* 10/12/17 HISAN ADD NEW SUB HLF11M54 REPLACING TSQA101 WITH      02150019
021600* QA7R7593       TABLES DHSQA2.TSQA201, TSQA202, TSQA205          02160019
021700* F *R7593       FOR SLAB DOWNGRADE LOGIC. STRIP PRODUCT ONLY.    02170019
021800* 04/09/18 HISRP ADD HISTORY FILE FOR PLATE MATERIAL              02180019
021900* SP8R7885                                                        02190019
022000* 05/11/18 HISRP CORRECT EMAIL SUBSCRIPT EXCEEDED AND             02200019
022100* SP8R8049 ADD 2ND EMAIL WORKING STORAGE TO ACCOMODATE FOOT PRINT 02210019
022200*          AND DUP SLAB EMAILS IN THE SAME EXECUTION              02220019
022300* 09/14/18 HISRP USE PLANT CODE H(IHW) TO INDICATE SLAB WILL BE   02230019
022400* OP8R8328       SCARF ONLY AND RETURNED TO IHE                   02240019
022500* 09/20/18 HISRP REMOVE IFS AS THE BAY FOR SCARF MATERIAL         02250019
022600* LF8R8345                                                        02260019
022610* 11/15/18 HISRP CHECK IF EXISTING HEAT/SLAB FROM PREVIOUS QTR    02270019
022620* LF8R8503 BEFORE CREATING NEW HEAT/SLAB ID                       02280019
022630* 12/11/18 HISRP CHG COATS SLABS TO 8.6 THICKNESS                 02290019
022640* SP8R8536                                                        02300019
022650* 01/10/19 HISRP WHEN APP ORDER NOT FOUND SET TO STOCK SLAB       02310019
022660* PS9R9003                                                        02320019
022670* 02/21/19 HISRP USE INCOMMNG THICKNESS FOR COATS WHEN < 8.6      02330019
022680* SP9R9090                                                        02340019
022690* 06/28/21 HISRP BRANDING NEW COMPANY DOMAIN NAME                 02350019
022691* PS1R1228                                                        02360019
022692* 12/13/22 HISRP PROCESS SCARF AND RETURN SLABS TO IHE SAME AS    02370019
022693* LF2R2458       FOOTPRINT SLABS AUTO APPLY TO ORDERS             02380019
022692* 01/26/22 HISRP CORRECT INITIALIZE SCARF RETURN SLABS INDICATOR  02390019
022693* EMG23003                                                        02400019
022692* 03/09/23 HISRP ADD IHW TO THE SCARF AND RETURN SLABS PROCESS    02410019
022693* LF3R3091       IHE(871-09999) IHW(871-07777)                    02420019
022692* 02/23/23 HISRP EDIT FOR BAD FORMATTED RECORDS FOR SCRAP SLABS   02430019
022693* LF3R3101       BEING SHIPPED TO BH                              02440019
022650* 12/11/23 HISRP RE-ACTIVATE EMAIL FOR STOCK ADDED SLABS          02441020
022660* LF3R3435       WHEN ORDER IS NOT FOUND                          02442020
022700*-----------------------------------------------------------------02450019
022800 ENVIRONMENT DIVISION.                                            02460019
022900 CONFIGURATION SECTION.                                           02470019
023000 SOURCE-COMPUTER. IBM-370.                                        02480019
023100 OBJECT-COMPUTER. IBM-370.                                        02490019
023200 INPUT-OUTPUT SECTION.                                            02500019
023300 FILE-CONTROL.                                                    02510019
023400     SELECT SLAB-FILE                                             02520019
023500        ASSIGN TO SLABIN.                                         02530019
023600     SELECT SLAB-OUTFILE                                          02540019
023700        ASSIGN TO SLABOUT.                                        02550019
023800     SELECT HIST-FILE                                             02560019
023900        ASSIGN TO HIST.                                           02570019
024000     SELECT NOGRD-FILE                                            02580019
024100        ASSIGN TO NOGRDOUT.                                       02590019
024200     SELECT SLABOW-FILE                                           02600019
024300        ASSIGN TO SLABOW.                                         02610019
024400     SELECT FGNOW-FILE                                            02620019
024500        ASSIGN TO FGNOW.                                          02630019
024600     SELECT OUT-PRE-ASN-FILE                                      02640019
024700        ASSIGN TO OUTPRASN.                                       02650019
024800     SELECT PMHIST-FILE                                           02660019
024900        ASSIGN TO PMHIST.                                         02670019
025000                                                                  02680019
025100 DATA DIVISION.                                                   02690019
025200 FILE SECTION.                                                    02700019
025300 FD  SLAB-FILE                                                    02710019
025400     RECORDING MODE IS F                                          02720019
025500     BLOCK CONTAINS 0 RECORDS                                     02730019
025600     LABEL RECORD IS STANDARD.                                    02740019
025700* NOTE: IF LRECL CHANGES INCREASE NOGRD-TABLE AND NOGRD FILE      02750019
025800 01  SLAB-INREC.                                                  02760019
025900     05  FILLER                  PIC X(300).                      02770019
026000 FD  NOGRD-FILE                                                   02780019
026100     RECORDING MODE IS F                                          02790019
026200     BLOCK CONTAINS 0 RECORDS                                     02800019
026300     LABEL RECORD IS STANDARD.                                    02810019
026400 01  NO-GRADE-REC.                                                02820019
026500     05  FILLER                  PIC X(300).                      02830019
026600                                                                  02840019
026700 FD  SLAB-OUTFILE                                                 02850019
026800     RECORDING MODE IS F                                          02860019
026900     BLOCK CONTAINS 0 RECORDS                                     02870019
027000     LABEL RECORD IS STANDARD.                                    02880019
027100 01  SLAB-OUTREC.                                                 02890019
027200     05  FILLER                  PIC X(80).                       02900019
027300 FD  HIST-FILE                                                    02910019
027400     RECORDING MODE IS F                                          02920019
027500     BLOCK CONTAINS 0 RECORDS                                     02930019
027600     LABEL RECORD IS STANDARD.                                    02940019
027700 01  HISTORY-RECORD.                                              02950019
027800     05  FILLER                  PIC X(300).                      02960019
027900 FD  PMHIST-FILE                                                  02970019
028000     RECORDING MODE IS F                                          02980019
028100     BLOCK CONTAINS 0 RECORDS                                     02990019
028200     LABEL RECORD IS STANDARD.                                    03000019
028300 01  PMHIST-RECORD.                                               03010019
028400     05  FILLER                  PIC X(300).                      03020019
028500 FD  SLABOW-FILE                                                  03030019
028600     RECORDING MODE IS F                                          03040019
028700     BLOCK CONTAINS 0 RECORDS                                     03050019
028800     LABEL RECORD IS STANDARD.                                    03060019
028900 01  SLABOW-REC.                                                  03070019
029000     05  FILLER                  PIC X(330).                      03080019
029100 FD  FGNOW-FILE                                                   03090019
029200     RECORDING MODE IS F                                          03100019
029300     BLOCK CONTAINS 0 RECORDS                                     03110019
029400     LABEL RECORD IS STANDARD.                                    03120019
029500 01  FGNOW-REC.                                                   03130019
029600     05  FILLER                  PIC X(100).                      03140019
029800 FD  OUT-PRE-ASN-FILE                                             03150019
029900     RECORDING MODE IS F                                          03160019
030000     BLOCK CONTAINS 0 RECORDS                                     03170019
030100     LABEL RECORD IS STANDARD.                                    03180019
030200 01  OUT-PRE-ASN-RECORD          PIC X(80).                       03190019
030300                                                                  03200019
030400 WORKING-STORAGE SECTION.                                         03210019
030500                                                                  03220019
030700 01  WORK-CALLING-PROGRAM        PIC X(08) VALUE 'HSLFSB16'.      03230019
030800 01  WS-CLEV-COND-CODE           PIC X(02) VALUE SPACES.          03240019
030900     88 CLEV-DG-COND-CODE        VALUES '01', '11', '21',         03250019
031000                                        '22', '23', '31',         03260019
031100                                        '32', '42', '43',         03270019
031200                                        '52', '53'.               03280019
031300 01  WS-IH-COND-CODE             PIC X     VALUE SPACES.          03290019
031400     88 IH-DG-COND-CODE          VALUES '3', '4', '5',            03300019
031500                                        '6', '7'.                 03310019
031600 01  WS-SCARF-IND                PIC X     VALUE SPACES.          03320019
031700     88 RETURN-SCARF-SLAB        VALUES 'Y'.                      03330019
031800                                                                  03340019
032000 01  WORK-SYSTEM-ID              PIC X(04) VALUE SPACES.          03350019
032100     88  TEST-SYSTEM                       VALUE 'TEST' 'JIMS'.   03360019
032200     88  PROD-SYSTEM                       VALUE 'PROD'.          03370019
032300                                                                  03380019
032400 01  NOGRD-TABLE.                                                 03390019
032500     05  NOGRD-CNT             PIC 9999 VALUE ZERO.               03400019
032600     05  NOGRD-MAX             PIC 9999 VALUE 2000.               03410019
032700                                                                  03420019
032800     05  NOGRD-ENTRIES OCCURS 0 TO 2000 TIMES                     03430019
032900                         DEPENDING ON NOGRD-CNT.                  03440019
033000         10  NOGRD-ENTRY       PIC X(300) VALUE SPACES.           03450019
033100                                                                  03460019
033300 01  LITERALS.                                                    03470019
033400     05  LIT-D                 PIC X(01) VALUE 'D'.               03480019
033500     05  LIT-N                 PIC X(01) VALUE 'N'.               03490019
033600     05  LIT-Y                 PIC X(01) VALUE 'Y'.               03500019
033700                                                                  03510019
033800 01  WORK-AREA.                                                   03520019
033900     05  LIT-EMAIL-FROM    PIC X(29)                              03530019
034000       VALUE '<EMAIL>'.                      03540019
034500     05  LIT-DUP-SUBJECT.                                         03550019
034600        07  FILLER            PIC X(40)                           03560019
034700            VALUE '*** DUP SLAB ALREADY EXISTS TGSI101 *** '.     03570019
034800     05  WS-SHIP-DATE.                                            03580019
034900         10  WS-SHIP-YY        PIC X(2) VALUE SPACES.             03590019
035000         10  WS-SHIP-MM        PIC X(2) VALUE SPACES.             03600019
035100         10  WS-SHIP-DD        PIC X(2) VALUE SPACES.             03610019
035200                                                                  03620019
035300     05  WS-PLANT              PIC X.                             03630019
035400           88  C-CLEV              VALUE 'C'.                     03640019
035500           88  C-COATES            VALUE 'D'.                     03650019
035600           88  C-DELETE            VALUE 'Q'.                     03660019
035700           88  C-MANUAL            VALUE 'O'.                     03670019
035800           88  C-IN-EAST           VALUE 'I' 'H'.                 03680019
035900           88  C-MEX               VALUE 'S'.                     03690019
036000           88  C-LEX               VALUE 'L'.                     03700019
036100*          BELOW PLANTS NOT ACTIVE                                03710019
036200           88  C-IN-WEST           VALUE 'H'.                     03720019
036300           88  C-SPARROWS          VALUE 'P' '2'.                 03730019
036400           88  C-USX               VALUE 'V'.                     03740019
036500           88  C-USX-GARY          VALUE '4'.                     03750019
036600           88  C-AKSTEEL           VALUE '0'.                     03760019
036700           88  C-BETA              VALUE 'Z'.                     03770019
036800           88  C-ELLWOOD           VALUE 'E'.                     03780019
036900           88  C-CST               VALUE '1'.                     03790019
037000                                                                  03800019
037100     05  WS-GRADE-CHECK          PIC XXX.                         03810019
037200         88  WS-GRADE-TYPE-1     VALUES '125' '127' '130'         03820019
037300                                        '132' '146' '163'         03830019
037400                                        '170' '600' '612'         03840019
037500                                        '613'.                    03850019
037600         88  WS-GRADE-TYPE-2     VALUES '213' '336' '337'         03860019
037700                                        '621' '625' '629'.        03870019
037800                                                                  03880019
037900     05  WS-TAPER                  PIC 99V9 VALUE ZEROS.          03890019
037900     05  ACC-IHE                   PIC 99 VALUE ZEROS.            03900019
037900     05  ACC-IHW                   PIC 99 VALUE ZEROS.            03910019
038000                                                                  03920019
038100 01  WS-ASEG.                                                     03930019
038200     05  WS-ASEG-LEN               PIC 9(4)  COMP VALUE 18.       03940019
038300     05  WS-ASEG-KEY.                                             03950019
038400         10  FILLER                PIC X(8)  VALUE 'HSLFSB15'.    03960019
038500         10  FILLER                PIC X(8)  VALUE SPACES.        03970019
038600                                                                  03980019
038700 01  COUNTERS.                                                    03990019
038800     05  CNT-ROWS                  PIC S9(04) COMP VALUE 0.       04000019
038810     05  BAD-REC                   PIC 9(2)  VALUE 0.             04010019
038900                                                                  04020019
039000 01  WS-DSEG.                                                     04030019
039100     05  WS-DSEG-LEN               PIC 9(4)  COMP VALUE 221.      04040019
039200     05  WS-DSEG-HEAT              PIC X(9)  VALUE SPACES.        04050019
039300     05  WS-DSEG-NEXT-INGOT        PIC 9(2)  VALUE ZEROS.         04060019
039400     05  WS-DSEG-LAST-DATE         PIC X(8)  VALUE ZEROS.         04070019
039500     05  WS-DSEG-TAGS.                                            04080019
039600**       TAGS NOT USED                                            04090019
039700         10  WS-TAG-NUMBER  OCCURS 25 TIMES PIC X(08) VALUE ' '.  04100019
039800                                                                  04110019
039900 01  WS-GET-DATE                 PIC 9(04) VALUE ZEROS.           04120019
040000 01  FILLER                      REDEFINES WS-GET-DATE.           04130019
040100     05  WS-GET-CENT             PIC 9(02).                       04140019
040200     05  WS-GET-YEAR             PIC 9(02).                       04150019
040300                                                                  04160019
040400 01  WORK-DATES.                                                  04170019
040500     05  WS-DATE-1-CCYYMM.                                        04180019
040600         10  WS-DATE-1-CC        PIC 9(02) VALUE ZEROS.           04190019
040700         10  WS-DATE-1-YYMM.                                      04200019
040800             15  WS-DATE-1-YY    PIC 9(02) VALUE ZEROS.           04210019
040900             15  WS-DATE-1-MM    PIC 9(02) VALUE ZEROS.           04220019
041000     05  SAVE-DATE               PIC 9(06).                       04230019
041100     05  SAVE-DATE-R             REDEFINES SAVE-DATE.             04240019
041200         10  SAVE-DATE-YY-MM     PIC 9(04).                       04250019
041300         10  SAVE-DATE-DD        PIC 9(02).                       04260019
041400     05  SAVE-TIME               PIC X(08).                       04270019
041500                                                                  04280019
041700 01  WORK-MISC.                                                   04290019
041800     05  WS-MESSAGE              PIC X(15).                       04300019
042000                                                                  04310019
042100 01  CONTROL-INDICATORS.                                          04320019
042200     05  CNTL-DUP-SLAB           PIC X  VALUE ' '.                04330019
042300         88  DUP-SLAB-EMAIL             VALUE 'Y'.                04340019
042400     05  CNTL-EOF                PIC X.                           04350019
042500         88  FILE-NOT-DONE              VALUE 'N'.                04360019
042600         88  FILE-DONE                  VALUE 'Y'.                04370019
042700     05  CNTL-END-OF-SLAB-ORD-SW PIC X  VALUE 'N'.                04380019
042800         88  CNTL-END-OF-SLAB-ORD       VALUE 'Y'.                04390019
042900     05  CALSIL-CNTL             PIC X  VALUE ' '.                04400019
043000         88  CALSIL-TREATED             VALUE 'Y'.                04410019
043100     05  WS-GRADE-CNTL           PIC X  VALUE SPACES.             04420019
043200         88  GRADE-FOUND                VALUE 'Y'.                04430019
043300         88  NO-GRADE-FOUND             VALUE 'N'.                04440019
043500     05  CALL-11M54-SW           PIC X  VALUE SPACES.             04450019
043600         88  CALL-11M54                 VALUE 'Y'.                04460019
043900     05  SW-ORDER-SPLIT-FOUND    PIC X  VALUE SPACES.             04470019
044000         88  ORDER-SPLIT-FOUND          VALUE 'Y'.                04480019
044100         88  ORDER-SPLIT-NOT-FOUND      VALUE 'N'.                04490019
044200     05  SW-EDI-ERROR-PRESENT    PIC X  VALUE SPACES.             04500019
044300         88  EDI-ERROR-PRESENT          VALUE 'Y'.                04510019
044400     05  SW-STOCK-SLAB           PIC X  VALUE 'N'.                04520019
044500         88  STOCK-SLAB                 VALUE 'Y'.                04530019
044600*-----------------------------------------------------------------04540019
044700* THIS FILE IS FOR STRIP SLABS FOR USER SAS REPORTS               04550019
044800*-----------------------------------------------------------------04560019
044900 01  HISTORY-FORMAT.                                              04570019
045000     05  HIST-MM                 PIC 99.                          04580019
045100     05  HIST-DD                 PIC 99.                          04590019
045200     05  HIST-CC                 PIC 99.                          04600019
045300     05  HIST-YY                 PIC 99.                          04610019
045400     05  FILLER                  PIC X.                           04620019
045500     05  HIST-PLANT              PIC X(6).                        04630019
045600     05  FILLER                  PIC X.                           04640019
045700     05  HIST-MANIFEST           PIC X(5).                        04650019
045800     05  FILLER                  PIC X.                           04660019
045900     05  HIST-SO-NUM             PIC X(4).                        04670019
046000     05  FILLER                  PIC X.                           04680019
046100     05  HIST-HEAT               PIC X(10).                       04690019
046200     05  FILLER                  PIC X.                           04700019
046300     05  HIST-FGN-SLAB-ID        PIC X(12).                       04710019
046400     05  FILLER                  PIC X.                           04720019
046500     05  HIST-BH-HEAT-ING-CUT.                                    04730019
046600         10  HIST-BH-HEAT        PIC X(9).                        04740019
046700         10  HIST-BH-INGOT       PIC XX.                          04750019
046800         10  HIST-BH-CUT         PIC X.                           04760019
046900     05  FILLER                  PIC X.                           04770019
047000     05  HIST-WIDTH              PIC 99.99.                       04780019
047100     05  FILLER                  PIC X.                           04790019
047200     05  HIST-THICK              PIC Z9.99.                       04800019
047300     05  FILLER                  PIC X.                           04810019
047400     05  HIST-LENGTH             PIC 999.                         04820019
047500     05  FILLER                  PIC X.                           04830019
047600     05  HIST-WEIGHT             PIC 9(5).                        04840019
047700     05  FILLER                  PIC X.                           04850019
047800     05  HIST-BH-GRADE           PIC X(7).                        04860019
047900     05  FILLER                  PIC X.                           04870019
048000     05  HIST-FGN-GRADE          PIC X(5).                        04880019
048100     05  FILLER                  PIC X.                           04890019
048200     05  HIST-CARNO              PIC X(10).                       04900019
048300     05  FILLER                  PIC X.                           04910019
048400     05  HIST-PO-NUMBER          PIC X(30).                       04920019
048500     05  FILLER                  PIC X.                           04930019
048600     05  HIST-COND-CODE          PIC XX.                          04940019
048700     05  FILLER                  PIC X.                           04950019
048800     05  HIST-TAPER              PIC XX    VALUE SPACES.          04960019
048900     05  FILLER                  PIC X     VALUE SPACES.          04970019
049000     05  HIST-MILL               PIC X     VALUE SPACES.          04980019
049100                                                                  04990019
049200*-----------------------------------------------------------------05000019
049300*- USED IN SUB HSLFSM01 TO CONVERT NON BH SLAB ID TO BH SLAB ID   05010019
049400*- NUMBERING CONVENTION FROM NON BH PLANTS                        05020019
049500 01  HSLFSK01.                                                    05030019
049600     05  K01-INPUT.                                               05040019
049700         10  K01-PLANT               PIC X.                       05050019
049800         10  K01-NON-BH-HEAT         PIC X(20).                   05060019
049900         10  K01-NON-BH-SLAB         PIC X(20).                   05070019
050000         10  K01-NON-BH-MELT-DATE.                                05080019
050100             15  K01-NON-BH-MELT-MM  PIC XX.                      05090019
050200             15  K01-NON-BH-MELT-DD  PIC XX.                      05100019
050300             15  K01-NON-BH-MELT-YY  PIC XX.                      05110019
050400     05  K01-OUTPUT.                                              05120019
050500         10  K01-BH-HEAT.                                         05130019
050600             15  FILLER              PIC X(3).                    05140019
050700             15  K01-BH-HEAT-YEAR    PIC X.                       05150019
050800             15  FILLER              PIC X(5).                    05160019
050900         10  K01-BH-SLAB.                                         05170019
051000             15  K01-BH-SLAB-HEAT    PIC X(9).                    05180019
051100             15  K01-BH-SLAB-ING     PIC X(2).                    05190019
051200             15  K01-BH-SLAB-CUT     PIC X.                       05200019
051300*-----------------------------------------------------------------05210019
051400* SLAB INPUT RECORD                                               05220019
051500 COPY HLF11K0D.                                                   05230019
008801******************************************************************00008801
008802* NOTE:                                                           00008802
008803* THIS COPY WAS CREATED FROM HFGNSLAB WITH ADDITIONAL FIELDS ADDED00008803
008804******************************************************************00008804
008805 01  HLF11K0D.                                                    00008805
008806     05  K0D-TRAIN.                                               00008806
008807         10  K0D-FROM-PLANT            PIC X.                     00008807
008808         88  ABEX                      VALUE 'A'.                 00008808
008809*LOC 8CJ                                                          00008811
008810         88  WEIRTON                   VALUE 'W'.                 00008809
008811         88  AKSTEEL                   VALUE '0'.                 00008810
008812*LOC 8FS                                                          00008811
008813         88  AMGHENT                   VALUE 'Z'.                 00008811
008814*LOC 83D                                                          00008811
008815         88  CLEVELAND                 VALUE 'C'.                 00008812
008816         88  COATESVILLE               VALUE 'D'.                 00008813
008817         88  CSN                       VALUE 'Q'.                 00008814
008818         88  CST                       VALUE '1'.                 00008815
008819         88  ELLWOOD                   VALUE 'E'.                 00008816
008820         88  ILVA                      VALUE 'O'.                 00008817
008821*LOC 8CE                                                          00008811
008822         88  INDIANA-HARBOR            VALUE 'H'.                 00008818
008823*LOC 84L 86L                                                      00008811
008824         88  INLAND                    VALUE 'I'.                 00008819
008825         88  MEXICO                    VALUE 'S'.                 00008820
008826         88  SPARROWS-POINT            VALUE 'P'.                 00008821
008827         88  USX                       VALUE 'V'.                 00008822
008828         88  USX-GARY                  VALUE '4'.                 00008823
008829*LOC 84P                                                          00008811
008830         88  DOFASCO                   VALUE 'F'.                 00008819
008831*LOC 8HA                                                          00008811
008832         88  CALVERT                   VALUE 'Y'.                 00008819
008833         10  FILLER                    PIC X.                     00008824
008834         10  K0D-COND-CODE             PIC X(2).                  00008825
008835     05  K0D-DATE.                                                00008826
008836         10  K0D-YY                    PIC XX.                    00008827
008837         10  K0D-MM                    PIC XX.                    00008828
008838         10  K0D-DD                    PIC XX.                    00008829
008839     05  K0D-RRID                      PIC X(4).                  00008830
008840     05  K0D-CARNO                     PIC X(10).                 00008831
008841     05  K0D-MFST.                                                00008832
008842         10  K0D-MFST-L-110            PIC XX.                    00008833
008843         10 FILLER                     PIC XXX.                   00008834
008844     05  K0D-SO.                                                  00008835
008845         10  K0D-SO-NUM                PIC X(4).                  00008836
008846         10  K0D-SO-PS                 PIC X.                     00008837
008847             88  K0D-STRIP             VALUE 'A' 'D' 'G' 'S'.     00008838
008848*OP6R6141                                                         00008838
008849             88  K0D-MTSST             VALUE 'M'.                 00008838
008850             88  K0D-PLATE-160         VALUE 'B' 'E' 'P'.         00008839
008851             88  K0D-PLATE-110         VALUE 'C' 'F'.             00008840
008852     05  K0D-STYPE                     PIC X.                     00008841
008853     05  K0D-ACCT-GRD                  PIC XXX.                   00008842
008854     05  K0D-MILL-ID                   PIC XXX.                   00008843
008855     05  K0D-HEAT-ING-CUT.                                        00008844
008856       07  K0D-HEAT.                                              00008845
008857         10  K0D-PLANT                 PIC X.                     00008846
008858         10  K0D-FURN1                 PIC X.                     00008847
008859         10  K0D-FURN2                 PIC X.                     00008848
008860         10  K0D-YRCD                  PIC X.                     00008849
008861         10  K0D-SEQUENCE.                                        00008850
008862             15  K0D-MELT              PIC X(4).                  00008851
008863             15  K0D-ZERO              PIC X.                     00008852
008864         10  REDEFINES K0D-SEQUENCE.                              00008853
008865             15  FILLER                PIC X.                     00008854
008866             15  K0D-OFF-SHIFT         PIC X(4).                  00008855
008867       07  K0D-ING                       PIC XX.                  00008856
008868       07  K0D-CUT                       PIC X.                   00008857
008869     05  K0D-WID                       PIC S99V9 COMP-3.          00008858
008870     05  K0D-THK                       PIC S99V9 COMP-3.          00008859
008871     05  K0D-LEN                       PIC S999  COMP-3.          00008860
008872     05  K0D-WT                        PIC S9(5) COMP-3.          00008861
008873     05  K0D-WTCD                      PIC X.                     00008862
008874     05  K0D-STEEL-GRADE.                                         00008863
008875         10  K0D-STL-GRD-1             PIC X.                     00008864
008876         10  K0D-STL-GRD-2             PIC X.                     00008864
008877         10  K0D-STL-GRADE-NUMERIC     PIC XXX.                   00008865
008878         10  K0D-STL-GRD-6-7.                                     00008866
008879             15  K0D-STL-GRD-6         PIC X.                     00008867
008880             15  K0D-STL-GRD-7         PIC X.                     00008868
008881     05  K0D-OTHER-CHEM.                                          00008869
008882         10  K0D-CARBON                PIC 9V99.                  00008870
008883         10  K0D-MANG                  PIC 9V99.                  00008871
008884     05  K0D-EXTENTION REDEFINES                                  00008872
008885                       K0D-OTHER-CHEM  PIC X(6).                  00008873
008886     05  K0D-ORDER     REDEFINES                                  00008874
008887                       K0D-OTHER-CHEM  PIC X(6).                  00008875
008888     05  K0D-SCARF                     PIC X.                     00008876
008889     05  K0D-MOLD-TYPE                 PIC XX.                    00008877
008890     05  K0D-TRANS-IND                 PIC X.                     00008878
008891* ADDITIONAL FIELDS START HERE                                    00008879
008892     05  K0D-PO-NUMBER                 PIC X(22).                 00008880
008893     05  K0D-FGN-MFST                  PIC X(10).                 00008881
008894         88 K0D-PRE-ASN                VALUES '0000000000'        00008882
008895                                              '          '.       00008883
008896         88 K0D-SHIPPED-ASN            VALUES 'HPLSOB15  '.       00008882
008897     05  K0D-SHIP-METHOD               PIC X(01).                 00008884
008898     05  K0D-FGN-HEAT                  PIC X(09).                 00008885
008899     05  K0D-RECV-PLT-ORDER-1          PIC X(20).                 00008886
008900     05  K0D-RECV-PLT-ORDER-2          PIC X(20).                 00008887
008901     05  K0D-ID-SLAB-ORD               PIC X(13).                 00008890
008902     05  K0D-IND-SLAB-TUNDISH          PIC X.                     00008891
008903         88  K0D-FIRST-SLAB            VALUE '1'.                 00008892
008904         88  K0D-MIDDLE-SLAB           VALUE '2'.                 00008894
008905         88  K0D-LAST-SLAB             VALUE '3'.                 00008895
008906     05  K0D-QUAL-CODE                 PIC XX.                    00008896
008907     05  K0D-CHEM-CODE                 PIC XX.                    00008897
008908     05  K0D-DIVERSION-CODE            PIC XX.                    00008897
008910     05  K0D-WID2                      PIC 99V9.                  00008898
008920*OP6R6141                                                         00008838
008921     05  K0D-MTS                       PIC X.                     00008898
008930         88  K0D-MTSST             VALUE 'Y'.                     00008838
008931*QA6R6440 START                                                           
008940     05  K0D-FGN-COND-CODE.                                               
008941         10  K0D-FGN-QUAL-CODE         PIC XX.                          96
008950         10  K0D-FGN-QUAL-PRC          PIC XX.                            
008960*----                                                                     
009000     05  FILLER                        PIC X(110).                00008900
051700* DATA PASSED TO AND FROM HLF11M54                                05240019
051800 COPY HLF11K0F.                                                   05250019
000100 01  HLF11K0F.                                                    00000100
000200     05  K0F-CALLING-PGM                  PIC X(08).              00000200
000300     05  K0F-INPUTS.                                              00000300
000400         10  K0F-OLD-HEAT-GRADE           PIC X(7).               00000400
000500         10  K0F-NEW-HEAT-GRADE           PIC X(7).               00000500
000600         10  K0F-CUR-SLAB-GRADE           PIC X(7).               00000600
000700         10  K0F-HSC.                                             00000700
000800             15  K0F-HEAT                 PIC X(9).               00000800
000900             15  K0F-SLAB                 PIC X(2).               00000900
001000             15  K0F-CUT                  PIC X(1).               00001000
001100         10  K0F-CDE-FGN-SLAB-COND        PIC X(4).               00001100
001101         10  K0F-NON-BH-HEAT              PIC X(20).              00001101
001110         10  K0F-NON-BH-SLAB              PIC X(20).              00001110
001200     05  K0F-OUTPUTS.                                             00001200
001300         10  K0F-NEW-SLAB-LEVEL           PIC X(2).               00001300
001400         10  K0F-NEW-SLAB-GRADE           PIC X(7).               00001400
001500         10  K0F-NAM-PRODUCE-FROM         PIC X(10).              00001500
001600         10  K0F-ERROR-MSG.                                       00001600
001700             15  K0F-ERROR-TXT            PIC X(30).              00001700
001800             15  K0F-ERROR-DATA           PIC X(21).              00001800
052000 COPY HDPIMSWK.                                                   05260019
000100 01  IMS-WORK-MODULE.                                                     
000200******************************************************************        
000300****         I M S   C A L L   F U N C T I O N S              ****        
000400******************************************************************        
000500     05  IMS-CALL-FUNCTION              PIC X(4)    VALUE '    '.         
000600     05  IMS-GU                         PIC X(4)    VALUE 'GU  '.         
000700     05  IMS-GN                         PIC X(4)    VALUE 'GN  '.         
000800     05  IMS-GNP                        PIC X(4)    VALUE 'GNP '.         
000900     05  IMS-GHU                        PIC X(4)    VALUE 'GHU '.         
001000     05  IMS-GHN                        PIC X(4)    VALUE 'GHN '.         
001100     05  IMS-GHNP                       PIC X(4)    VALUE 'GHNP'.         
001200     05  IMS-ISRT                       PIC X(4)    VALUE 'ISRT'.         
001300     05  IMS-REPL                       PIC X(4)    VALUE 'REPL'.         
001400     05  IMS-DLET                       PIC X(4)    VALUE 'DLET'.         
001500     05  IMS-CHKP                       PIC X(4)    VALUE 'CHKP'.         
001600     05  IMS-PURG                       PIC X(4)    VALUE 'PURG'.         
001700     05  IMS-ROLL                       PIC X(4)    VALUE 'ROLL'.         
001800     05  IMS-ROLB                       PIC X(4)    VALUE 'ROLB'.         
001900     05  IMS-CHNG                       PIC X(4)    VALUE 'CHNG'.         
002000******************************************************************        
002100****         I M S   S S A   T O O L S                        ****        
002200******************************************************************        
002300     05  IMS-UNQUALIFIER                PIC X    VALUE ' '.               
002400     05  IMS-LEFT-PAREN                 PIC X    VALUE '('.               
002500     05  IMS-RIGHT-PAREN                PIC X    VALUE ')'.               
002600     05  IMS-INDEPENDENT-AND            PIC X    VALUE '#'.               
002700     05  IMS-DEPENDENT-AND              PIC X    VALUE '&'.               
002800     05  IMS-LOGICAL-OR                 PIC X    VALUE '+'.               
002900     05  IMS-ASTERIK                    PIC X    VALUE '*'.               
003000     05  IMS-NULL                       PIC X    VALUE '-'.               
003100     05  IMS-PATH-CALL                  PIC X    VALUE 'D'.               
003200     05  IMS-FIRST                      PIC X    VALUE 'F'.               
003300     05  IMS-LAST                       PIC X    VALUE 'L'.               
003400     05  IMS-CONCATINATED-KEY           PIC X    VALUE 'C'.               
003500     05  IMS-PARENTAGE                  PIC X    VALUE 'P'.               
003600     05  IMS-POSITION-QUAL              PIC X    VALUE 'U'.               
003700     05  IMS-PATH-POSITION-QUAL         PIC X    VALUE 'V'.               
003800     05  IMS-NO-REPLACE                 PIC X    VALUE 'N'.               
003900******************************************************************        
004000****         I M S   K E Y  O P E R A T O R S                 ****        
004100******************************************************************        
004200     05  IMS-EQUAL                      PIC XX   VALUE ' ='.              
004300     05  IMS-LESS-THAN                  PIC XX   VALUE ' <'.              
004400     05  IMS-GREATER-THAN               PIC XX   VALUE ' >'.              
004500     05  IMS-LESS-OR-EQUAL              PIC XX   VALUE '<='.              
004600     05  IMS-GREATER-OR-EQUAL           PIC XX   VALUE '>='.              
004700     05  IMS-NOT-EQUAL                  PIC XX   VALUE '^='.              
004800******************************************************************        
004900****         I M S   R E T U R N   C O D E S                  ****        
005000******************************************************************        
005100     05  IMS-CHECK-RETURN-CODE          PIC XX   VALUE SPACE.             
005200         88  IMS-CALL-SUCCESSFUL                 VALUE '  '.              
005300         88  IMS-SECURITY-VIOLATION              VALUE 'A4'.              
005400         88  IMS-CROSSED-HIER-BOUNDARY           VALUE 'GA'.              
005500         88  IMS-END-OF-DATABASE                 VALUE 'GB'.              
005600         88  IMS-SEG-NOT-FOUND                   VALUE 'GE'.              
005700         88  IMS-CHANGED-SEGMENT-TYPE            VALUE 'GK'.              
005800         88  IMS-SEG-ALREADY-INSERTED            VALUE 'II'.              
005900         88  IMS-NO-MORE-MESSAGES                VALUE 'QC'.              
006000         88  IMS-END-OF-MESSAGE                  VALUE 'QD'.              
006100         88  IMS-ISRT-DEST-UNKNOWN               VALUE 'QH'.              
006200******************************************************************        
006300****        I M S   F O R M A T   A T T R I B U T E S         ****        
006400******************************************************************        
006500                                                                          
006600*  CURSOR FIELD CONTAINS X'C0' WHICH WILL SET CURSOR FIELD ON             
006700*               WHEN PLACED IN FIRST BYTE OF ATTRIBUTE FIELD.             
006800                                                                          
006900     05  IMS-CURSOR                     PIC X     VALUE X'C0'.            
007000     05  IMS-MOD-HI-CURSOR              PIC XX    VALUE X'C088'.          
007100     05  IMS-MOD-CURSOR                 PIC XX    VALUE X'C0C1'.          
007200     05  IMS-MOD-HI                     PIC XX    VALUE X'0088'.          
007300     05  IMS-MOD                        PIC XX    VALUE X'00C1'.          
007400     05  IMS-MOD-NON-DISPLAY            PIC XX    VALUE X'00C5'.          
007500     05  IMS-MOD-NUM-PROT               PIC XX    VALUE X'00F1'.          
007600     05  IMS-DEFAULT-PLUS-ATTRIBUTES.                                     
007700         10  IMS-D-DEFAULT-ONLY         PIC S999  VALUE +128 COMP.        
007800         10  IMS-D-NON-DISPLAY          PIC S999  VALUE +132 COMP.        
007900         10  IMS-D-HILITE               PIC S999  VALUE +136 COMP.        
008000         10  IMS-D-NUMERIC              PIC S999  VALUE +144 COMP.        
008100         10  IMS-D-PROTECT              PIC S999  VALUE +160 COMP.        
008200         10  IMS-D-HILITE-NUMERIC       PIC S999  VALUE +152 COMP.        
008300         10  IMS-D-HILITE-PROTECT       PIC S999  VALUE +168 COMP.        
008400     05  IMS-REPLACE-ATTRIBUTES.                                          
008500         10  IMS-R-MOD                  PIC S999  VALUE +193 COMP.        
008600         10  IMS-R-NON-DISPLAY          PIC S999  VALUE +196 COMP.        
008700         10  IMS-R-HILITE               PIC S999  VALUE +200 COMP.        
008800         10  IMS-R-NUMERIC              PIC S999  VALUE +208 COMP.        
008900         10  IMS-R-PROTECT              PIC S999  VALUE +224 COMP.        
009000         10  IMS-R-MOD-NUMERIC          PIC S999  VALUE +209 COMP.        
009100         10  IMS-R-MOD-NON-DISPLAY      PIC S999  VALUE +197 COMP.        
009200         10  IMS-R-MOD-HILITE           PIC S999  VALUE +201 COMP.        
009300         10  IMS-R-MOD-HI-CURSOR        PIC S9(5)                         
009400                                        VALUE +49288 COMP.                
009410         10  IMS-R-MOD-CURSOR           PIC S9(5)                         
009420                                        VALUE +49347 COMP.                
009500         10  IMS-R-MOD-HILITE-NUMERIC   PIC S999  VALUE +217 COMP.        
009600         10  IMS-R-MOD-HILITE-PROTECT   PIC S999  VALUE +233 COMP.        
009700         10  IMS-R-HILITE-PROTECT       PIC S999  VALUE +232 COMP.        
009800         10  IMS-R-HILITE-NUMERIC       PIC S999  VALUE +216 COMP.        
009900         10  IMS-R-MOD-PROTECT          PIC S999  VALUE +225 COMP.        
010000         10  IMS-R-MOD-NUMERIC-PROTECT  PIC S999  VALUE +241 COMP.        
052100 COPY USERABND.                                                   05270019
000010 01  USERABND.                                                            
000020     05  IMS-USER-ABEND-CODES.                                            
000030         10  ABEND-CODE           PIC 9(4)    VALUE 4000   COMP.          
000040         10  ABEND-4001           PIC 9(4)    VALUE 4001   COMP.          
000050         10  ABEND-4002           PIC 9(4)    VALUE 4002   COMP.          
000060         10  ABEND-4003           PIC 9(4)    VALUE 4003   COMP.          
000070         10  ABEND-4004           PIC 9(4)    VALUE 4004   COMP.          
000080         10  ABEND-4005           PIC 9(4)    VALUE 4005   COMP.          
000090         10  ABEND-4006           PIC 9(4)    VALUE 4006   COMP.          
000100         10  ABEND-4007           PIC 9(4)    VALUE 4007   COMP.          
000110         10  ABEND-4008           PIC 9(4)    VALUE 4008   COMP.          
000120         10  ABEND-4009           PIC 9(4)    VALUE 4009   COMP.          
000130         10  ABEND-4010           PIC 9(4)    VALUE 4010   COMP.          
000140         10  ABEND-4011           PIC 9(4)    VALUE 4011   COMP.          
000150         10  ABEND-4012           PIC 9(4)    VALUE 4012   COMP.          
000160         10  ABEND-4013           PIC 9(4)    VALUE 4013   COMP.          
000170         10  ABEND-4014           PIC 9(4)    VALUE 4014   COMP.          
000180         10  ABEND-4015           PIC 9(4)    VALUE 4015   COMP.          
000190         10  ABEND-4016           PIC 9(4)    VALUE 4016   COMP.          
000200         10  ABEND-4017           PIC 9(4)    VALUE 4017   COMP.          
000210         10  ABEND-4018           PIC 9(4)    VALUE 4018   COMP.          
000220         10  ABEND-4019           PIC 9(4)    VALUE 4019   COMP.          
000230         10  ABEND-4020           PIC 9(4)    VALUE 4020   COMP.          
000240         10  ABEND-4021           PIC 9(4)    VALUE 4021   COMP.          
000250         10  ABEND-4022           PIC 9(4)    VALUE 4022   COMP.          
000260         10  ABEND-4023           PIC 9(4)    VALUE 4023   COMP.          
000270         10  ABEND-4024           PIC 9(4)    VALUE 4024   COMP.          
000280         10  ABEND-4025           PIC 9(4)    VALUE 4025   COMP.          
000290         10  ABEND-4026           PIC 9(4)    VALUE 4026   COMP.          
000300         10  ABEND-4027           PIC 9(4)    VALUE 4027   COMP.          
000310         10  ABEND-4028           PIC 9(4)    VALUE 4028   COMP.          
000320         10  ABEND-4029           PIC 9(4)    VALUE 4029   COMP.          
000330         10  ABEND-4030           PIC 9(4)    VALUE 4030   COMP.          
000340         10  ABEND-4031           PIC 9(4)    VALUE 4031   COMP.          
000350         10  ABEND-4032           PIC 9(4)    VALUE 4032   COMP.          
000360         10  ABEND-4033           PIC 9(4)    VALUE 4033   COMP.          
000370         10  ABEND-4034           PIC 9(4)    VALUE 4034   COMP.          
000380         10  ABEND-4035           PIC 9(4)    VALUE 4035   COMP.          
000390         10  ABEND-4036           PIC 9(4)    VALUE 4036   COMP.          
000400         10  ABEND-4037           PIC 9(4)    VALUE 4037   COMP.          
000410         10  ABEND-4038           PIC 9(4)    VALUE 4038   COMP.          
000420         10  ABEND-4039           PIC 9(4)    VALUE 4039   COMP.          
000430         10  ABEND-4040           PIC 9(4)    VALUE 4040   COMP.          
000440         10  ABEND-4041           PIC 9(4)    VALUE 4041   COMP.          
000450         10  ABEND-4042           PIC 9(4)    VALUE 4042   COMP.          
000460         10  ABEND-4043           PIC 9(4)    VALUE 4043   COMP.          
000470         10  ABEND-4044           PIC 9(4)    VALUE 4044   COMP.          
000480         10  ABEND-4045           PIC 9(4)    VALUE 4045   COMP.          
000490         10  ABEND-4046           PIC 9(4)    VALUE 4046   COMP.          
000500         10  ABEND-4047           PIC 9(4)    VALUE 4047   COMP.          
000510         10  ABEND-4048           PIC 9(4)    VALUE 4048   COMP.          
000520         10  ABEND-4049           PIC 9(4)    VALUE 4049   COMP.          
000530         10  ABEND-4050           PIC 9(4)    VALUE 4050   COMP.          
000540         10  ABEND-4051           PIC 9(4)    VALUE 4051   COMP.          
000550         10  ABEND-4052           PIC 9(4)    VALUE 4052   COMP.          
000560         10  ABEND-4053           PIC 9(4)    VALUE 4053   COMP.          
000570         10  ABEND-4054           PIC 9(4)    VALUE 4054   COMP.          
000580         10  ABEND-4055           PIC 9(4)    VALUE 4055   COMP.          
000590         10  ABEND-4056           PIC 9(4)    VALUE 4056   COMP.          
000600         10  ABEND-4057           PIC 9(4)    VALUE 4057   COMP.          
000610         10  ABEND-4058           PIC 9(4)    VALUE 4058   COMP.          
000620         10  ABEND-4059           PIC 9(4)    VALUE 4059   COMP.          
000630         10  ABEND-4060           PIC 9(4)    VALUE 4060   COMP.          
000640         10  ABEND-4061           PIC 9(4)    VALUE 4061   COMP.          
000650         10  ABEND-4062           PIC 9(4)    VALUE 4062   COMP.          
000660         10  ABEND-4063           PIC 9(4)    VALUE 4063   COMP.          
000670         10  ABEND-4064           PIC 9(4)    VALUE 4064   COMP.          
000680         10  ABEND-4065           PIC 9(4)    VALUE 4065   COMP.          
000690         10  ABEND-4066           PIC 9(4)    VALUE 4066   COMP.          
000700         10  ABEND-4067           PIC 9(4)    VALUE 4067   COMP.          
000710         10  ABEND-4068           PIC 9(4)    VALUE 4068   COMP.          
000720         10  ABEND-4069           PIC 9(4)    VALUE 4069   COMP.          
000730         10  ABEND-4070           PIC 9(4)    VALUE 4070   COMP.          
000740         10  ABEND-4071           PIC 9(4)    VALUE 4071   COMP.          
000750         10  ABEND-4072           PIC 9(4)    VALUE 4072   COMP.          
000760         10  ABEND-4073           PIC 9(4)    VALUE 4073   COMP.          
000770         10  ABEND-4074           PIC 9(4)    VALUE 4074   COMP.          
000780         10  ABEND-4075           PIC 9(4)    VALUE 4075   COMP.          
000790         10  ABEND-4076           PIC 9(4)    VALUE 4076   COMP.          
000800         10  ABEND-4077           PIC 9(4)    VALUE 4077   COMP.          
000810         10  ABEND-4078           PIC 9(4)    VALUE 4078   COMP.          
000820         10  ABEND-4079           PIC 9(4)    VALUE 4079   COMP.          
000830         10  ABEND-4080           PIC 9(4)    VALUE 4080   COMP.          
000840         10  ABEND-4081           PIC 9(4)    VALUE 4081   COMP.          
000850         10  ABEND-4082           PIC 9(4)    VALUE 4082   COMP.          
000860         10  ABEND-4083           PIC 9(4)    VALUE 4083   COMP.          
000870         10  ABEND-4084           PIC 9(4)    VALUE 4084   COMP.          
000880         10  ABEND-4085           PIC 9(4)    VALUE 4085   COMP.          
000890         10  ABEND-4086           PIC 9(4)    VALUE 4086   COMP.          
000900         10  ABEND-4087           PIC 9(4)    VALUE 4087   COMP.          
000910         10  ABEND-4088           PIC 9(4)    VALUE 4088   COMP.          
000920         10  ABEND-4089           PIC 9(4)    VALUE 4089   COMP.          
000930         10  ABEND-4090           PIC 9(4)    VALUE 4090   COMP.          
000940         10  ABEND-4091           PIC 9(4)    VALUE 4091   COMP.          
000950         10  ABEND-4092           PIC 9(4)    VALUE 4092   COMP.          
000960         10  ABEND-4093           PIC 9(4)    VALUE 4093   COMP.          
052200 COPY HMTHA311.                                                   05280019
000100                                                                          
000200*************************************************                         
000300*  PLEASE NOTIFY INFO CENTER WHEN CHANGING THIS COPY!!!!!!!               
000400*                                                                         
000500*  CALL END USER COMPUTING AT EXT 2834                                    
000600*                                                                         
000610*11/07/2013  HISPF  QA3R3786  ADD FOREIGN/DOMESTIC STEEL SWITCH.          
000700*************************************************                         
000800 01  HMTHA311.                                                            
000900     05  H311-HEAT-NUMBER.                                                
001000         10  H311-PLANT          PIC 9        VALUE ZERO.                 
001100         10  H311-FURNACE        PIC 99       VALUE ZERO.                 
001200         10  H311-YRCDE          PIC X        VALUE SPACES.               
001300         10  H311-MELT           PIC 9(4)     VALUE ZERO.                 
001400         10  H311-DIVR           PIC 9        VALUE ZERO.                 
001500     05  H311-ORG-INGOT          PIC XX       VALUE SPACES.               
001600     05  FILLER  REDEFINES H311-ORG-INGOT.                                
001700         10  H311-CDE-KEY-TEST   PIC X.                                   
001800         10  H311-CDE-KEY-SEQ    PIC X.                                   
001900     05  H311-GRADE-ORD.                                                  
002000         10  H311-GRD-FIVE       PIC X(5)     VALUE SPACES.               
002100         10  H311-GRD-TWO        PIC X(2)     VALUE SPACES.               
002200     05  H311-GRADE-TAP.                                                  
002300         10  H311-GRDT-FIVE      PIC X(5)     VALUE SPACES.               
002400         10  H311-GRDT-TWO       PIC X(2)     VALUE SPACES.               
002500     05  H311-NUMERIC-CHEM.                                               
002600         10  H311-CARBON         PIC 9V99     VALUE ZERO.                 
002700         10  H311-MANGANESE      PIC 9V99     VALUE ZERO.                 
002800         10  H311-PHOSPHORUS     PIC V9(3)    VALUE ZERO.                 
002900         10  H311-SULPHUR        PIC V9(3)    VALUE ZERO.                 
003000         10  H311-SILICON        PIC 9V999    VALUE ZERO.                 
003100         10  H311-COPPER         PIC V9(3)    VALUE ZERO.                 
003200         10  H311-NICKEL         PIC 9V99     VALUE ZERO.                 
003300         10  H311-CHROMIUM       PIC 9V99     VALUE ZERO.                 
003400         10  H311-TIN            PIC V9(3)    VALUE ZERO.                 
003500         10  H311-MOLYBDENUM     PIC V9(3)    VALUE ZERO.                 
003600         10  H311-VANADIUM       PIC V9(3)    VALUE ZERO.                 
003700         10  H311-TITANIUM       PIC V9(3)    VALUE ZERO.                 
003800         10  H311-ALUMINIUM      PIC V9(3)    VALUE ZERO.                 
003900         10  H311-COLUMBIUM      PIC V9(3)    VALUE ZERO.                 
004000         10  H311-NITROGEN       PIC V9(3)    VALUE ZERO.                 
004100         10  H311-BORON          PIC V9(4)    VALUE ZERO.                 
004200         10  H311-COBOLT         PIC 9V999    VALUE ZERO.                 
004300         10  H311-LEAD           PIC V9(4)    VALUE ZERO.                 
004400         10  H311-ARSENIC        PIC V9(4)    VALUE ZERO.                 
004500         10  H311-ZIRCONIUM      PIC V9(4)    VALUE ZERO.                 
004600         10  H311-TUNGSTEN       PIC V9(4)    VALUE ZERO.                 
004700         10  H311-MAGNESIUM      PIC V9(4)    VALUE ZERO.                 
004800         10  H311-SYMBOL-ONE     PIC X(2)     VALUE SPACES.               
004900         10  H311-VALUE-ONE      PIC V9(4)    VALUE ZERO.                 
005000         10  H311-SYMBOL-TWO     PIC X(2)     VALUE SPACES.               
005100         10  H311-VALUE-TWO      PIC V9(4)    VALUE ZERO.                 
005200     05  H311-ALPHA-CHEM REDEFINES H311-NUMERIC-CHEM.                     
005300         10  H311-C              PIC X(3).                                
005400         10  H311-MN             PIC X(3).                                
005500         10  H311-P              PIC X(3).                                
005600         10  H311-S              PIC X(3).                                
005700         10  H311-SI             PIC X(4).                                
005800         10  H311-CU             PIC X(3).                                
005900         10  H311-NI             PIC X(3).                                
006000         10  H311-CR             PIC X(3).                                
006100         10  H311-SN             PIC X(3).                                
006200         10  H311-MO             PIC X(3).                                
006300         10  H311-V              PIC X(3).                                
006400         10  H311-TI             PIC X(3).                                
006500         10  H311-AL             PIC X(3).                                
006600         10  H311-CB             PIC X(3).                                
006700         10  H311-N              PIC X(3).                                
006800         10  H311-B              PIC X(4).                                
006900         10  H311-CO             PIC X(4).                                
007000         10  H311-PB             PIC X(4).                                
007100         10  H311-AS             PIC X(4).                                
007200         10  H311-ZR             PIC X(4).                                
007300         10  H311-W              PIC X(4).                                
007400         10  H311-MG             PIC X(4).                                
007500         10  H311-SYM-ONE        PIC X(2).                                
007600         10  H311-VAL-ONE        PIC X(4).                                
007700         10  H311-SYM-TWO        PIC X(2).                                
007800         10  H311-VAL-TWO        PIC X(4).                                
007900     05  H311-BIRTH-DATE.                                                 
008000         10  H311-YEAR           PIC XX       VALUE SPACES.               
008100         10  H311-MONTH          PIC XX       VALUE SPACES.               
008200         10  H311-DAY            PIC XX       VALUE SPACES.               
008300     05  H311-CHANGE-DATE.                                                
008400         10  H311-YEAR-CHG       PIC XX       VALUE SPACES.               
008500         10  H311-MONTH-CHG      PIC XX       VALUE SPACES.               
008600         10  H311-DAY-CHG        PIC XX       VALUE SPACES.               
008700                                                                          
008800     05  H311-USABLE-IND         PIC X        VALUE SPACES.               
008900                                                                          
009000         88 OFFICIAL-NOT-SELECTED             VALUE 'X'.                  
009100         88 OFFICIAL-SELECTED                 VALUE ' '.                  
009200                                                                          
009300     05  H311-AUTHOR             PIC X(6)     VALUE SPACES.               
009400     05  H311-DIVERSIONS.                                                 
009500         10  H311-DIVER-ONE      PIC 9        VALUE ZERO.                 
009600         10  H311-DIVER-TWO      PIC 9        VALUE ZERO.                 
009700         10  H311-DIVER-THREE    PIC 9        VALUE ZERO.                 
009800         10  H311-DIVER-FOUR     PIC 9        VALUE ZERO.                 
009900         10  H311-DIVER-FIVE     PIC 9        VALUE ZERO.                 
010000         10  H311-DIVER-SIX      PIC 9        VALUE ZERO.                 
010100         10  H311-DIVER-SEVEN    PIC 9        VALUE ZERO.                 
010200         10  H311-DIVER-EIGHT    PIC 9        VALUE ZERO.                 
010300         10  H311-DIVER-NINE     PIC 9        VALUE ZERO.                 
010400                                                                          
010500     05  H311-NEW-DATA-NUM.                                               
010600                                                                          
010700         10  H311-ALUMINUM-SBLE  PIC 99V9999  VALUE ZERO.                 
010800         10  H311-BORON-SBLE     PIC 99V9999  VALUE ZERO.                 
010900         10  H311-CALCIUM-TOT    PIC 99V9999  VALUE ZERO.                 
011000         10  H311-CALCIUM-SBLE   PIC 99V9999  VALUE ZERO.                 
011100         10  H311-CERIUM         PIC 99V9999  VALUE ZERO.                 
011200         10  H311-TELLURIUM      PIC 99V9999  VALUE ZERO.                 
011300         10  H311-ANTIMONY       PIC 99V9999  VALUE ZERO.                 
011400         10  H311-SELENIUM       PIC 99V9999  VALUE ZERO.                 
011500         10  H311-HYDROGEN-DIF   PIC 9V99999  VALUE ZERO.                 
011600                                                                          
011700     05  H311-NEW-DATA-ALPHA REDEFINES H311-NEW-DATA-NUM.                 
011800                                                                          
011900         10  H311-AL-SBLE        PIC X(06).                               
012000         10  H311-B-SBLE         PIC X(06).                               
012100         10  H311-CA-TOT         PIC X(06).                               
012200         10  H311-CA-SBLE        PIC X(06).                               
012300         10  H311-CE             PIC X(06).                               
012400         10  H311-TE             PIC X(06).                               
012500         10  H311-SB             PIC X(06).                               
012600         10  H311-SE             PIC X(06).                               
012700         10  H311-H-DIF          PIC X(06).                               
012800                                                                          
012900     05  H311-OFFICIAL-SELECT-TIME   PIC X(06)  VALUE SPACES.             
013000     05  H311-OFFICIAL-TEST-NUM      PIC X(02)  VALUE SPACES.             
013100                                                                          
013200     05  H311-REMARKS                PIC X(30)  VALUE SPACES.             
013300                                                                          
013400     05  H311-CARBON-DEGAS           PIC V999   VALUE ZEROS.              
013500     05  H311-C-DEGAS REDEFINES H311-CARBON-DEGAS                         
013600                                     PIC XXX.                             
013700                                                                          
013800     05  H311-CDE-DISP-IND         PIC X(02) VALUE SPACE.                 
013900*        88  H311-DISP-IND-VALID             VALUE '00' THRU '09'         
013910*        88  H311-DISP-IND-VALID             VALUE '00' THRU '12'         
013910         88  H311-DISP-IND-VALID             VALUE '00' THRU '13'         
014000                                                   '99'.                  
014100         88  H311-NOT-PROCESSED-BY-A02       VALUE '99'.                  
014200         88  H311-PROCESS-SUCCESSFUL         VALUE '00'.                  
014300*        88  H311-PROCESS-NOT-SUCCESSFUL     VALUE '01' THRU '09'.        
014300*        88  H311-PROCESS-NOT-SUCCESSFUL     VALUE '01' THRU '12'.        
014300         88  H311-PROCESS-NOT-SUCCESSFUL     VALUE '01' THRU '13'.        
014400         88  H311-NO-FINALS-AVAIL            VALUE '01'.                  
014500         88  H311-NO-GRADE-GROUP-AVAIL       VALUE '02'.                  
014600         88  H311-NO-PERCENTS-AVAIL          VALUE '03'.                  
014700         88  H311-NO-INTENDED-CHEM-AVAIL     VALUE '04'.                  
014800         88  H311-NO-OFFICIAL-TEST           VALUE '05'.                  
014900         88  H311-COULD-NOT-REGRADE          VALUE '06'.                  
015000         88  H311-NO-STEEL-ORDER-NUM-AVAIL   VALUE '07'.                  
015100         88  H311-STEEL-ORDER-NUM-NOT-STRIP  VALUE '08'.                  
015200         88  H311-LADLE-FINAL-MISMATCH       VALUE '09'.                  
015202         88  H311-CE-PCM-FAIL                VALUE '10'.                  
015203         88  H311-PB-AS-SB-HIGH              VALUE '11'.                  
015204         88  H311-CA-OFF-CHEM                VALUE '12'.                  
015204         88  H311-OFF-CHEMISTRY              VALUE '13'.                  
015210     05  H311-CARBON-EQUIV     PIC 9V999     VALUE ZEROES.                
015220     05  H311-CARBON-EQUIV-A REDEFINES                                    
015221         H311-CARBON-EQUIV     PIC X(04).                                 
015300     05  H311-MM-USA-MSG-SW    PIC X         VALUE SPACES.                
015301         88  H311-MM-USA-MSG-YES             VALUE ' '.                   
015303         88  H311-MM-USA-MSG-NO              VALUE 'N'.                   
015310     05  FILLER                PIC X(15)     VALUE SPACES.                
015400                                                                          
052300 COPY HSLABADD.                                                   05290019
000100**  H253-REMOVE-DROSS IN THIS COPY IS ONLY FOR THE PURPOSE                
000200**  OF PRINTING 'RMVE DROSS' ON THE PLATE MILL MANIFEST (HSIMFP02).       
000300**  THIS IS NOT THE CORRECT PLACEMENT OF THE DROSS INDICATOR ON           
000400**  THE PLATE MILL INVENTORY.  THIS FIELD COMES FROM EITHER               
000500**  HCCPRM22, HPLSIB05, HPLSIB5A, OR HSLGNB02.  IF THIS COPY              
000600**  IS EXPANDED TO INCLUDE ALL OF THE H253 DATABASE FIELDS, THIS          
000700**  FIELD SHOULD BE MOVED TO IT'S CORRECT POSITION.  11/03/88 RMT.        
000710*                                                                         
000800 01  HSLABADD.                                                            
000900     05  H251-HEAT-NUMBER                  PIC X(7).                      
001000     05  H251-HEAT-WGT-LIQ                 PIC 9(6).                      
001100     05  H251-RM-LIQ-YIELD                 PIC 99V99.                     
001200     05  H251-TAP-GRADE.                                                  
001300         10  H251-TAP-GRD                  PIC X(5).                      
001400         10  H251-TAP-SUFX                 PIC XX.                        
001500     05  H252-INGOT-NO.                                                   
001600         10  H252-STRAND-ID                PIC 9.                         
001700         10  H252-INGOT-ID                 PIC 99.                        
001800     05  H252-AP-HEAT-WGT                  PIC 9(6).                      
001900     05  H252-INGOT-WGT                    PIC 9(5).                      
002000     05  H252-NET-YIELD                    PIC 99V99.                     
002100     05  H252-MOLD-ID                      PIC XX.                        
002200     05  H252-MOLD-SUFX                    PIC X.                         
002300     05  H253-REMOVE-DROSS              REDEFINES                         
002400         H252-MOLD-SUFX                    PIC X.                         
002500     05  H252-INVERSION-CD                 PIC 9.                         
002600     05  H252-MOLD-NO                      PIC 9(4).                      
002700     05  H252-INGOT-STAT                   PIC 9.                         
002800     05  H252-INGOT-SIZE                   PIC 9(4).                      
002900     05  H253-CUT                          PIC X.                         
003000     05  H253-AP-INGOT-WT                  PIC 9(5).                      
003100     05  H253-NET-YIELD                    PIC 9(4).                      
003200     05  H253-HEAT-SUFX                    PIC 9.                         
003300     05  H253-STEEL-ORDER                  PIC X(5).                      
003400     05  H253-SLAB-WIDTH                   PIC 99V9.                      
003500     05  H253-SLAB-THICK                   PIC 99V99.                     
003600     05  H253-SLAB-LEN                     PIC 999.                       
003700     05  H253-SLAB-WGT                     PIC 9(5).                      
003800     05  H253-THEO-SLAB-WGT                PIC 9(5).                      
003900     05  H253-ORD-SLAB-WIDTH               PIC 99V9.                      
004000     05  H253-ORD-SLAB-LEN                 PIC 999.                       
004100     05  H253-MFST-NO                      PIC 9(5).                      
004200     05  H253-STEEL-GRADE.                                                
004300         10  H253-STL-GRADE                PIC X(5).                      
004400         10  H253-STL-SUFX                 PIC XX.                        
004500     05  H253-CARBON                       PIC S9V99.                     
004600     05  H253-MANG                         PIC S9V99.                     
004700     05  H253-SILICONE                     PIC S9V999.                    
004800     05  H253-STEEL-TYPE                   PIC X.                         
004900     05  H253-ACTG-GRADE                   PIC XXX.                       
005000     05  H253-MILL-CD                      PIC 999.                       
005100     05  H253-DATE-PRODUCED                PIC 9(6).                      
005200     05  H253-TURN-PRODUCED                PIC 9.                         
005300     05  H253-SM-DEST                      PIC X.                         
005400     05  H253-SM-SCARF-CD                  PIC X.                         
005500     05  H253-SM-DISCARD                   PIC 99.                        
005600     05  H253-SM-ROLL-REV                  PIC 9.                         
005700     05  H253-SM-ROLL-METHOD               PIC 9.                         
005800     05  H253-SM-ROLL-TYPE                 PIC 9.                         
005900     05  H253-SM-MANUAL-ROLL               PIC 9.                         
006000     05  H253-SM-COLD-STL                  PIC X.                         
006100     05  H253-PROD-STATUS                  PIC XX.                        
006200     05  H253-SLAB-STATUS                  PIC XX.                        
006300     05  H253-NOT-ROLLED-REASON            PIC 99.                        
006400     05  H253-SY-SCARF-CD                  PIC X.                         
006500     05  H253-SY-PREHEAT-CD                PIC X.                         
006600     05  H253-SY-PAINT-CD                  PIC X.                         
006700     05  H253-SY-INSPECT-CD                PIC X.                         
006800     05  H253-SY-TEST-CD                   PIC X.                         
006900     05  H253-SY-SKIN-SCARF                PIC X.                         
007000     05  H253-CHANGE-REASON                PIC XX.                        
007100     05  H253-ACTG-CD                      PIC XX.                        
007200     05  H253-PRIORITY-CD                  PIC 99.                        
007300     05  H253-PR-SCH-HOLD                  PIC XX.                        
007400     05  H253-OPER-HOLD                    PIC XX.                        
007500     05  H253-MET-TRANS-CD.                                               
007600         10  H253-MET-TRANS-1              PIC X.                         
007700         10  H253-MET-TRANS-2              PIC X.                         
007800     05  H253-MET-REASON-CD                PIC XX.                        
007900     05  H253-MET-TEST-SEQ                 PIC X(5).                      
008000     05  H253-MET-RATING                   PIC X.                         
008100     05  H253-PARENT-CUT                   PIC X.                         
008200     05  H253-THEO-SLAB-IND                PIC X.                         
008300     05  H253-SY-BURN-CD                   PIC X.                         
008400     05  H253-BURN-TYPE                    PIC XX.                        
008500     05  H253-BURN-REQ-NO                  PIC 9(4).                      
008600     05  H253-BURN-CUT-LETTERS.                                           
008700         10  H253-BURN-CUT-NO1             PIC X.                         
008800         10  H253-BURN-CUT-NO2             PIC X.                         
008900         10  H253-BURN-CUT-NO3             PIC X.                         
009000         10  H253-BURN-CUT-NO4             PIC X.                         
009100         10  H253-BURN-CUT-NO5             PIC X.                         
009200         10  H253-BURN-CUT-NO6             PIC X.                         
009300         10  H253-BURN-CUT-NO7             PIC X.                         
009400         10  H253-BURN-CUT-NO8             PIC X.                         
009500     05  H253-SCH-CD                       PIC XX.                        
009600     05  H253-STL-PRACT                    PIC XXX.                       
009700     05  H253-SM-SEQ-NUM                REDEFINES                         
009800         H253-STL-PRACT                    PIC XXX.                       
009900     05  H253-SCH-WEEK.                                                   
010000         10  H253-SCH-YR                   PIC 99.                        
010100         10  H253-SCH-WK                   PIC 99.                        
010420     05  H253-LOCATION-CD.                                                
010430         10  H253-110-IND                  PIC X.                         
010440         10  H253-TRANSFER-FROM            PIC X.                         
010450     05  H253-AUTO-MAN-IND                 PIC X.                         
010470     05  H253-CDE-MAX-TMP-BEF-4H           PIC X.                         
010500     05  H253-4YARD-BAY-LOC                PIC XXX.                       
010600     05  H253-FIN-MILL-BAY                 PIC XXX.                       
010610******************************************************************        
010700     05  PM-ORDERED-INFO.                                                 
010800         10  H253-PILE                     PIC 9(4).                      
010900         10  H253-UR                       PIC 9(6).                      
011000         10  H253-ITEM                     PIC  99.                       
011100         10  H253-SUB-ITEM                 PIC 9.                         
011200         10  H253-JUMP-NO                  PIC 999.                       
011300         10  H253-SLAB-SEQ-NUM             PIC X(6).                      
011400         10  H253-SERIAL                   PIC X(7).                      
011500         10  H253-SERIAL-CD                PIC X.                         
011600         10  H253-PREV-SERIAL-CD           PIC X.                         
011700         10  H253-MENT-CUT                 PIC X.                         
011800         10  H253-CB-AMOUNT                PIC 999.                       
011900         10  H253-ORD-PLT-GUAGE            PIC 99V999.                    
012000         10  H253-ORD-PLT-WIDTH            PIC 999V99.                    
012100         10  H253-ORD-PLT-PATTERN          PIC 99.                        
012200         10  H253-CAR-NO-HOLD.                                            
012300             15  SLABS-FOR-HT-TM           PIC XX.                        
012400             15  TAP-TIME-CAST             PIC X(4).                      
012500             15  CAR-NUM-CAST              PIC X(4).                      
012600         10  H253-HEAT-TREAT-CD            PIC 9.                         
012700         10  H253-COMB-IND                 PIC X.                         
012800         10  H253-AIM-GAUGE                PIC 99V999.                    
012900         10  H253-AIM-WIDTH                PIC 999V99.                    
013000         10  H253-GRADE-CODE               PIC X(7).                      
013100         10  H253-SPECL-ALLOYING           PIC X(6).                      
013200         10  H253-REDUCE-TO-GUAGE          PIC 99V99.                     
013300         10  H253-TRANSFER-GAUGE           PIC 99V99.                     
013400         10  H253-REQUIRED-LEN-HR          PIC 9(4).                      
013500         10  H253-TURN-PRACTICE            PIC 9.                         
013600         10  H253-SPRAY-PRACTICE           PIC 9.                         
013700         10  H253-TEMP-PRACTICE            PIC XXX.                       
013800         10  H253-MET-TEST-REQ-HR          PIC X.                         
013900         10  H253-SLAB-CODE                PIC 9.                         
014000         10  H253-REQUIRED-LEN-FS          PIC 9(5).                      
014100         10  H253-MET-TEST-REQ-FS          PIC 99.                        
014200         10  H253-HARDNESS-CODE            PIC XX.                        
014300         10  H253-SPECL-WID-LEN-TOL        PIC 9.                         
014400         10  H253-SPECL-OVR-UND-TOL        PIC 9(6).                      
014500         10  H253-SPLIT-COUNT              PIC X.                         
014600         10  H253-OPERATOR                 PIC 999   COMP-3.              
014610******************************************************************        
014700     05  HM-ORDERED-PRODUCTION-INFO     REDEFINES PM-ORDERED-INFO.        
014800         10  FILLER                        PIC X(12).                     
014810         10  H203-SEGMENT-ID               PIC X(13).                     
014900         10  H203-MET-REASON-CD-2          PIC XX.                        
015000         10  H203-H-JBODY                  PIC X.                         
015100             88  H203-JBODY                VALUE 'J'.                     
015200         10  H203-ORD-PRODUCTION-NUMBER.                                  
015300             88  H203-UNPLANNED-STOCK      VALUE ALL '0'.                 
015400             15  H203-ORD-DISTRICT         PIC X(3).                      
015500                 88  H203-PLANNED-STOCK    VALUE '066'.                   
015600             15  H203-ORD-BASE             PIC X(5).                      
015700             15  H203-ORD-SUFFIX           PIC X.                         
015800             15  H203-ORD-SPLIT-KEY.                                      
015900                 20  H203-ORD-SPLIT        PIC XX.                        
016000                 20  H203-ORD-SUBSPLIT     PIC XX.                        
016100                 20  H203-ORD-REROLL       PIC XX.                        
016200         10  H203-CUSTOMER-SHORTY-NAME     PIC X(7).                      
016300         10  H203-CAR-NO-HOLD.                                            
016400             15  H203-SLABS                PIC XX.                        
016500             15  H203-TAP-TIME             PIC X(4).                      
016600             15  H203-CAR-NUM-CAST         PIC X(4).                      
016700         10  H203-INGOT-POUR-HEIGHT        PIC 999.                       
016800         10  H203-SOAKING-PIT-NO           PIC 999.                       
016900         10  H203-SOAKING-PIT-TIME         PIC 9(4).                      
017000         10  H203-WEIGHED-INDICATOR        PIC X.                         
017100         10  H203-PRIMARY-SLAB-RATING-CD   PIC X.                         
017200         10  H203-PHOSPHORUS               PIC V999.                      
017300         10  H203-SULPHUR                  PIC V999.                      
017400         10  H203-COPPER                   PIC V999.                      
017500         10  H203-VANADIUM                 PIC V999.                      
017600         10  H203-TITANIUM                 PIC V999.                      
017700         10  H203-COLUMBIUM                PIC V999.                      
017800         10  H203-ALUMINUM                 PIC 9V999.                     
017900         10  H203-NICKEL                   PIC 9V99.                      
018000         10  H203-CHROMIUM                 PIC 9V99.                      
018100         10  H203-TIN                      PIC V999.                      
018200         10  H203-MOLYBDENUM               PIC V999.                      
018300         10  H203-NITROGEN                 PIC V999.                      
018400         10  H203-PLANNED-DESTINATION      PIC X.                         
018500             88  H203-DEDICATION-PARKING-LOT VALUE 'D'.                   
018600             88  H203-REGULAR-STOCK          VALUE 'S'.                   
018700             88  H203-NORMAL-DELIVERY        VALUE 'N'.                   
018800             88  H203-DIRECT-ROLLING         VALUE 'R'.                   
018900         10  H203-STOCK-INDICATOR          PIC X.                         
019000             88  H203-STOCK-SLAB-ORDER       VALUE 'S'.                   
019100             88  H203-BONAFIDE-SLAB-ORDER    VALUE 'B'.                   
019200         10  H203-OFF-CENTER-SLIT-INDICATOR  PIC X.                       
019300             88  H203-OFF-CENTER-SLITTING    VALUE 'O'.                   
019400         10  H203-OPERATOR                 PIC 999   COMP-3.              
019500         10  H203-OVERAGE-FLAG             PIC X(1).                      
019501         10  H203-FILLER2                  PIC X(5).                      
019510******************************************************************        
019600     05  H203-EXTRA-INFO                REDEFINES PM-ORDERED-INFO.        
019700         10  FILLER                        PIC X(111).                    
019800         10  H203-US-HEAT-NO               PIC X(9).                      
019900******************************************************************        
020000     05  H203-LACK-INFO                 REDEFINES PM-ORDERED-INFO.        
020100         10  H203-LACK-TRAIN               PIC XXX.                       
020200         10  H203-LACK-SHIP-DATE.                                         
020300             15  H203-LACK-SHIP-YEAR       PIC 99.                        
020400             15  H203-LACK-SHIP-MONTH      PIC 99.                        
020500             15  H203-LACK-SHIP-DAY        PIC 99.                        
020600         10  H203-LACK-MET-GRADE           PIC XXX.                       
020700         10  H203-LACK-CAR-NO              PIC X(9).                      
020800         10  H203-LACK-MANIFEST-NO         PIC 9(4).                      
020900         10  H203-FILLER                   PIC X(95).                     
021000******************************************************************        
021100     05  H203-ID-SLAB-ORD-INFO          REDEFINES PM-ORDERED-INFO.        
021200         10  H203-ID-SLAB-ORD              PIC X(13).                     
021300         10  H203-ID-MOTHER-SLAB-ORD       PIC 9(4).                      
021310         10  FILLER                        PIC X(103).                    
021400******************************************************************        
052400 COPY ADP03SS1.                                                   05300019
000010*****************************************************************         
000020*                                                               *         
000030*     THIS IS THE SEGMENT SEARCH ARGUMENT FOR A QUALIFIED       *         
000040*  CALL FOR SEGMENT HLFDP03A OF THE WORK DATA BASE - HLFDPP03   *         
000050*                                                               *         
000060*****************************************************************         
000070     SKIP1                                                                
000080 01  ADP03SS1-SSA.                                                        
000090     05  ADP03SS1-SEG-NAME         PIC X(8)   VALUE   'HLFDP03A'.         
000100     05  ADP03SS1-COMMAND-CODE-IND PIC X(1)   VALUE   '*'.                
000110     05  ADP03SS1-COMMAND-CODE     PIC X(1)   VALUE   '-'.                
000120     05  ADP03SS1-QUALIFIER        PIC X(1)   VALUE   '('.                
000130     05  ADP03SS1-FLD-NAME         PIC X(8)   VALUE   'ADP03KEY'.         
000140     05  ADP03SS1-OPERATOR         PIC X(2)   VALUE   ' ='.               
000150     05  ADP03SS1-KEY-VALUE        PIC X(16).                             
000160     05  ADP03SS1-END-CHAR         PIC X(1)   VALUE   ')'.                
052500 COPY DDP03SS1.                                                   05310019
000010*****************************************************************         
000020*                                                               *         
000030*     THIS IS THE SEGMENT SEARCH ARGUMENT FOR A QUALIFIED       *         
000040*  CALL FOR SEGMENT HLFDP03D OF THE WORK DATA BASE - HLFDPP03   *         
000050*                                                               *         
000060*****************************************************************         
000070     SKIP1                                                                
000080 01  DDP03SS1-SSA.                                                        
000090     05  DDP03SS1-SEG-NAME         PIC X(8)   VALUE   'HLFDP03D'.         
000100     05  DDP03SS1-COMMAND-CODE-IND PIC X(1)   VALUE   '*'.                
000110     05  DDP03SS1-COMMAND-CODE     PIC X(1)   VALUE   '-'.                
000120     05  DDP03SS1-QUALIFIER        PIC X(1)   VALUE   '('.                
000130     05  DDP03SS1-FLD-NAME         PIC X(8)   VALUE   'DDP03KEY'.         
000140     05  DDP03SS1-OPERATOR         PIC X(2)   VALUE   ' ='.               
000150     05  DDP03SS1-KEY-VALUE        PIC X(8).                              
000160     05  DDP03SS1-END-CHAR         PIC X(1)   VALUE   ')'.                
052600* THIS EMAIL IS FOR FOOT PRINT                                    05320019
052700 COPY HLFGNKML.                                                   05330019
000100 01  HLFGNKML-MSG-IN.                                                     
000200     05 EMAIL-TRANS-LL         PIC S9999 VALUE 0 COMP.                    
000300     05 EMAIL-TRANS-ZZ         PIC S999 VALUE +000 COMP.                  
000400     05 EMAIL-TRANS-NAME       PIC X(9) VALUE 'HLFGNFAX '.                
006020*******************************************************************       
006030*  EMAIL-HEADER-LINE CONTAINS THE INFO TO SEND AN EMAIL           *       
006031*  THE VARIABLES WILL BE FILLED IN BY THE SENDING PGM             *       
006040*******************************************************************       
006042 05  EMAIL-INFORMATION.                                                   
006043     10  EMAIL-LINE-1.                                                    
006044         15  FILLER             PIC X(80) VALUE '{{BEGIN EMAIL}}'.        
006045     10  EMAIL-LINE-2.                                                    
006046         15  FILLER             PIC X(7)  VALUE '{{FROM '.                
006047         15  EMAIL-FROM         PIC X(50).                                
006048         15  FILLER             PIC X(23) VALUE '}}'.                     
006049     10  EMAIL-LINE-3.                                                    
006050         15 FILLER              PIC X(10) VALUE '{{SUBJECT '.             
006051         15 EMAIL-SUBJECT       PIC X(50).                                
006052         15 FILLER              PIC X(20) VALUE '}}'.                     
006053     10  EMAIL-LINE-4           PIC X(80) VALUE SPACES.                   
006057     10  EMAIL-LINE-5           PIC X(80) VALUE SPACES.                   
006062     10  EMAIL-LINE-6           PIC X(80) VALUE SPACES.                   
006063     10  EMAIL-LINE-7           PIC X(80) VALUE SPACES.                   
006064     10  EMAIL-LINE-8           PIC X(80) VALUE SPACES.                   
006065     10  EMAIL-LINE-9           PIC X(80) VALUE SPACES.                   
006095   05  EMAIL-HEADER-BREAKDOWN  REDEFINES EMAIL-INFORMATION.               
006096     10  EMAIL-HEADER-LINE      OCCURS 9 TIMES PIC X(80).                 
006102                                                                          
006110   05  EMAIL-TRAILER-PAGE.                                                
006200       10  EMAIL-LINE-10.                                                 
006300           15  FILLER             PIC X(7)  VALUE '{{END}}'.              
006400           15  FILLER             PIC X(73) VALUE SPACES.                 
006410                                                                          
006500     05  EMAIL-TEXT-SUB           PIC S999   VALUE ZEROS.                 
006600     05  EMAIL-TEXT-LIMIT         PIC S999   VALUE +200.                  
006700                                                                          
006800     05  EMAIL-DETAIL-INFO.                                               
006900         10  EMAIL-TEXT-LINE     OCCURS 200 TIMES PIC X(80).              
007000                                                                          
007100     05  EMAIL-PRINTER-ID.                                                
007200         10  EMAIL-PRINTER-NAME   PIC X(8)  VALUE SPACES.                 
007300     05  KML-EMAIL-OR-FAX-SWITCH      PIC X     VALUE SPACES.             
007310         88  KML-SEND-EMAIL           VALUE 'E'.                          
007320         88  KML-SEND-FAX             VALUE 'F' ' '.                      
007400                                                                          
052800* THIS EMAIL IS FOR DUP RECORDS                                   05340019
052900 01  HLFGNKML-EML-IN.                                             05350019
053000     05 EML-TRANS-LL           PIC S9999 VALUE 0 COMP.            05360019
053100     05 EML-TRANS-ZZ           PIC S999 VALUE +000 COMP.          05370019
053200     05 EML-TRANS-NAME         PIC X(9) VALUE 'HLFGNFAX '.        05380019
053300 05  EML-INFORMATION.                                             05390019
053400     10  EML-LINE-1.                                              05400019
053500         15  FILLER             PIC X(80) VALUE '{{BEGIN EMAIL}}'.05410019
053600     10  EML-LINE-2.                                              05420019
053700         15  FILLER             PIC X(7)  VALUE '{{FROM '.        05430019
053800         15  EML-FROM           PIC X(50).                        05440019
053900         15  FILLER             PIC X(23) VALUE '}}'.             05450019
054000     10  EML-LINE-3.                                              05460019
054100         15 FILLER              PIC X(10) VALUE '{{SUBJECT '.     05470019
054200         15 EML-SUBJECT         PIC X(50).                        05480019
054300         15 FILLER              PIC X(20) VALUE '}}'.             05490019
054400     10  EML-LINE-4             PIC X(80) VALUE SPACES.           05500019
054500     10  EML-LINE-5             PIC X(80) VALUE SPACES.           05510019
054600     10  EML-LINE-6             PIC X(80) VALUE SPACES.           05520019
054700     10  EML-LINE-7             PIC X(80) VALUE SPACES.           05530019
054800     10  EML-LINE-8             PIC X(80) VALUE SPACES.           05540019
054900     10  EML-LINE-9             PIC X(80) VALUE SPACES.           05550019
055000   05  EML-HEADER-BREAKDOWN    REDEFINES EML-INFORMATION.         05560019
055100     10  EML-HEADER-LINE        OCCURS 9 TIMES PIC X(80).         05570019
055200                                                                  05580019
055300   05  EML-TRAILER-PAGE.                                          05590019
055400       10  EML-LINE-10.                                           05600019
055500           15  FILLER             PIC X(7)  VALUE '{{END}}'.      05610019
055600           15  FILLER             PIC X(73) VALUE SPACES.         05620019
055700                                                                  05630019
055800     05  EML-TEXT-SUB             PIC S999   VALUE ZEROS.         05640019
055900     05  EML-TEXT-LIMIT           PIC S999   VALUE +200.          05650019
056000                                                                  05660019
056100     05  EML-DETAIL-INFO.                                         05670019
056200         10  EML-TEXT-LINE       OCCURS 200 TIMES PIC X(80).      05680019
056300                                                                  05690019
056400     05  EML-PRINTER-ID.                                          05700019
056500         10  EML-PRINTER-NAME     PIC X(8)  VALUE SPACES.         05710019
056600     05  KML-EML-OR-FAX-SWITCH        PIC X     VALUE SPACES.     05720019
056700         88  KM2-SEND-EMAIL           VALUE 'E'.                  05730019
056800         88  KM2-SEND-FAX             VALUE 'F' ' '.              05740019
057000******************************************************************05750019
057100* THE NEXT 2 COPIES WILL BE USED FOR MAKING PATH CALLS.           05760019
057200* KEEP THE LAYOUTS TOGETHER (HLF1301A & HLF1301B).                05770019
057300******************************************************************05780019
057400 COPY HLF1301A.                                                   05790019
000100******************************************************************00000100
000200*    BEGIN COPY CLAUSE HLF1301A - PROVIDING DB ORDER SEGMENT     *00000200
000300*           LENGTH = 448                    LAST UPDATED 02/09/84*00000300
000400*    10/23/96 ADDED WHERE MEASURED FIELDS FOR GAUGE TOLERANCE     00000400
000500*             ENHANCEMENT - DAS                                   00000500
000600*    10/03/05 ADD 'B' AS A VALUE FOR A1301-HOT-ROLL-PLUS          00000600
000610*    04/10/15 QA5R5246 ADDED NEW MILL PRACTICE CODE 'V'           00000610
000620*    07/19/17 LF7R7390 ADDED MADE TO FORECAST & ORDER FIELDS      00000620
000700*                                                                 00000700
000800******************************************************************00000800
000900                                                                  00000900
001000 01  HLF1301A.                                                    00001000
001100     03  A1301-PRODUCTION-ORDER-NO.                               00001100
001200         05  A1301-ORDER-DISTRICT       PIC XXX   VALUE SPACE.    00001200
001300         05  A1301-ORDER-BASE           PIC X(5)  VALUE SPACE.    00001300
001400         05  A1301-ORDER-SUFFIX         PIC X     VALUE SPACE.    00001400
001500                                                                  00001500
001600     03  A1301-GROUP-ROLLING-NO                                   00001600
001700                        REDEFINES A1301-PRODUCTION-ORDER-NO.      00001700
001800         05  FILLER                     PIC XXX.                  00001800
001900         05  A1301-GROUP-ROLLING-NUMBER PIC X(5).                 00001900
002000         05  FILLER                     PIC X.                    00002000
002100                                                                  00002100
002200     03  A1301-INQUIRY-NUMBER                                     00002200
002300                      REDEFINES A1301-PRODUCTION-ORDER-NO.        00002300
002400         05  A1301-INQUIRY-DISTRICT     PIC X(3).                 00002400
002500         05  A1301-INQUIRY-BASE         PIC X(5).                 00002500
002600         05  A1301-INQUIRY-CHECK REDEFINES A1301-INQUIRY-BASE.    00002600
002700             07  A1301-INQUIRY-IDENTIFIER  PIC X.                 00002700
002800                 88  A1301-INQUIRY                VALUE 'Q'.      00002800
002900             07  FILLER                 PIC XXXX.                 00002900
003000         05  A1301-INQUIRY-SUFFIX       PIC X.                    00003000
003100                                                                  00003100
003200     03  A1301-REPLACE-ORDER-NUMBER                               00003200
003300                      REDEFINES A1301-PRODUCTION-ORDER-NO.        00003300
003400         05  A1301-REPLACE-DISTRICT     PIC XXXX.                 00003400
003500         05  A1301-REPLACE-BASE         PIC X(4).                 00003500
003600         05  A1301-REPLACE-SUFFIX       PIC X.                    00003600
003700                                                                  00003700
003800     03  A1301-CUSTOMER-NAME.                                     00003800
003900         05  A1301-CUSTOMER-SHORTY-NAME PIC X(7)  VALUE SPACE.    00003900
004000             88  A1301-LACK-ORDER    VALUE 'LCR    ' 'LCRSTK '    00004000
004100                                           'LGAL   ' 'LGALSTK'    00004100
004200                                           'LHRPG  ' 'LHRPGS '    00004200
004300                                           'LACK   '.             00004300
004400             88  A1301-LACK-CR       VALUE 'LCR    '.             00004400
004500             88  A1301-LACK-CR-STK   VALUE 'LCRSTK '.             00004500
004600             88  A1301-LACK-GALV     VALUE 'LGAL   '.             00004600
004700             88  A1301-LACK-GALV-STK VALUE 'LGALSTK'.             00004700
004800             88  A1301-LACK-HRPG     VALUE 'LHRPG  '.             00004800
004900             88  A1301-LACK-HRPGS    VALUE 'LHRPGS '.             00004900
005000         05  A1301-CUSTOMER-DESTINATION PIC XX    VALUE SPACE.    00005000
005100     03  A1301-ACTION-CODE              PIC X     VALUE SPACE.    00005100
005200     03  A1301-PLANT-PRIORITY-CODE      PIC X     VALUE SPACE.    00005200
005300                                                                  00005300
005400     03  A1301-ORDER-GAUGE-CODE         PIC X     VALUE SPACE.    00005400
005500         88  A1301-ACTUAL-GAUGE                   VALUE 'A'.      00005500
005600         88  A1301-MINIMUM-GAUGE                  VALUE 'M'.      00005600
005700     03  A1301-ORDER-GAUGE              PIC V9(4) VALUE ZERO.     00005700
005800     03  A1301-ORDER-WIDTH.                                       00005800
005900         05  A1301-ORD-WID-WHOLE        PIC 99    VALUE ZERO.     00005900
006000         05  A1301-ORD-WID-NUM          PIC 99    VALUE ZERO.     00006000
006100         05  A1301-ORD-WID-DEN          PIC 99    VALUE ZERO.     00006100
006200     03  A1301-ORDER-LENGTH.                                      00006200
006300         05  A1301-ORD-LEN-WHOLE        PIC 999   VALUE ZERO.     00006300
006400         05  A1301-ORD-LEN-NUM          PIC 99    VALUE ZERO.     00006400
006500         05  A1301-ORD-LEN-DEN          PIC 99    VALUE ZERO.     00006500
006600     03  A1301-ORDER-WEIGHT             PIC S9(9) COMP-3 VALUE +0.00006600
006700     03  A1301-MIN-COIL-WEIGHT          PIC S9(5) VALUE +0.       00006700
006800     03  A1301-MAX-COIL-WEIGHT          PIC S9(5) VALUE +0.       00006800
006900                                                                  00006900
007000     03  A1301-TYPE-TRANSPORT-CODE      PIC XX    VALUE SPACE.    00007000
007100     03  A1301-PLANT-PRODUCT-CODE.                                00007100
007200         05  A1301-PRODUCT-CODE-BYTE1   PIC X     VALUE SPACE.    00007200
007300             88  A1301-HOT-ROLL          VALUES '1' THRU '9'      00007300
007400                                                'A' '-' 'B'       00007400
007500                                                'T' 'V'           00007500
007600                                                'W' 'X' 'Y' 'Z'.  00007600
007700             88  A1301-COLD-ROLL      VALUES 'C' THRU 'H' 'J' 'K' 00007700
007800                                                'R' 'S' '0'.      00007800
007900             88  A1301-TIN-PLATE         VALUES 'L' THRU 'Q'.     00007900
008000             88  A1301-HRP-CM            VALUES '0'.              00008000
008100             88  A1301-HRPO-OUTSIDE-CM   VALUES 'W' 'X' 'Y' 'Z'.  00008100
008200             88  A1301-BH-GALV           VALUES 'U' 'I'.          00008200
008300             88  A1301-CHROMIZED         VALUES 'W' 'X'.          00008300
008400             88  A1301-GALVANIZED        VALUES 'T' 'V'.          00008400
008500             88  A1301-NONE-SPECIFIED    VALUE  'Z'.              00008500
008600             88  A1301-HOT-DIP           VALUES 'R' 'S'.          00008600
008700         05  A1301-PRODUCT-CODE-BYTE2   PIC X     VALUE SPACE.    00008700
008800             88  A1301-FINISHED-SPECIAL  VALUE  '6'.              00008800
008900                                                                  00008900
009000     03  A1301-MILL-INSTRUCTIONS.                                 00009000
009100         05  A1301-MILL-INSTRUCTION-OCCR    OCCURS 6              00009100
009200                                        INDEXED BY A1301-MI-INX.  00009200
009300             07  A1301-MILL-INSTR        PIC X(4).                00009300
009400                                                                  00009400
009500*    *************************************************************00009500
009600*    THIS DATA FROM MET DRESSING                                  00009600
009700*    *************************************************************00009700
009800                                                                  00009800
009900     03  A1301-STEEL-GRADE-ORDER.                                 00009900
010000         04  A1301-STL-GRD-ORD-CHAR-1    PIC X     VALUE SPACE.   00010000
010100         04  A1301-STL-GRD-ORD-CHAR-2    PIC X     VALUE SPACE.   00010100
010200         04  A1301-STL-GRD-ORD-CHAR-3-5  PIC XXX   VALUE SPACE.   00010200
010300         04  A1301-STL-GRD-ORD-CHAR-6    PIC X     VALUE SPACE.   00010300
010400         04  A1301-STL-GRD-ORD-CHAR-7    PIC X     VALUE SPACE.   00010400
010500     03  A1301-STEEL-GRADE-ORDER-2ND.                             00010500
010600         04  A1301-STL-GRD-ORD-2ND-CHAR-1    PIC X   VALUE SPACE. 00010600
010700         04  A1301-STL-GRD-ORD-2ND-CHAR-2    PIC X   VALUE SPACE. 00010700
010800         04  A1301-STL-GRD-ORD-2ND-CHAR-3-5  PIC XXX VALUE SPACE. 00010800
010900         04  A1301-STL-GRD-ORD-2ND-CHAR-6    PIC X   VALUE SPACE. ********
011000         04  A1301-STL-GRD-ORD-2ND-CHAR-7    PIC X   VALUE SPACE. ********
011100     03  A1301-CHEMISTRY-APPLICATION     PIC X(4)  VALUE SPACE.   ********
011200     03  A1301-CHEMISTRY-APPL-EXCEPT     PIC X(4)  VALUE SPACE.   ********
011300     03  A1301-SLAB-GRADING              PIC X     VALUE SPACE.   ********
011400     03  A1301-FINISHING-COILING-TEMP    PIC XX    VALUE SPACE.   ********
011500     03  A1301-PRODUCT-ACCOUNT           PIC X(4)  VALUE SPACE.   ********
011600     03  A1301-MET-TEST-CODE             PIC X(6)  VALUE SPACE.   ********
011700     03  A1301-MET-TEST-CODE-REDEF  REDEFINES A1301-MET-TEST-CODE.********
011800         05  A1301-MET-TEST-CODE-ONE     PIC X.                   ********
011900         05  FILLER                      PIC X(5).                ********
012000     03  A1301-WELDS                     PIC XX     VALUE SPACE.  ********
012100     03  A1301-EXPOSED-IND               PIC X      VALUE SPACE.  ********
012200         88  A1301-EXPOSED                          VALUE 'X'.    ********
012300         88  A1301-NOT-EXPOSED                      VALUE ' '.    ********
012400                                                                  ********
012500**************************************************************************
012600*    THIS DATA DEVELOPED BY PROVIDING CALCULATIONS *              ********
012700******************************************************************00012700
012800                                                                  00012800
012900     03  A1301-MOLD-ORDERED            PIC XX       VALUE SPACE.  00012900
013000     03  A1301-CUSTOM-CUT              PIC X        VALUE SPACE.  00013000
013100         88  A1301-CUSTOM                    VALUE 'C'.           00013100
013200         88  A1301-NORMAL                    VALUE 'N'.           00013200
013300     03  A1301-CUTTING-PRACTICE        PIC X        VALUE SPACE.  00013300
013400         88  A1301-PRODUCT-OF-INGOT          VALUE '1'.           00013400
013500         88  A1301-WHOLE                     VALUE '1'.           00013500
013600         88  A1301-ONE-HALF                  VALUE '2'.           00013600
013700         88  A1301-ONE-THIRD                 VALUE '3'.           00013700
013800         88  A1301-ONE-THIRD-TWO-THIRDS      VALUE '4'.           00013800
013900         88  A1301-TWO-THIRDS                VALUE '5'.           00013900
014000         88  A1301-MANUAL-OVERRIDE           VALUE '6'.           00014000
014100                                                                  00014100
014200     03  A1301-WIDTH-MULTS             PIC S99      VALUE +0.     00014200
014300                                                                  00014300
014400     03  A1301-MIN-SLAB-LENGTH-TEEM    PIC S999     VALUE +0.     00014400
014500     03  A1301-MAX-SLAB-LENGTH-TEEM    PIC S999     VALUE +0.     00014500
014600     03  A1301-MIN-SLAB-LENGTH-CAST    PIC S999     VALUE +0.     00014600
014700     03  A1301-MAX-SLAB-LENGTH-CAST    PIC S999     VALUE +0.     00014700
014800     03  A1301-SLAB-LENGTH-PREFER      PIC S999     VALUE +0.     00014800
014900                                                                  00014900
015000     03  A1301-ORDER-SLAB-THICK-TEEM   PIC 99V9     VALUE ZERO.   00015000
015100     03  A1301-ORDER-SLAB-THICK-CAST   PIC 99V9     VALUE ZERO.   00015100
015200                                                                  00015200
015300     03  A1301-PIW-LIMIT-WGT-MIN       PIC 9(5)     VALUE ZERO.   00015300
015400     03  A1301-PIW-LIMIT-WGT-MAX       PIC 9(5)     VALUE ZERO.   00015400
015500     03  A1301-MIN-SLAB-WEIGHT-TEEM    PIC S9(5)    VALUE +0.     00015500
015600     03  A1301-MAX-SLAB-WEIGHT-TEEM    PIC S9(5)    VALUE +0.     00015600
015700     03  A1301-MIN-SLAB-WEIGHT-CAST    PIC S9(5)    VALUE +0.     00015700
015800     03  A1301-MAX-SLAB-WEIGHT-CAST    PIC S9(5)    VALUE +0.     00015800
015900                                                                  00015900
016000     03  A1301-MIN-SLAB-WGT-ALTERNATE1 PIC S9(5)    VALUE +0.     00016000
016100     03  A1301-MAX-SLAB-WGT-ALTERNATE1 PIC S9(5)    VALUE +0.     00016100
016200     03  A1301-MIN-SLAB-WGT-ALTERNATE2 PIC S9(5)    VALUE +0.     00016200
016300     03  A1301-MAX-SLAB-WGT-ALTERNATE2 PIC S9(5)    VALUE +0.     00016300
016400     03  A1301-MIN-SLAB-WGT-ALTERNATE3 PIC S9(5)    VALUE +0.     00016400
016500     03  A1301-MAX-SLAB-WGT-ALTERNATE3 PIC S9(5)    VALUE +0.     00016500
016600                                                                  00016600
016700     03  A1301-EXPECTED-COIL-WGT-TEEM  PIC S9(5)    VALUE +0.     00016700
016800     03  A1301-EXPECTED-COIL-WGT-CAST  PIC S9(5)    VALUE +0.     00016800
016900                                                                  00016900
017000     03  A1301-GAUGE-TOLERANCE-MINUS   PIC V9(4)    VALUE ZERO.   00017000
017100     03  A1301-GAUGE-TOLERANCE-PLUS    PIC V9(4)    VALUE ZERO.   00017100
017200     03  A1301-GAUGE-TOL-OVERRIDE-IND  PIC X        VALUE SPACE.  00017200
017300     03  A1301-WIDTH-TOLERANCE         PIC 9V9(4)   VALUE ZERO.   00017300
017400     03  A1301-WIDTH-TOL-OVERRIDE-IND  PIC X        VALUE SPACE.  00017400
017500                                                                  00017500
017600******************************************************************00017600
017700*    END OF PROVIDING CALC DATA *                                 00017700
017800******************************************************************00017800
017900                                                                  00017900
018000     03  A1301-CAPTION-CODE            PIC X(5)     VALUE SPACE.  00018000
018100     03  A1301-CAPTION-CODE-REDEF  REDEFINES A1301-CAPTION-CODE.  00018100
018200         05  A1301-CAPTION             PIC X(3).                  00018200
018300         05  A1301-EDGE                PIC X.                     00018300
018400         05  FILLER                    PIC X.                     00018400
018500                                                                  00018500
018600     03  A1301-STEEL-CHAR-CODE-GROUP.                             00018600
018700         05  A1301-YPROD               PIC XX     VALUE SPACE.    00018700
018800         05  A1301-YFURTH              PIC XX     VALUE SPACE.    00018800
018900         05  A1301-YSEC                PIC X      VALUE SPACE.    00018900
019000         05  A1301-YBASE               PIC XX     VALUE SPACE.    00019000
019100         05  A1301-YHEAT               PIC XX     VALUE SPACE.    00019100
019200         05  A1301-YCHEM               PIC X      VALUE SPACE.    00019200
019300         05  A1301-YOIL                PIC X      VALUE SPACE.    00019300
019400         05  A1301-YFIN                PIC XX     VALUE SPACE.    00019400
019500         05  A1301-YFORM               PIC XX     VALUE SPACE.    00019500
019600         05  A1301-YCOAT               PIC XX     VALUE SPACE.    00019600
019700*                                                                 00019700
019800     03  A1301-ORDER-BIRTHDATE     PIC S9(13) COMP-3  VALUE ZERO. 00019800
019900*                                                                 00019900
020000     03  A1301-STOCK-BONAFIDE-TAG  PIC X(15)      VALUE SPACE.    00020000
020100     03  A1301-PREFER-LENGTH-CAST      PIC S999     VALUE +0.     00020100
020200     03  A1301-PREFER-LENGTH-TEEM      PIC S999     VALUE +0.     00020200
020300     03  A1301-TEST-CODE               PIC X      VALUE SPACE.    00020300
020400     03  A1301-AUTOMOTIVE-INDICATOR    PIC X      VALUE 'N'.      00020400
020500         88  A1301-AUTOMOTIVE-YES                 VALUE 'Y'.      00020500
020600         88  A1301-AUTOMOTIVE-NO                  VALUE 'N'.      00020600
020700     03  A1301-FINISH-COIL-TEMP-2          PIC XX    VALUE SPACE. 00020700
020800******************************************************************00020800
020900**                                                              **00020900
021000**              ORDER STATUS FIELD ADDITIONS                    **00021000
021100**         M                                                    **00021100
021200******************************************************************00021200
021300     03  A1301-COND-CODE                   PIC X  VALUE SPACES.   00021300
021400         88  A1301-ORDER-SUSPEND-PROD        VALUE 'P'.           00021400
021500         88  A1301-ORDER-SUSPEND-SHIP        VALUE 'S'.           00021500
021600         88  A1301-ORDER-SUSPEND-BOTH        VALUE 'B'.           00021600
021700     03  A1301-SHIP-CANCEL-IND             PIC X  VALUE SPACES.   00021700
021800         88  A1301-SHIPPED-COMPLETE          VALUE 'S'.           00021800
021900         88  A1301-CANCELED                  VALUE 'C'.           00021900
022000     03  A1301-VACATION-SHUTDOWN-IND       PIC X  VALUE SPACES.   00022000
022100         88  A1301-VAC-SHUTDOWN              VALUE 'Y'.           00022100
022200     03  A1301-ORDER-WGT                 PIC 9(9) COMP-3 VALUE 0. 00022200
022300     03  A1301-CREDIT-RELEASE-WGT        PIC 9(9) COMP-3 VALUE 0. 00022300
022400     03  A1301-REVISION-NUMBER             PIC X  VALUE SPACES.   00022400
022500     03  A1301-REVISION-DATE-TIME.                                00022500
022600         05  A1301-REVISION-YR             PIC 99 VALUE ZERO.     00022600
022700         05  A1301-REVISION-MTH            PIC 99 VALUE ZERO.     00022700
022800         05  A1301-REVISION-DAY            PIC 99 VALUE ZERO.     00022800
022900         05  A1301-REVISION-HR             PIC 99 VALUE ZERO.     00022900
023000         05  A1301-REVISION-MIN            PIC 99 VALUE ZERO.     00023000
023100     03  A1301-STATUS-CNTL-WD.                                    00023100
023200         05  A1301-PLANT-HOLD              PIC X VALUE SPACES.    00023200
023300         05  FILLER                        PIC X VALUE SPACES.    00023300
023400         05  FILLER                        PIC X VALUE SPACES.    00023400
023500         05  FILLER                        PIC X VALUE SPACES.    00023500
023600         05  FILLER                        PIC X VALUE SPACES.    00023600
023700         05  FILLER                        PIC X VALUE SPACES.    00023700
023800         05  FILLER                        PIC X VALUE SPACES.    00023800
023900         05  FILLER                        PIC X VALUE SPACES.    00023900
024000         05  FILLER                        PIC X VALUE SPACES.    00024000
024100         05  FILLER                        PIC X VALUE SPACES.    00024100
024200     03  A1301-TRANS-TABLE.                                       00024200
024300         05  A1301-TRANS-CODE1             PIC X VALUE SPACES.    00024300
024400         05  A1301-TRANS-CODE2             PIC X VALUE SPACES.    00024400
024500         05  A1301-TRANS-CODE3             PIC X VALUE SPACES.    00024500
024600         05  A1301-TRANS-CODE4             PIC X VALUE SPACES.    00024600
024700         05  A1301-TRANS-CODE5             PIC X VALUE SPACES.    00024700
024800**                                                                00024800
024900     03  A1301-SEGMENT-CREATED-BY          PIC X VALUE SPACES.    00024900
025000         88  A1301-INSERTED-BY-OS     VALUE '1'.                  00025000
025100         88  A1301-UPDATED-BY-SPA     VALUE '2'.                  00025100
025200         88  A1301-SPA2-GROUP         VALUE '3'.                  00025200
025300**                                                                00025300
025400     03  A1301-MILL-CAN-SUPPLY-WT      PIC 9(5) VALUE ZEROS.      00025400
025500     03  A1301-MILL-CAN-SUPPLY-ORD   REDEFINES                    00025500
025600            A1301-MILL-CAN-SUPPLY-WT   PIC 999V99.                00025600
025700     03  A1301-MILL-CAN-SUPPLY-IND     PIC X VALUE SPACES.        00025700
025800         88  A1301-MILL-CAN-WT-IND           VALUE '#'.           00025800
025900         88  A1301-MILL-CAN-INCH-IND         VALUE SPACE.         00025900
026000**                                                                00026000
026100     03  A1301-MIN-SLAB-WGT-CAST-ALT1  PIC S9(5) COMP-3 VALUE +0. 00026100
026200     03  A1301-MAX-SLAB-WGT-CAST-ALT1  PIC S9(5) COMP-3 VALUE +0. 00026200
026300     03  A1301-MIN-SLAB-WGT-CAST-ALT2  PIC S9(5) COMP-3 VALUE +0. 00026300
026400     03  A1301-MAX-SLAB-WGT-CAST-ALT2  PIC S9(5) COMP-3 VALUE +0. 00026400
026500     03  A1301-MIN-SLAB-WGT-CAST-ALT3  PIC S9(5) COMP-3 VALUE +0. 00026500
026600     03  A1301-MAX-SLAB-WGT-CAST-ALT3  PIC S9(5) COMP-3 VALUE +0. 00026600
026700**                                                                00026700
026800     03  A1301-EXPEDITER-CODE              PIC X(5)  VALUE SPACES.00026800
026900     03  A1301-END-USE-DESCRIPTION         PIC X(40) VALUE SPACES.00026900
027000     03  A1301-MILL-PRACTICE.                                     00027000
027100         05  A1301-MILL-PRACTICE-CHAR-1    PIC X     VALUE SPACES.00027100
027200             88  A1301-HOT-ROLL-PLUS           VALUE 'H' 'B'.     00027200
027300             88  A1301-COLD-ROLL-PLUS VALUE 'C' 'D' 'G' 'S' 'X'.  00027300
027400             88  A1301-CR-PLUS-IF              VALUE 'G' 'X'.     00027400
027500             88  A1301-IF                  VALUE 'F' 'G' 'X' 'V'. 00027500
027600             88  A1301-SUPER-SURFACE-CRIT  VALUE 'S' 'X' 'V'.     00027600
027700             88  A1301-CR-PLUS-PHYS-QUAL       VALUE 'D'.         00027700
027800             88  A1301-PHYSICAL-QUALITY        VALUE 'P'.         00027800
027900         05  A1301-MILL-PRACTICE-CHAR-2    PIC X     VALUE SPACES.00027900
028000         05  A1301-MILL-PRACTICE-CHAR-3    PIC X     VALUE SPACES.00028000
028100     03  A1301-WHERE-MEASURED-FRAC         PIC 9(06).             00028100
028200     03  A1301-WHERE-MEASURED-FRAC-R REDEFINES                    00028200
028300         A1301-WHERE-MEASURED-FRAC.                               00028300
028400         05  A1301-WHERE-MEASURED-W        PIC 9(02).             00028400
028500         05  A1301-WHERE-MEASURED-N        PIC 9(02).             00028500
028600         05  A1301-WHERE-MEASURED-D        PIC 9(02).             00028600
028700     03  A1301-WHERE-MEASURED-FRAC-X REDEFINES                    00028700
028800         A1301-WHERE-MEASURED-FRAC         PIC X(06).             00028800
028900     03  A1301-PROJ-165-PLAN               PIC X(1).              00028900
029000         88  A1301-PLAN-A-RELEASED            VALUE 'A'.          00029000
029100         88  A1301-PLAN-I-NEGOTIATE           VALUE 'I' '0' '1'   00029100
029200                                '2' '3' '4' '5' '6' '7' '8' '9'.  00029200
029300         88  A1301-PLAN-K-SECONDARY           VALUE 'K'.          00029300
029400         88  A1301-PLAN-L-C9-STOCK-COILS      VALUE 'L'.          00029400
029500         88  A1301-PLAN-M-CONVERSION          VALUE 'M'.          00029500
029600         88  A1301-PLAN-P-C12-STOCK-SLAB      VALUE 'P'.          00029600
029700         88  A1301-PLAN-Q-PRODUCT-APP         VALUE 'Q'.          00029700
029800         88  A1301-PLAN-R-LACKAWANNA          VALUE 'R'.          00029800
029900         88  A1301-PLAN-S-CANCEL-ORDER        VALUE 'S'.          00029900
030000         88  A1301-PLAN-T-SGMT-ATTCHD         VALUE 'T'.          00030000
030100         88  A1301-PLAN-U-REG-MLT             VALUE 'U'.          00030100
030200         88  A1301-PLAN-V-FORCE-PART          VALUE 'V'.          00030200
030300         88  A1301-PLAN-W-DOUBLE-LENGTH       VALUE 'W'.          00030300
030400         88  A1301-PLAN-X-REG-MLT-LESS300     VALUE 'X'.          00030400
030500         88  A1301-PLAN-0-GRD-BASE-ERR        VALUE '0'.          00030500
030600         88  A1301-PLAN-1-HMRW-ERR            VALUE '1'.          00030600
030700         88  A1301-PLAN-2-WGT-ERR             VALUE '2'.          00030700
030800         88  A1301-PLAN-3-GRD-POS7-ERR        VALUE '3'.          00030800
030900         88  A1301-PLAN-4-GRD-POS6-ERR        VALUE '4'.          00030900
031000         88  A1301-PLAN-5-GRD-POS2-ERR        VALUE '5'.          00031000
031100     03  A1301-SEGMENT-ID.                                        00031100
031200         05  A1301-SEGMENT-GRADE           PIC X(7).              00031200
031300         05  A1301-SEGMENT-WIDTH           PIC X(3).              00031300
031400         05  A1301-SEGMENT-LENGTH          PIC X(3).              00031400
031410     03  A1301-MTF-MTO-MTOM-IND            PIC X(3).              00031410
031420         88  A1301-MADE-TO-FORECAST           VALUE 'MTF'.        00031420
031430         88  A1301-MADE-TO-ORDER              VALUE 'MTO'.        00031430
031440         88  A1301-MADE-TO-ORDER-MANAGED      VALUE 'MTM'.        00031440
031500     03  FILLER                            PIC X(28) VALUE SPACES.00031500
031600******************************************************************00031600
031700*               END COPY CLAUSE HLF1301A                         *00031700
031800******************************************************************00031800
057500 COPY HLF1301B.                                                   05800019
000100******************************************************************        
000200* 01/21/14 GLM LF3R3322 - ADD B1301-ORD-MIN-SLB-WID AND          *        
000210*                             B1301-ORD-MAX-SLB-WID              *        
000220*                                                                         
000230*    BEGIN COPY CLAUSE HLF1301B - PROVIDING DB SPLIT SEGMENT     *        
000300*           LENGTH = 776             LAST UPDATED  11/30/92 VLH  *        
000400******************************************************************        
000500 01  HLF1301B.                                                            
000600     03  B1301-SPLIT-KEY.                                                 
000700         05  B1301-SPLIT-NUMBER        PIC XX         VALUE SPACE.        
000800         05  B1301-SUBSPLIT-NBR        PIC XX         VALUE SPACE.        
000900         05  B1301-REROLL-NBR          PIC XX         VALUE SPACE.        
001000     03  B1301-PRODUCTION-ORDER-NO.                                       
001100         05  B1301-ORDER-DISTRICT      PIC XXX        VALUE SPACE.        
001200         05  B1301-ORDER-BASE          PIC X(5)       VALUE SPACE.        
001300         05  B1301-ORDER-SUFFIX        PIC X          VALUE SPACE.        
001400     03  B1301-GROUP-ROLLING-NUMBER    PIC X(5)       VALUE SPACE.        
001500     03  B1301-C-M-STL-ITEM-NUMBER.                                       
001600         05  B1301-C-M-STL-ITM-PROD    PIC X          VALUE SPACE.        
001700         05  B1301-C-M-STL-ITM-TM-WK   PIC XX         VALUE SPACE.        
001800         05  B1301-C-M-STL-ITM-NMBR    PIC X(4)       VALUE SPACE.        
001900         05  B1301-C-M-STL-ITM-SUFX    PIC X          VALUE SPACE.        
002000     03  B1301-SPLIT-STATUS            PIC X          VALUE SPACE.        
002100         88  B1301-FIRM                               VALUE 'F'.          
002200         88  B1301-UNFIRM                             VALUE 'U'.          
002300         88  B1301-SUSPENDED                          VALUE 'X'.          
002400         88  B1301-SHIPPED-COMPLETE                   VALUE 'C'.          
002500         88  B1301-SPLIT-CANCELLED                    VALUE 'A'.          
002600         88  B1301-SPLIT-TRANSFERRED                  VALUE 'T'.          
002700     03  B1301-SPLIT-51-IND            PIC X          VALUE SPACE.        
002800         88  B1301-LESS-51                            VALUE 'O'.          
002900         88  B1301-MORE-51                            VALUE 'A'.          
003000         88  B1301-NO-SLAB-TO-SCHEDULE                VALUE 'N'.          
003100     03  B1301-SCHEDULE-PRIORITY       PIC X          VALUE ZERO.         
003200         88  B1301-NO-PRIORITY                        VALUE '0'.          
003300         88  B1301-MANUAL-PRIORITY                    VALUE '1'.          
003400         88  B1301-COMPLETE-SHIPPABLE                 VALUE '2'.          
003500         88  B1301-INCOMPLETE-SHIPPABLE               VALUE '3'.          
003600         88  B1301-COMPLETE-NOT-SHIPPABLE             VALUE '4'.          
003700         88  B1301-INCOMPLETE-NOT-SHIPPABLE           VALUE '5'.          
003800         88  B1301-UNAVAIL-INC-SHIPPABLE              VALUE '6'.          
003900         88  B1301-UNAVAIL-INC-NOT-SHIPPABL           VALUE '7'.          
004000     03  B1301-SLAB-WIDTH-CAST-NEW     PIC 99V99.                         
004100     03  B1301-SCHED-WEEK.                                                
004200         05  B1301-SCHED-YR            PIC XX         VALUE SPACE.        
004300         05  B1301-SCHED-WK            PIC XX         VALUE SPACE.        
004400     03  B1301-TANDEM-WEEK.                                               
004500         05  B1301-TANDEM-YR           PIC XX         VALUE SPACE.        
004600         05  B1301-TANDEM-WK           PIC XX         VALUE SPACE.        
004700     03  B1301-PRODUCT-INDEX.                                             
004800         05  B1301-PROD-INDX-POS-1     PIC X.                             
004900             88  B1301-NO-VALUE                       VALUE '0'.          
005000             88  B1301-CONVERSION                     VALUE '1'.          
005100             88  B1301-HOT-ROLL-ANNEAL                VALUE '2'.          
005200             88  B1301-HOT-ROLL-LUBE                  VALUE '3'.          
005300             88  B1301-HOT-ROLL-PICKL-ANNEAL          VALUE '4'.          
005400             88  B1301-HR-ANNEAL-PICKL-LUBE           VALUE '5'.          
005500             88  B1301-HOT-ROLL-PICKLE                VALUE '6'.          
005600             88  B1301-HOT-ROLL-BLACK                 VALUE '7'.          
005700             88  B1301-TINPLATE                       VALUE '8'.          
005800             88  B1301-COLDROLL                       VALUE '9'.          
005900         05  B1301-PROD-INDX-POS-2     PIC X.                             
006000             88  B1301-NO-VALUE                       VALUE '0'.          
006100             88  B1301-SHEETS                         VALUE '1'.          
006200             88  B1301-SKIN-PASS-SLIT-COILS           VALUE '2'.          
006300             88  B1301-SLIT-COILS                     VALUE '3'.          
006400             88  B1301-SKIN-PASS-COILS                VALUE '4'.          
006500             88  B1301-SHIP-COILS                     VALUE '5'.          
006600             88  B1301-OTHER-PROCESSING               VALUE '9'.          
006700             88  B1301-GA-EXPOSED                     VALUE 'A'.          
006800             88  B1301-GA-UNEXPOSED                   VALUE 'B'.          
006900             88  B1301-GZ-EXPOSED                     VALUE 'C'.          
007000             88  B1301-GZ-UNEXPOSED                   VALUE 'D'.          
007100     03  B1301-PROVIDING-RESTRICT      PIC X          VALUE SPACE.        
007200         88  B1301-MLT-HLD-ROLL-NO-MELT      VALUE 'F'.                   
007300         88  B1301-MLT-HLD-MELT-NO-ROLL      VALUE 'N'.                   
007400         88  B1301-DO-NOT-PROVIDE-MANUAL     VALUE 'A' 'D' 'M'.           
007500         88  B1301-DO-NOT-PROVIDE-MECH       VALUE 'A' 'B' 'D'            
007600                                                   'E' 'G' 'H'            
007700                                                   'K' 'L' 'M'            
007800                                                   'R' 'S' 'U'            
007900                                                   'V' 'W' 'Y'            
008000                                                   'O'.                   
008100         88  B1301-DO-NOT-PROVIDE-SLABS      VALUE 'S' 'A' 'M'.           
008200         88  B1301-DO-NOT-STEAL              VALUE 'C'.                   
008300         88  B1301-DO-NOT-RELEASE-TO-HM      VALUE 'D'.                   
008400         88  B1301-APPLY-CUSTOM              VALUE 'B' 'G' 'Y'.           
008500         88  B1301-APPLY-HEAT-LOTS           VALUE 'H' 'G'.               
008600         88  B1301-DO-NOT-ORDER              VALUE 'D' 'I' 'J'            
008700                                                   'K' 'L' 'M'            
008800                                                   'R' 'S' 'U'            
008900                                                   'V' 'W'.               
009000         88  B1301-DO-NOT-ORDER-J            VALUE 'J'.                   
009100         88  B1301-NO-RESTRICT               VALUE 'X'.                   
009200         88  B1301-DO-NOT-SCHEDULE-HM        VALUE 'F' 'M' 'Y'            
009300                                                   'Z'.                   
009400         88  B1301-DO-NOT-SCHEDULE           VALUE 'D' 'L' 'M'            
009500                                                   'R' 'Y' 'Z'.           
009600         88  B1301-APPLICATION-MESSAGES      VALUE 'Q'.                   
009700         88  B1301-STOCK-PROGRAM             VALUE 'K' 'U' 'V'            
009800                                                   'W'.                   
009900         88  B1301-HOT-CHARGE-CUST-ORDER     VALUE 'H'.                   
010000         88  B1301-SPECIAL-CAST-INGOT-ORDER  VALUE 'O'.                   
010100     03  B1301-PROVIDING-STATUS        PIC X   VALUE SPACE.               
010200         88  B1301-PROVIDED-COMPLETE         VALUE 'C'.                   
010300         88  B1301-PROVIDED-DONE             VALUE 'D'.                   
010400         88  B1301-PROVIDED-PARTIAL          VALUE 'P'.                   
010500         88  B1301-NO-STEEL                  VALUE 'N'.                   
010600     03  B1301-SLAB-ORDER-STATUS       PIC X   VALUE SPACE.               
010700         88  B1301-NOT-RELEASED                VALUE 'N'.                 
010800         88  B1301-RELEASED                    VALUE 'R'.                 
010900         88  B1301-TEMP-LOCK                   VALUE 'T'.                 
011000         88  B1301-SUSPEND                     VALUE 'S'.                 
011100         88  B1301-CANCELLED                   VALUE 'X'.                 
011200         88  B1301-CUT-COMPLETE                VALUE 'C'.                 
011300     03  B1301-STOCK-SLAB-ORDER        PIC X   VALUE SPACE.               
011400         88  B1301-STOCK-SLAB-ORDR             VALUE 'S'.                 
011500         88  B1301-BONAFIDE-SLAB-ORDER         VALUE 'B'.                 
011600     03  B1301-SLAB-ORDER-TYPE         PIC X   VALUE SPACE.               
011700         88  B1301-FROM-SP-PT                  VALUE 'S'.                 
011800         88  B1301-FROM-LACK                   VALUE 'L'.                 
011900         88  B1301-SPCL-CM-BONAFIDE            VALUE 'B'.                 
012000         88  B1301-COLD-MILL-STOCK             VALUE 'C'.                 
012100         88  B1301-TINPLATE-STOCK              VALUE 'T'.                 
012200         88  B1301-HOT-MILL-STOCK              VALUE 'H'.                 
012300         88  B1301-SPECIAL-TYPE-1              VALUE '1'.                 
012400         88  B1301-SPECIAL-TYPE-2              VALUE '2'.                 
012500         88  B1301-SPECIAL-TYPE-3              VALUE '3'.                 
012600         88  B1301-SPECIAL-TYPE-4              VALUE '4'.                 
012700         88  B1301-SPECIAL-TYPE-5              VALUE '5'.                 
012800         88  B1301-NORMAL                      VALUE 'N'.                 
012900         88  B1301-OFF-CENTER-LINE-BURN        VALUE 'O'.                 
013000     03  B1301-PLANNED-DESTINATION     PIC X   VALUE SPACE.               
013100         88  B1301-DEDICATION-PARKING-LOT      VALUE 'D'.                 
013200         88  B1301-REGULAR-STOCK               VALUE 'S'.                 
013300         88  B1301-NORMAL-DELIVERY             VALUE 'N'.                 
013400         88  B1301-DIRECT-ROLLING              VALUE 'R'.                 
013500         88  B1301-SPECIAL-DEST-1              VALUE '1'.                 
013600         88  B1301-SPECIAL-DEST-2              VALUE '2'.                 
013700         88  B1301-SPECIAL-DEST-3              VALUE '3'.                 
013800     03  B1301-STEEL-SOURCE            PIC X   VALUE SPACE.               
013900         88  B1301-CASTER                      VALUE '1' '4' '7'.         
014000         88  B1301-TEEMED                      VALUE '2' '5' '8'.         
014100         88  B1301-BOTH                        VALUE '3' '6' '9'.         
014200         88  B1301-GUARANTEED                  VALUE '1' '2' '3'.         
014300         88  B1301-CRITICAL                    VALUE '4' '5' '6'.         
014400         88  B1301-IMPORTANT                   VALUE '7' '8' '9'.         
014500     03  B1301-SLAB-WIDTH-CAST         PIC 99  VALUE ZERO.                
014600     03  B1301-SLAB-WIDTH-TEEM         PIC 99  VALUE ZERO.                
014700*    ***********************************************************          
014800*    *    SECONDARY INDEX HLF13XAE REQUIRES SLAB ORDER WIDTH              
014900*    *        AND ALTERNATE GRADE TOGETHER                                
015000*    ***********************************************************          
015100     03  B1301-FULL-SLAB-ORDER-KEY.                                       
015200         05  B1301-SLAB-ORDER-WIDTH-KEY                                   
015300                                       PIC 99   VALUE ZERO.               
015400         05  B1301-STEEL-GRADE-ALTERNATE.                                 
015500             07  B1301-STL-GRD-ALT     PIC X(5) VALUE SPACE.              
015600             07  B1301-STL-GRD-SUFX-ALT                                   
015700                                       PIC XX   VALUE SPACE.              
015800     03  B1301-SLAB-ORDER-KEY REDEFINES                                   
015900                              B1301-FULL-SLAB-ORDER-KEY.                  
016000         05  B1301-SLAB-ORDER-WIDTH    PIC 99.                            
016100         05  B1301-STEEL-GRADE-ALT     PIC X(5).                          
016200         05                            PIC XX.                            
016300     03  B1301-STEEL-GRADE-TEEM.                                          
016400         05  B1301-STL-GRADE-TEEM      PIC X(5)  VALUE SPACE.             
016500         05  B1301-STL-GRD-SUFX-TEEM   PIC XX    VALUE SPACE.             
016600     03  B1301-STEEL-GRADE-CAST.                                          
016700         05  B1301-STL-GRADE-CAST      PIC X(5)  VALUE SPACE.             
016800         05  B1301-STL-GRD-SUFX-CAST   PIC XX    VALUE SPACE.             
016900     03  B1301-GAUGE-DEVIATION         PIC V9(4) VALUE ZERO.              
017000     03  B1301-HM-BAND-GAUGE           PIC V9(4) VALUE ZERO.              
017100     03  B1301-HM-BAND-GAUGE-OVERRIDE  PIC X     VALUE SPACE.             
017200     03  B1301-HM-BAND-WIDTH           PIC 99V99 VALUE ZERO.              
017300     03  B1301-HM-BAND-WIDTH-OVERRIDE  PIC X     VALUE SPACE.             
017400     03  B1301-CM-BAND-WIDTH           PIC 99V99 VALUE ZERO.              
017500******************************************************************        
017600* *                                                                       
017700*************     END OF SECONDARY INDEX FIELDS     **************        
017800* *                                                                       
017900******************************************************************        
018000     03  B1301-SPLIT-CHARGE-WEIGHT     PIC S9(9) COMP-3 VALUE +0.         
018100     03  B1301-SPLIT-CHARGE-WT-MIN     PIC S9(9) COMP-3 VALUE +0.         
018200     03  B1301-CM-BAND-WEIGHT          PIC S9(9) COMP-3 VALUE +0.         
018300     03  B1301-CM-BAND-WEIGHT-ADJ      PIC S9(9) COMP-3 VALUE +0.         
018400     03  B1301-PROVIDED-SLAB-WEIGHT    PIC S9(9) COMP-3 VALUE +0.         
018500     03  B1301-PROVIDED-COIL-WEIGHT    PIC S9(9) COMP-3 VALUE +0.         
018600     03  B1301-H-M-SCHEDULEABLE-WEIGHT PIC S9(9) COMP-3 VALUE +0.         
018700     03  B1301-HOT-ROLLED-WEIGHT       PIC S9(9) COMP-3 VALUE +0.         
018800     03  B1301-EXPECT-TO-SHIP-DATE.                                       
018900         05  B1301-EXPECT-TO-SHIP-YR   PIC 99         VALUE ZERO.         
019000         05  B1301-EXPECT-TO-SHIP-MO   PIC 99         VALUE ZERO.         
019100         05  B1301-EXPECT-TO-SHIP-DAY  PIC 99         VALUE ZERO.         
019200     03  B1301-NOT-BEFORE-DATE.                                           
019300         05  B1301-NOT-BEFORE-YEAR     PIC 99         VALUE ZERO.         
019400         05  B1301-NOT-BEFORE-MONTH    PIC 99         VALUE ZERO.         
019500         05  B1301-NOT-BEFORE-DAY      PIC 99         VALUE ZERO.         
019600     03  B1301-C-M-RELEASE-DATE        PIC S9(13) COMP-3                  
019700                                                      VALUE ZERO.         
019800     03  B1301-C-M-RELEASE-SWITCH      PIC X          VALUE SPACE.        
019900         88  B1301-RELEASED-CM                        VALUE 'R'.          
020000         88  B1301-HOLD-IN-CM                         VALUE 'H'.          
020100     03  B1301-PRIOR-NEGOTIATE         PIC XXX        VALUE SPACE.        
020200     03  B1301-GROUP-STATUS            PIC X          VALUE SPACE.        
020300         88  B1301-FUTURE-USE                         VALUE ' '.          
020400     03  B1301-GROUP-RESTRICT-CODE     PIC X          VALUE SPACE.        
020500         88  B1301-NO-RESTRICTION                     VALUE ' '.          
020600         88  B1301-NO-MECHANICAL-GROUPING             VALUE 'M'.          
020700         88  B1301-NO-GROUPING                        VALUE 'G'.          
020800     03  B1301-PROCESS-CODE            PIC XXX        VALUE SPACE.        
020900     03  B1301-SEQ-CODE-OCCURS.                                           
021000         05  B1301-SEQUENCE-CODE       OCCURS 11                          
021100                                       INDEXED BY B1301-SEQ-INX.          
021200             10  B1301-SEQ-UNIT-CODE   PIC XX.                            
021300             10  B1301-SEQ-LEAD-TIME   PIC 99.                            
021400*    **********************************************************           
021500*    **** THE FOLLOWING IS SLAB ORDER DATA ****                           
021600*    **********************************************************           
021700     03  B1301-SLAB-ORDER-BIRTHDATE.                                      
021800         05  B1301-SO-BIRTHDATE-YR     PIC 99         VALUE ZERO.         
021900         05  B1301-SO-BIRTHDATE-MO     PIC 99         VALUE ZERO.         
022000         05  B1301-SO-BIRTHDATE-DAY    PIC 99         VALUE ZERO.         
022100     03  B1301-SLAB-ORDER-COMPLETE-DATE.                                  
022200         05  B1301-SO-COMPLETE-YR      PIC 99         VALUE ZERO.         
022300         05  B1301-SO-COMPLETE-MO      PIC 99         VALUE ZERO.         
022400         05  B1301-SO-COMPLETE-DAY     PIC 99         VALUE ZERO.         
022500     03  B1301-SLAB-WEIGHT-ORDERED     PIC S9(5) COMP-3 VALUE +0.         
022600     03  B1301-NUM-FULL-SLABS-ORDER    PIC S9(5) COMP-3 VALUE +0.         
022700     03  B1301-NUM-FRAC-SLABS-ORDER    PIC S9(5) COMP-3 VALUE +0.         
022800     03  B1301-FRAC-SLAB-WGT-ORDER     PIC S9(5) COMP-3 VALUE +0.         
022900     03  B1301-FRAC-SLAB-LENGTH        PIC S999         VALUE +0.         
023000     03  B1301-FRAC-SLAB-THICKNESS     PIC S99V9        VALUE +0.         
023100     03  B1301-FRAC-SLAB-WIDTH         PIC S99          VALUE +0.         
023200     03  B1301-SLAB-ORDER-WEIGHT       PIC S9(9) COMP-3 VALUE +0.         
023300     03  B1301-SLAB-ORDER-WT-MIN       PIC S9(9) COMP-3 VALUE +0.         
023400     03  B1301-EXCESS-APPROVED         PIC X         VALUE SPACE.         
023500         88  B1301-APPROVED-EXCESS                   VALUE 'Y'.           
023600     03  B1301-SLAB-WGT-ON-STL-ORDER   PIC S9(9) COMP-3 VALUE +0.         
023700     03  B1301-ACCEPTED-WEIGHT         PIC S9(9) COMP-3 VALUE +0.         
023800     03  B1301-NUM-ACCEPTED-SLABS      PIC S9(5) COMP-3 VALUE +0.         
023900     03  B1301-REMAKE-PRIMARY-STL.                                        
024000         05  B1301-REMAKE-OCCURRENCE-PRISTL                               
024100                                       PIC 9          VALUE ZERO.         
024200         05  B1301-REMAKE-WEIGHT-PRISTL                                   
024300                                       PIC S9(9) COMP-3 VALUE +0.         
024400     03  B1301-REMAKE-HOT-MILL.                                           
024500         05  B1301-REMAKE-OCCURRENCE-HM                                   
024600                                       PIC 9          VALUE ZERO.         
024700         05  B1301-REMAKE-WEIGHT-HM    PIC S9(9) COMP-3 VALUE +0.         
024800     03  B1301-MOST-RECENT-CHANGE-IND  PIC 99    COMP VALUE ZERO.         
024900     03  B1301-SLAB-ORDER-TRACK-TBL.                                      
025000         05  B1301-SLAB-ORDER-TRACK-TABLE  OCCURS 10                      
025100                                           INDEXED BY B1301-INX1.         
025200             07  B1301-CHANGE-CODE         PIC X.                         
025300             07  B1301-SO-CHANGED-DATE-TIME                               
025400                                           PIC 9(13) COMP-3.              
025500             07  B1301-SO-CHANGED-FIELD    PIC X(4).                      
025600             07  B1301-SO-OLD-FIELD-VALUE  PIC X(9).                      
025700*                                                                         
025800     03  B1301-SPLIT-BIRTHDATE         PIC S9(13) COMP-3 VALUE +0.        
025900*                                                                         
026000     03  B1301-DBL-SLAB-WID-CAST-IND   PIC X          VALUE SPACE.        
026100         88  B1301-DOUBLE-WIDTH-CAST                VALUE 'X'.            
026200         88  B1301-DBL-WIDTH-CAST-CHG               VALUE '-'.            
026300     03  B1301-DBL-SLAB-WID-TEEM-IND   PIC X          VALUE SPACE.        
026400         88  B1301-DOUBLE-WIDTH-TEEM                VALUE 'X'.            
026500         88  B1301-DBL-WIDTH-TEEM-CHG               VALUE '-'.            
026600     03  B1301-UNIT-SCHD-COMMODITY-CODE PIC XX        VALUE SPACE.        
026700         88  B1301-TRADE                            VALUE '01'.           
026800         88  B1301-P-O-SHIP                         VALUE '02'.           
026900         88  B1301-HR-PROC-COILS                    VALUE '03'.           
027000         88  B1301-HRP-PROC-COILS                   VALUE '04'.           
027100         88  B1301-HR-CUT-LENGTHS                   VALUE '05'.           
027200         88  B1301-HRP-CUT-LENGTHS                  VALUE '06'.           
027300         88  B1301-HR-SLIT-COILS                    VALUE '07'.           
027400         88  B1301-HRP-SLIT-COILS                   VALUE '08'.           
027500         88  B1301-CR-EXPOSED                       VALUE '09'.           
027600         88  B1301-CR-UNEXPOSED                     VALUE '10'.           
027700         88  B1301-TIN-US                           VALUE '11'.           
027800         88  B1301-HR-FSS                           VALUE '12'.           
027900         88  B1301-HRP-FSS                          VALUE '13'.           
028000         88  B1301-CR-FSS                           VALUE '14'.           
028100         88  B1301-HR-OTHER-PROCESSING              VALUE '18'.           
028200         88  B1301-TP-OTHER-PROCESSING              VALUE '19'.           
028300         88  B1301-CR-OTHER-PROCESSING              VALUE '20'.           
028400         88  B1301-GA-EXP                           VALUE '21'.           
028500         88  B1301-GA-UNEXP                         VALUE '22'.           
028600         88  B1301-GZ-EXP                           VALUE '23'.           
028700         88  B1301-GZ-UNEXP                         VALUE '24'.           
028800     03  B1301-HM-BAND-WIDTH-PRISTL    PIC 99V99.                         
028900     03  B1301-HMRW-EDIT-IND           PIC X.                             
029000         88  B1301-HMRW-EDIT-OVERRIDE               VALUE 'X'.            
029100     03  B1301-SLAB-ORDER-WT-OVERRIDE  PIC X          VALUE SPACE.        
029200         88  B1301-ORDER-WGT-OVERRIDDEN             VALUE 'X'.            
029300     03  B1301-ORDERED-SLAB-WT-PROV    PIC S9(9) COMP-3 VALUE +0.         
029400     03  B1301-ORDERED-SLAB-WT-STOLEN  PIC S9(9) COMP-3 VALUE +0.         
029500     03  B1301-MET-PEND-SLAB-WT-PROV   PIC S9(9) COMP-3 VALUE +0.         
029600*********************************************************************     
029700***                                                               ***     
029800***   ORDER STATUS FIELD ADDITIONS                                ***     
029900***                                                               ***     
030000*********************************************************************     
030100     03  B1301-OUTSIDE-PROC-LOCATION       PIC XXX  VALUE SPACE.          
030200     03  B1301-PRIORITY-CODE               PIC XXX  VALUE SPACE.          
030300     03  B1301-HOLD-IND                    PIC X    VALUE SPACE.          
030400     03  B1301-HOLD-REASON                 PIC XX   VALUE SPACE.          
030500     03  B1301-SHIP-CANCEL-IND             PIC X    VALUE SPACE.          
030600         88  B1301-OS-SHIP-COMPLETE   VALUE 'S'.                          
030700         88  B1301-CANCELED           VALUE 'C'.                          
030800     03  B1301-COND-CODE                   PIC X    VALUE SPACE.          
030900         88  B1301-SUSPEND-PROD       VALUE 'P'.                          
031000         88  B1301-SUSPEND-SHIP       VALUE 'S'.                          
031100         88  B1301-SUSPEND-BOTH       VALUE 'B'.                          
031200     03  B1301-CONDITION-DATE.                                            
031300         05  B1301-CONDITION-YR            PIC 99   VALUE ZERO.           
031400         05  B1301-CONDITION-MTH           PIC 99   VALUE ZERO.           
031500         05  B1301-CONDITION-DAY           PIC 99   VALUE ZERO.           
031600     03  B1301-SPLIT-COMPLETE-IND          PIC X    VALUE SPACE.          
031700         88  B1301-ASK-CALL-COMPLETE  VALUE 'X'.                          
031800         88  B1301-CALL-COMPLETE      VALUE 'C'.                          
031900     03  B1301-REPROMISE-INSTR PIC X(4).                                  
032000*                                                                         
032100*                  ACTUAL WEIGHTS                              *          
032200*                                                                         
032300     03  B1301-SPLIT-LBS              PIC S9(9)  COMP-3 VALUE +0.         
032400     03  B1301-CM-RELEASE-BAND-WGT    PIC S9(7)  COMP-3 VALUE +0.         
032500     03  B1301-CM-RELEASE-CHARGE-WGT  PIC S9(7)  COMP-3 VALUE +0.         
032600     03  B1301-SLAB-APPLIED-WGT       PIC S9(7)  COMP-3 VALUE +0.         
032700     03  B1301-COIL-APPLIED-WGT       PIC S9(7)  COMP-3 VALUE +0.         
032800     03  B1301-LIFT-APPLIED-WGT       PIC S9(7)  COMP-3 VALUE +0.         
032900     03  B1301-STEEL-ORDER-WGT        PIC S9(7)  COMP-3 VALUE +0.         
033000     03  B1301-OS-SLAB-ORD-WGT        PIC S9(7)  COMP-3 VALUE +0.         
033100     03  B1301-SLAB-ORD-WGT-DELIVERED PIC S9(7)  COMP-3 VALUE +0.         
033200     03  B1301-SLAB-ORD-WGT-REJECTED  PIC S9(7)  COMP-3 VALUE +0.         
033300     03  B1301-READY-SHIP-WGT         PIC S9(7)  COMP-3 VALUE +0.         
033400     03  B1301-ON-MANIFEST-WGT        PIC S9(7)  COMP-3 VALUE +0.         
033500     03  B1301-SHIPPED-WGT            PIC S9(7)  COMP-3 VALUE +0.         
033600     03  B1301-REROLL-WGT             PIC S9(7)  COMP-3 VALUE +0.         
033700     03  B1301-EXCESS-OK-WGT          PIC S9(7)  COMP-3 VALUE +0.         
033800     03  B1301-SHORTAGE-OK-WGT        PIC S9(7)  COMP-3 VALUE +0.         
033900     03  B1301-GROUP-WGT              PIC S9(7)  COMP-3 VALUE +0.         
034000         88  B1301-NON-GROUPED      VALUE +0.                             
034100         88  B1301-GROUPED          VALUE +1 THRU +9999999.               
034200*                                                                         
034300*                        SHIP WEIGHT EQUIV WGTS ***  (SWE)                
034400*                                                                         
034500     03  B1301-PROVIDED-SLAB-WGT-SWE  PIC S9(7)  COMP-3 VALUE +0.         
034600     03  B1301-PROVIDED-COIL-WGT-SWE  PIC S9(7)  COMP-3 VALUE +0.         
034700     03  B1301-PROVIDED-LIFT-WGT-SWE  PIC S9(7)  COMP-3 VALUE +0.         
034800     03  B1301-CM-RELEASE-HM-SWE      PIC S9(7)  COMP-3 VALUE +0.         
034900     03  B1301-SLAB-ORDER-SWE         PIC S9(7)  COMP-3 VALUE +0.         
035000     03  B1301-STEEL-ORDER-SWE        PIC S9(7)  COMP-3 VALUE +0.         
035100     03  B1301-EXCESS-WGT-SWE         PIC S9(7)  COMP-3 VALUE +0.         
035200     03  B1301-REJECT-WGT-SWE         PIC S9(7)  COMP-3 VALUE +0.         
035300     03  B1301-REROLL-WGT-SWE         PIC S9(7)  COMP-3 VALUE +0.         
035400*                                                                         
035500     03  B1301-NBR-SLABS-APPLIED      PIC S999 VALUE ZERO.                
035600     03  B1301-NBR-COILS-APPLIED      PIC S999 VALUE ZERO.                
035700     03  B1301-NBR-LIFTS-APPLIED      PIC S999 VALUE ZERO.                
035800*                                                                         
035900     03  B1301-AVAILABLE-COMP-INDS.                                       
036000         05  B1301-AVAIL-COMP-PICK       PIC  X VALUE SPACE.              
036100         05  B1301-AVAIL-COMP-TAND       PIC  X VALUE SPACE.              
036200         05  B1301-AVAIL-COMP-ANNL       PIC  X VALUE SPACE.              
036300     03  B1301-ORD-MIN-SLB-WID           PIC  99V9  VALUE ZEROES.         
036301     03  B1301-ORD-MAX-SLB-WID           PIC  99V9  VALUE ZEROES.         
036310     03  FILLER                          PIC  X(04) VALUE SPACE.          
036400     03  B1301-TRANSACTION-TABLE.                                         
036500         05  B1301-TRANS-CODE1           PIC  X VALUE SPACE.              
036600         05  B1301-TRANS-CODE2           PIC  X VALUE SPACE.              
036700         05  B1301-TRANS-CODE3           PIC  X VALUE SPACE.              
036800         05  B1301-TRANS-CODE4           PIC  X VALUE SPACE.              
036900         05  B1301-TRANS-CODE5           PIC  X VALUE SPACE.              
037000*                                                                         
037100     03  B1301-SHIPPED-COMPLETE-DATE.                                     
037200         05  B1301-SHIPPED-YEAR            PIC 99 VALUE ZERO.             
037300         05  B1301-SHIPPED-MONTH           PIC 99 VALUE ZERO.             
037400         05  B1301-SHIPPED-DAY             PIC 99 VALUE ZERO.             
037500     03  B1301-LAST-UPDATE.                                               
037600         05  B1301-LAST-UPDATE-YR          PIC 99 VALUE ZERO.             
037700         05  B1301-LAST-UPDATE-MTH         PIC 99 VALUE ZERO.             
037800         05  B1301-LAST-UPDATE-DAY         PIC 99 VALUE ZERO.             
037900         05  B1301-LAST-UPDATE-HR          PIC 99 VALUE ZERO.             
038000         05  B1301-LAST-UPDATE-MIN         PIC 99 VALUE ZERO.             
038100*                                                                         
038200     03  B1301-SEGMENT-CREATED-BY          PIC X  VALUE SPACE.            
038300         88  B1301-INSERTED-BY-OS     VALUE '1'.                          
038400         88  B1301-UPDATED-BY-SPA     VALUE '2'.                          
038500         88  B1301-SPA2-GROUP         VALUE '3'.                          
038600*                                                                         
038700     03  B1301-SLAB-NBR-DELIVERED   PIC S999   COMP-3 VALUE ZERO.         
038800     03  B1301-SLAB-NBR-REJECTED    PIC S999   COMP-3 VALUE ZERO.         
038900     03  B1301-HM-DELIVERED-WGT     PIC S9(7)  COMP-3 VALUE ZERO.         
039000     03  B1301-HM-REJECTED-WGT      PIC S9(7)  COMP-3 VALUE ZERO.         
039100     03  B1301-HM-DELIVERED-NBR     PIC S999   COMP-3 VALUE ZERO.         
039200     03  B1301-HM-REJECTED-NBR      PIC S999   COMP-3 VALUE ZERO.         
039300     03  B1301-PICK-DELIVERED-WGT   PIC S9(7)  COMP-3 VALUE ZERO.         
039400     03  B1301-PICK-REJECTED-WGT    PIC S9(7)  COMP-3 VALUE ZERO.         
039500     03  B1301-PICK-DELIVERED-NBR   PIC S999   COMP-3 VALUE ZERO.         
039600     03  B1301-PICK-REJECTED-NBR    PIC S999   COMP-3 VALUE ZERO.         
039700     03  B1301-STOCK-DETERMINER     PIC X         VALUE SPACE.            
039800         88  B1301-STOCK-SPLIT        VALUE 'Y'.                          
039900*                                                                         
040000     03  B1301-HO-RESEND-IND        PIC X         VALUE SPACE.            
040100     03  B1301-OP-MATL-WGT-STOLEN   PIC S9(7)     VALUE ZERO.             
040200*                                                                         
040300     03  B1301-CHANGE-DATE.                                               
040400         05  B1301-CHANGE-YR           PIC 99     VALUE ZERO.             
040500         05  B1301-CHANGE-MO           PIC 99     VALUE ZERO.             
040600         05  B1301-CHANGE-DAY          PIC 99     VALUE ZERO.             
040700*                                                                         
040800     03  B1301-READY-DATE.                                                
040900         05  B1301-READY-MO            PIC XX     VALUE SPACE.            
041000         05  B1301-READY-DAY           PIC XX     VALUE SPACE.            
041100*                                                                         
041200     03  B1301-MINUS-1-WGT             PIC 9999   VALUE ZERO.             
041300*                                                                         
041400     03  B1301-CURRENT-WGT             PIC 9999   VALUE ZERO.             
041500*                                                                         
041600     03  B1301-PLUS-1-WGT              PIC 9999   VALUE ZERO.             
041700*                                                                         
041800     03  B1301-PLUS-2-WGT              PIC 9999   VALUE ZERO.             
041900*                                                                         
042000     03  B1301-PLUS-3-WGT              PIC 9999   VALUE ZERO.             
042100*                                                                         
042200     03  B1301-PROCESS-CODE-4-5        PIC XX     VALUE SPACE.            
042300     03  B1301-EXCESS-FOR-AUTOLOAD    PIC S9(7)  COMP-3 VALUE +0.         
042400     03  B1301-SLAB-WIDTH-TEEM-COMP    PIC 99     VALUE ZERO.             
042500     03  B1301-OPICS-IND               PIC X      VALUE SPACE.            
042600         88  B1301-OPICS-SUGGESTED-SHIP           VALUE 'Y'.              
042700     03  B1301-SPLIT-MANUAL-READY-IND  PIC X      VALUE SPACE.            
042800         88  B1301-SPLIT-MANUAL-READY             VALUE 'M'.              
042900     03  B1301-FIFO-NO-MOVE-MATL-IND   PIC X      VALUE SPACE.            
043000         88  B1301-FIFO-NO-MOVE-MATL              VALUE 'Y'.              
043100     03  B1301-MELT-WEEK.                                                 
043200         05  B1301-MELT-YY             PIC XX     VALUE SPACE.            
043300         05  B1301-MELT-WW             PIC XX     VALUE SPACE.            
043400     03  FILLER                        PIC XX     VALUE SPACE.            
043500******************************************************************        
043600*               END COPY CLAUSE HLF1301B                         *        
043700******************************************************************        
057600 COPY HLF13S1A.                                                   05810019
000010 01  H131ASSA.                                                            
000020     05  H131A-SEGMENT-NAME    PIC X(8)    VALUE 'HLF1301A'.              
000030     05  FILLER                PIC X       VALUE '*'.                     
000040     05  H131A-COMMAND-CODE    PIC X       VALUE '-'.                     
000050     05  H131A-QUALIFIER       PIC X       VALUE ' '.                     
000060     05  H131A-KEY-NAME        PIC X(8)    VALUE 'A1301KEY'.              
000070     05  H131A-OPERATOR        PIC XX      VALUE ' ='.                    
000080     05  H131A-KEY-VALUE       PIC X(9).                                  
000090     05  H131A-END-SSA         PIC X       VALUE ')'.                     
057700 COPY HLF13S1B.                                                   05820019
000010 01  H131BSSA.                                                            
000020     05  H131B-SEGMENT-NAME    PIC X(8)    VALUE 'HLF1301B'.              
000030     05  FILLER                PIC X       VALUE '*'.                     
000040     05  H131B-COMMAND-CODE    PIC X       VALUE '-'.                     
000050     05  H131B-QUALIFIER       PIC X       VALUE ' '.                     
000060     05  H131B-KEY-NAME        PIC X(8)    VALUE 'B1301KEY'.              
000070     05  H131B-OPERATOR        PIC XX      VALUE ' ='.                    
000080     05  H131B-KEY-VALUE       PIC X(6).                                  
000090     05  H131B-END-SSA         PIC X       VALUE ')'.                     
058100 01  OUT-PRE-ASN-REC.                                             05830019
058200     05  PRE-ASN-SLAB-ORD          PIC X(13) VALUE SPACE.         05840019
058300     05  PRE-ASN-HEAT              PIC X(9)  VALUE SPACE.         05850019
058400     05  PRE-ASN-INGOT             PIC XX    VALUE SPACE.         05860019
058500     05  PRE-ASN-CUT               PIC X     VALUE SPACE.         05870019
058600     05  FILLER                    PIC X(54) VALUE SPACE.         05880019
058700                                                                  05890019
058800 COPY HSLFS172.                                                   05900019
000100 01  HDPMS172.                                                            
000200     02  H172-SEQ                 PIC 999.                                
000300     02  H172-HEAT                PIC X(9).                               
000400     02  H172-ING                 PIC 99.                                 
000500     02  H172-CUT                 PIC X.                                  
000600     02  H172-UL-DATE.                                                    
000700         03  H172-UL-MONTH        PIC XX.                                 
000800         03  H172-UL-DAY          PIC XX.                                 
000900         03  H172-UL-YEAR         PIC XX.                                 
001000     02  H172-UL-WID              PIC 99V9.                               
001100     02  H172-UL-THK              PIC 99V9.                               
001200     02  H172-UL-LEN              PIC 999.                                
001300     02  H172-UL-WGT              PIC 9(5).                               
001400     02  H172-UL-LOC              PIC XXX.                                
001500     02  H172-FILLER              PIC X(62).                              
058900 COPY H31SSA.                                                     05910019
000010 01  H31SSA.                                                              
000020     05  H311-SSA.                                                        
000030         10 H311-SEG-NAME            PIC X(8)  VALUE 'HMTHA311'.          
000040         10 H311-STAR                PIC X     VALUE '*'.                 
000050         10 H311-COMMAND             PIC X     VALUE '-'.                 
000060         10 H311-LEFT-PAREN          PIC X     VALUE '('.                 
000070         10 H311-KEY-NAME            PIC X(8)  VALUE 'H311HEAT'.          
000080         10 H311-OPERATOR            PIC XX    VALUE ' ='.                
000090         10 H311-KEY-VALUE           PIC X(9).                            
000100         10 H311-RIGHT-PAREN         PIC X     VALUE ')'.                 
000110                                                                          
000120     05  H311-SEGNAME.                                                    
000130         10  FILLER                  PIC X(9)  VALUE 'HMTHA311'.          
000140         10  H311-CMND               PIC X(3)  VALUE SPACES.              
000150                                                                          
000160     05  H312-SSA.                                                        
000170         10 H312-SEG-NAME            PIC X(8)  VALUE 'HMTHA312'.          
000180         10 H312-STAR                PIC X     VALUE '*'.                 
000190         10 H312-COMMAND             PIC X     VALUE '-'.                 
000200         10 H312-LEFT-PAREN          PIC X     VALUE '('.                 
000210         10 H312-KEY-NAME            PIC X(8)  VALUE 'H312INGT'.          
000220         10 H312-OPERATOR            PIC XX    VALUE ' ='.                
000230         10 H312-KEY-VALUE           PIC XXX.                             
000240         10 H312-RIGHT-PAREN         PIC X     VALUE ')'.                 
000250                                                                          
000260     05  H312-SEGNAME.                                                    
000270         10  FILLER                  PIC X(9)  VALUE 'HMTHA312'.          
000280         10  H312-CMND               PIC X(3)  VALUE SPACES.              
000290                                                                          
000300     05  H315-SSA.                                                        
000310         10 H315-SEG-NAME            PIC X(8)  VALUE 'HMTHA315'.          
000320         10 H315-STAR                PIC X     VALUE '*'.                 
000330         10 H315-COMMAND             PIC X     VALUE '-'.                 
000340         10 H315-LEFT-PAREN          PIC X     VALUE '('.                 
000350         10 H315-KEY-NAME            PIC X(8)  VALUE 'H315TEST'.          
000360         10 H315-OPERATOR            PIC XX    VALUE ' ='.                
000370         10 H315-KEY-VALUE           PIC X(5).                            
000380         10 H315-RIGHT-PAREN         PIC X     VALUE ')'.                 
000390                                                                          
000400     05  H315-SEGNAME.                                                    
000410         10  FILLER                  PIC X(8)  VALUE 'HMTHA315'.          
000420         10  H315-CMND               PIC X(3)  VALUE SPACES.              
000430                                                                          
000440     05  H316-SEGNAME.                                                    
000450         10  FILLER                  PIC X(8)  VALUE 'HMTHA316'.          
000460         10  H316-CMND               PIC X(3)  VALUE SPACES.              
000470                                                                          
000480     05  H317-SEGNAME.                                                    
000490         10  FILLER                  PIC X(8)  VALUE 'HMTHA317'.          
000500         10  H317-CMND               PIC X(3)  VALUE SPACES.              
000510*                                                                         
000520*                                                                         
000530*       THIS IS THE SSA FOR DATA BASE H31.                                
000540*       NOTE:  THIS SSA IS TO BE USED ONLY WHEN YOU                       
000550*              ARE USING THE SECONDARY INDEX H31X1                        
000560*              TO ACCESS H31 BY PORTIONS OF THE HEAT NUMBER               
000570*              THIS INDEX WAS DEVELOP FOR ON REQUEST ANALYSIS             
000580*              H311 SEGMENTS WITH A 9 IN THE 1ST POSITION OF              
000590*              THE HEAT NUMBER.                                           
000600*                                                                         
000610 01  H31X1SS1.                                                            
000620     05  H31X1-SEGMENT-NAME     PIC X(8)   VALUE 'HMTHA311'.              
000630     05  H31X1-COMMAND-START    PIC X      VALUE '*'.                     
000640     05  H31X1-COMMAND-1        PIC X      VALUE '-'.                     
000650     05  H31X1-COMMAND-2        PIC X      VALUE '-'.                     
000660     05  H31X1-COMMAND-3        PIC X      VALUE '-'.                     
000670     05  H31X1-QUALIFIER        PIC X      VALUE ' '.                     
000680     05  H31X1-KEY-NAME         PIC X(8)   VALUE 'H31X1   '.              
000690     05  H31X1-OPERATOR         PIC XX     VALUE '=>'.                    
000700     05  H31X1-KEY-VALUE.                                                 
000710         10  H31X1-HEAT-NUMBER-LOW.                                       
000720            15  H31X1-HEAT-NUMBER-POS-1                                   
000730                                PIC X  VALUE LOW-VALUE.                   
000740            15  H31X1-HEAT-NUMBER-REST                                    
000750                                PIC X(8) VALUE LOW-VALUE.                 
000760     05  H31X1-END-AND          PIC X      VALUE '&'.                     
000770     05  H31X1-FIELD-NAME-HIGH                                            
000780                                PIC X(8)   VALUE 'H31X1   '.              
000790     05  H31X1-MAX-OPER         PIC XX     VALUE '<='.                    
000800     05  H31X1-KEY-VALU.                                                  
000810         10  H31X1-HEAT-NUMBER-HIGH.                                      
000820            15  H31X1-HEAT-NUMBER-POS-1-HIGH                              
000830                                PIC X  VALUE HIGH-VALUE.                  
000840            15  H31X1-HEAT-NUMBER-REST-HIGH                               
000850                                PIC X(8) VALUE HIGH-VALUE.                
000860     05  H31X1-END-SSA          PIC X      VALUE ')'.                     
059000     EXEC SQL INCLUDE TGSI101 END-EXEC.                           05920019
      ******************************************************************        
      * DCLGEN TABLE(DHGSI1.TGSI101)                                   *        
      *        LIBRARY(HLDB2.COBLIB(TGSI101))                          *        
      *        ACTION(REPLACE)                                         *        
      *        LANGUAGE(COBOL)                                         *        
      *        NAMES(GSI101-)                                          *        
      *        APOST                                                   *        
      *        COLSUFFIX(YES)                                          *        
      * ... IS THE DCLGEN COMMAND THAT MADE THE FOLLOWING STATEMENTS   *        
      ******************************************************************        
           EXEC SQL DECLARE DHGSI1.TGSI101 TABLE                                
           ( ID_BH_SLAB_NUM                 CHAR(12) NOT NULL,                  
             ID_FGN_SLAB_NUM                CHAR(20) NOT NULL,                  
             ID_MANIFEST                    CHAR(10) NOT NULL,                  
             DTE_FRT_PAYMENT                DATE NOT NULL,                      
             DTE_SHIP                       DATE NOT NULL,                      
             NAM_PLANT_FROM                 CHAR(10) NOT NULL,                  
             CDE_PRODUCT                    CHAR(1) NOT NULL,                   
             CDE_STATUS                     CHAR(2) NOT NULL,                   
             DIM_WID_SLAB                   DECIMAL(4, 2) NOT NULL,             
             DIM_THK_SLAB                   DECIMAL(4, 2) NOT NULL,             
             DIM_LEN_SLAB                   DECIMAL(3, 0) NOT NULL,             
             WGT_SLAB_LBS                   DECIMAL(6, 0) NOT NULL,             
             CDE_BH_GRADE                   CHAR(7)  NOT NULL,                  
             CDE_FGN_GRADE                  CHAR(7)  NOT NULL,                  
             CDE_FGN_HEAT                   CHAR(20) NOT NULL,                  
             CDE_PO_NUM                     CHAR(30) NOT NULL,                  
             CDE_SHIP_MODE                  CHAR(1)  NOT NULL,                  
             ID_CARRIER                     CHAR(4)  NOT NULL,                  
             ID_VEHICLE                     CHAR(4)  NOT NULL,                  
             ID_VEHICLE_NUM                 CHAR(6)     NOT NULL,               
             TSP_CRTE                       TIMESTAMP   NOT NULL,               
             DTE_UNLOAD                     DATE NOT NULL,                      
             CDE_BAY_LOC                    CHAR(3)  NOT NULL,                  
             CDE_SLAB_COND                  CHAR(4)  NOT NULL,                  
             ID_SLAB_SEQ_NUM                CHAR(6)  NOT NULL,                  
             ID_SRC_PLANT_ORD_1             CHAR(20) NOT NULL,                  
             ID_SRC_PLANT_ORD_2             CHAR(20) NOT NULL,                  
             ID_SLAB_ORD                    CHAR(13) NOT NULL,                  
             ID_MOTHER_SLAB_ORD             SMALLINT NOT NULL,                  
             IND_SLAB_TUNDISH               CHAR(1)  NOT NULL,                  
             SW_APP_TO_BH_ORD               CHAR(1)  NOT NULL,                  
             CDE_FGN_SLAB_COND              CHAR(4)  NOT NULL,                  
             CDE_QUALITY_LEVEL              DECIMAL(2, 0) NOT NULL              
           ) END-EXEC.                                                          
      ******************************************************************        
      * COBOL DECLARATION FOR TABLE DHGSI1.TGSI101                     *        
      ******************************************************************        
       01  DCLTGSI101.                                                          
           10 GSI101-ID-BH-SLAB-NUM       PIC X(12).                            
           10 GSI101-ID-FGN-SLAB-NUM      PIC X(20).                            
           10 GSI101-ID-MANIFEST          PIC X(10).                            
           10 GSI101-DTE-FRT-PAYMENT      PIC X(10).                            
           10 GSI101-DTE-SHIP             PIC X(10).                            
           10 GSI101-NAM-PLANT-FROM       PIC X(10).                            
           10 GSI101-CDE-PRODUCT          PIC X(1).                             
           10 GSI101-CDE-STATUS           PIC X(2).                             
           10 GSI101-DIM-WID-SLAB         PIC S9(2)V9(2) USAGE COMP-3.          
           10 GSI101-DIM-THK-SLAB         PIC S9(2)V9(2) USAGE COMP-3.          
           10 GSI101-DIM-LEN-SLAB         PIC S9(3)V USAGE COMP-3.              
           10 GSI101-WGT-SLAB-LBS         PIC S9(6)V USAGE COMP-3.              
           10 GSI101-CDE-BH-GRADE         PIC X(7).                             
           10 GSI101-CDE-FGN-GRADE        PIC X(7).                             
           10 GSI101-CDE-FGN-HEAT         PIC X(20).                            
           10 GSI101-CDE-PO-NUM           PIC X(30).                            
           10 GSI101-CDE-SHIP-MODE        PIC X(1).                             
           10 GSI101-ID-CARRIER           PIC X(4).                             
           10 GSI101-ID-VEHICLE           PIC X(4).                             
           10 GSI101-ID-VEHICLE-NUM       PIC X(6).                             
           10 GSI101-TSP-CRTE             PIC X(26).                            
           10 GSI101-DTE-UNLOAD           PIC X(10).                            
           10 GSI101-CDE-BAY-LOC          PIC X(3).                             
           10 GSI101-CDE-SLAB-COND        PIC X(4).                             
           10 GSI101-ID-SLAB-SEQ-NUM      PIC X(6).                             
           10 GSI101-ID-SRC-PLANT-ORD-1   PIC X(20).                            
           10 GSI101-ID-SRC-PLANT-ORD-2   PIC X(20).                            
           10 GSI101-ID-SLAB-ORD          PIC X(13).                            
           10 GSI101-ID-MOTHER-SLAB-ORD   PIC S9(4) USAGE COMP.                 
           10 GSI101-IND-SLAB-TUNDISH     PIC X(1).                             
           10 GSI101-SW-APP-TO-BH-ORD     PIC X(1).                             
           10 GSI101-CDE-FGN-SLAB-COND    PIC X(4).                             
           10 GSI101-CDE-QUALITY-LEVEL    PIC S9(2)V USAGE COMP-3.              
      ******************************************************************        
      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 33      *        
      ******************************************************************        
059100     EXEC SQL INCLUDE TGSI103 END-EXEC.                           05930019
      ******************************************************************        
      * DCLGEN TABLE(DHGSI1.TGSI103)                                   *        
      *        LIBRARY(HLDB2.COBLIB(TGSI103))                          *        
      *        ACTION(REPLACE)                                         *        
      *        LANGUAGE(COBOL)                                         *        
      *        APOST                                                   *        
      * ... IS THE DCLGEN COMMAND THAT MADE THE FOLLOWING STATEMENTS   *        
      ******************************************************************        
           EXEC SQL DECLARE DHGSI1.TGSI103 TABLE                                
           ( CDE_BH_GRADE_4                 CHAR(4) NOT NULL,                   
             CDE_USER_UPDT                  CHAR(5) NOT NULL,                   
             TSP_LAST_UPDT                  TIMESTAMP NOT NULL                  
           ) END-EXEC.                                                          
      ******************************************************************        
      * COBOL DECLARATION FOR TABLE DHGSI1.TGSI103                     *        
      ******************************************************************        
       01  DCLTGSI103.                                                          
           10 GSI103-CDE-BH-GRADE-4       PIC X(4).                             
           10 GSI103-CDE-USER-UPDT        PIC X(5).                             
           10 GSI103-TSP-LAST-UPDT        PIC X(26).                            
      ******************************************************************        
      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 3       *        
      ******************************************************************        
059200     EXEC SQL INCLUDE TSQA101 END-EXEC.                           05940019
      ******************************************************************        
      * DCLGEN TABLE(DHSQA1.TSQA101)                                   *        
      *        LIBRARY(HTDB2.COBLIB(TSQA101))                          *        
      *        LANGUAGE(COBOL)                                         *        
      *        APOST                                                   *        
      * ... IS THE DCLGEN COMMAND THAT MADE THE FOLLOWING STATEMENTS   *        
      ******************************************************************        
           EXEC SQL DECLARE DHSQA1.TSQA101 TABLE                                
           ( CDE_GRADE                      CHAR(4) NOT NULL,                   
             IND_LEVEL                      CHAR(1) NOT NULL,                   
             CDE_DG_GRADE                   CHAR(4) NOT NULL,                   
             ID_USER_UPDT                   CHAR(8) NOT NULL,                   
             TSP_REC_UPDT                   TIMESTAMP NOT NULL                  
           ) END-EXEC.                                                          
      ******************************************************************        
      * COBOL DECLARATION FOR TABLE DHSQA1.TSQA101                     *        
      ******************************************************************        
       01  DCLTSQA101.                                                          
           10 SQA101-CDE-GRADE              PIC X(4).                           
           10 SQA101-IND-LEVEL              PIC X(1).                           
           10 SQA101-CDE-DG-GRADE           PIC X(4).                           
           10 SQA101-ID-USER-UPDT           PIC X(8).                           
           10 SQA101-TSP-REC-UPDT           PIC X(26).                          
      ******************************************************************        
      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 5       *        
      ******************************************************************        
059300     EXEC SQL INCLUDE SQLCA   END-EXEC.                           05950019
************************************************                        *****   
* SQL INCLUDE FOR SQLCA                                                     *   
************************************************                        *****   
*****     EXEC SQL INCLUDE SQLCA  END-EXEC.                                     
        01 SQLCA.                                                               
        05 SQLCAID     PIC X(8).                                                
        05 SQLCABC     PIC S9(9) COMP-4.                                       B
        05 SQLCODE     PIC S9(9) COMP-4.                                       B
        05 SQLERRM.                                                            B
           49 SQLERRML PIC S9(4) COMP-4.                                       B
           49 SQLERRMC PIC X(70).                                              B
        05 SQLERRP     PIC X(8).                                               B
        05 SQLERRD     OCCURS 6 TIMES                                          B
                       PIC S9(9) COMP-4.                                        
        05 SQLWARN.                                                            B
           10 SQLWARN0 PIC X.                                                  B
           10 SQLWARN1 PIC X.                                                  B
           10 SQLWARN2 PIC X.                                                  B
           10 SQLWARN3 PIC X.                                                  B
           10 SQLWARN4 PIC X.                                                  B
           10 SQLWARN5 PIC X.                                                  B
           10 SQLWARN6 PIC X.                                                  B
           10 SQLWARN7 PIC X.                                                  B
        05 SQLEXT      PIC X(8).                                               B
            77 SQL-NULL      PIC S9(9) COMP-4 VALUE +0.                         
            77 SQL-INIT-FLAG PIC S9(4) COMP-4 VALUE +0.                         
             88 SQL-INIT-DONE VALUE +1.                                         
     01 SQL-PLIST6.                                                             
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2621440.                     
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +50.                          
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                   
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                   
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                   
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                           
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                    
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                           
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                           
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +223.                         
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +3.                          B
     01 SQL-PLIST7.                                                            B
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2621440.                    B
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +45.                         B
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                  B
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                  B
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                  B
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                          B
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                   B
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +235.                        B
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +5.                          B
     01 SQL-PLIST8.                                                            B
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2623488.                    B
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +30.                         B
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                  B
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                  B
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                  B
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                          B
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                   B
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +248.                        B
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +4.                          B
        05 SQL-AVAR-LIST8.                                                     B
           10 SQL-AVAR-SIZE  PIC S9(9) COMP-4 VALUE +292.                      B
           10 SQL-AVAR-DESCS.                                                  B
              15 SQL-AVAR-TYPE1 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN1  PIC S9(4) COMP-4 VALUE +8.                     B
           10 SQL-AVAR-ADDRS.                                                  B
              15 SQL-AVAR-ADDR1 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND1  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE2 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN2  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR2 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND2  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE3 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN3  PIC S9(4) COMP-4 VALUE +12.                    B
              15 SQL-AVAR-ADDR3 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND3  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE4 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN4  PIC S9(4) COMP-4 VALUE +6.                     B
              15 SQL-AVAR-ADDR4 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND4  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE5 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN5  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR5 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND5  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE6 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN6  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR6 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND6  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE7 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN7  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR7 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND7  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE8 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN8  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR8 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND8  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE9 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN9  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR9 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND9  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE10 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN10  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR10 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND10  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE11 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN11  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR11 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND11  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE12 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN12  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR12 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND12  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE13 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN13  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR13 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND13  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE14 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN14  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR14 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND14  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE15 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN15  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR15 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND15  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE16 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN16  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR16 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND16  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE17 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN17  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR17 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND17  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE18 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN18  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR18 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND18  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE19 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN19  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR19 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND19  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE20 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN20  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR20 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND20  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE21 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN21  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR21 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND21  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE22 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN22  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR22 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND22  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE23 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN23  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR23 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND23  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE24 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN24  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR24 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND24  PIC S9(9) COMP-4.                             B
     01 SQL-PLIST9.                                                            B
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2623488.                    B
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +30.                         B
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                  B
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                  B
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                  B
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                          B
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                   B
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +292.                        B
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +4.                          B
        05 SQL-AVAR-LIST9.                                                     B
           10 SQL-AVAR-SIZE  PIC S9(9) COMP-4 VALUE +292.                      B
           10 SQL-AVAR-DESCS.                                                  B
              15 SQL-AVAR-TYPE1 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN1  PIC S9(4) COMP-4 VALUE +8.                     B
           10 SQL-AVAR-ADDRS.                                                  B
              15 SQL-AVAR-ADDR1 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND1  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE2 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN2  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR2 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND2  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE3 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN3  PIC S9(4) COMP-4 VALUE +12.                    B
              15 SQL-AVAR-ADDR3 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND3  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE4 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN4  PIC S9(4) COMP-4 VALUE +6.                     B
              15 SQL-AVAR-ADDR4 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND4  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE5 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN5  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR5 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND5  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE6 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN6  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR6 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND6  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE7 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN7  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR7 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND7  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE8 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN8  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR8 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND8  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE9 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN9  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR9 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND9  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE10 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN10  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR10 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND10  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE11 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN11  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR11 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND11  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE12 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN12  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR12 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND12  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE13 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN13  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR13 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND13  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE14 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN14  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR14 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND14  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE15 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN15  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR15 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND15  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE16 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN16  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR16 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND16  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE17 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN17  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR17 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND17  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE18 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN18  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR18 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND18  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE19 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN19  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR19 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND19  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE20 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN20  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR20 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND20  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE21 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN21  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR21 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND21  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE22 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN22  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR22 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND22  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE23 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN23  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR23 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND23  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE24 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN24  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR24 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND24  PIC S9(9) COMP-4.                             B
059400     EXEC SQL INCLUDE DB2AREA END-EXEC.                           05960019
000010************************************************************************  
000020**                                                                     *  
000030**      ****    ****     ***         ***    ****    *****    ***       *  
000040**      *   *   *   *   *   *       *   *   *   *   *       *   *      *  
000050**      *   *   ****       *  ****  *****   * *     ***     *****      *  
000060**      *   *   *   *    *          *   *   *  *    *       *   *      *  
000070**      ****    ****    *****       *   *   *   *   *****   *   *      *  
000080**                                                                     *  
000090***************************** DB2-AREA *********************************  
000100************************************************************************  
000110 01  DB2-AREA.                                                            
000120************************************************************************  
000130************************* SQL ERROR AREA *******************************  
000140************************************************************************  
000150     05  FILLER                PIC X(25) VALUE                            
000160                               '*************************'.               
000170     05  FILLER                PIC X(25) VALUE                            
000180                               '      SQL ERROR AREA     '.               
000190     05  FILLER                PIC X(25) VALUE                            
000200                               '*************************'.               
000210     05  SQLCA-ERROR-AREA      PIC X(100)  VALUE SPACES.                  
000220************************************************************************  
000230********************** DB2 ERROR MESSAGES ******************************  
000240************************************************************************  
000250     05  FILLER                PIC X(25) VALUE                            
000260                               '*************************'.               
000270     05  FILLER                PIC X(25) VALUE                            
000280                               '    DB2 ERROR MESSAGES   '.               
000290     05  FILLER                PIC X(25) VALUE                            
000300                               '*************************'.               
000310     05  DB2-ERROR-MESSAGES.                                              
000320         10  DSN8504.                                                     
000330             15  FILLER        PIC X(37)   VALUE                          
000340                 'DSN8504E - SQL ERROR, RETURN CODE IS:'.                 
000350             15  SQLCODE4      PIC -(10).                                 
000360         10  DSN8507.                                                     
000370             15  FILLER        PIC X(27)   VALUE                          
000380                 'DSN8507I - RECORD NOT FOUND'.                           
000390         10  DSN8508.                                                     
000400             15  FILLER        PIC X(25)   VALUE                          
000410                 'DSN8508E - ERROR DETECTED'.                             
000420             15  FILLER        PIC X(28)   VALUE                          
000430                 ' BY MESSAGE FORMAT ROUTINE, '.                          
000440             15  FILLER        PIC X(16)   VALUE                          
000450                 'RETURN CODE IS: '.                                      
000460             15  TIARCODE8     PIC -(10).                                 
000470************************************************************************  
000480************************DB2 CHECK RETURN CODE***************************  
000490************************************************************************  
000500     05  FILLER                PIC X(25) VALUE                            
000510                               '*************************'.               
000520     05  FILLER                PIC X(25) VALUE                            
000530                               '  DB2 CHECK RETURN CODE  '.               
000540     05  FILLER                PIC X(25) VALUE                            
000550                               '*************************'.               
000560     05  DB2-SQLCODE           PIC S9(9) COMP-4.                          
000570         88  DB2-SUCCESSFUL    VALUE +000.                                
000580         88  DB2-NOT-FOUND     VALUE +100.                                
000590         88  MEMBER-EXISTS     VALUE -803.                                
000591         88  DB2-MULTIPLE-ROWS-FOUND                                      
000592                               VALUE -811.                                
000600************************************************************************  
000610************************DB2 ERROR TABLE ********************************  
000620************************************************************************  
000630     05  FILLER                PIC X(25) VALUE                            
000640                               '*************************'.               
000650     05  FILLER                PIC X(25) VALUE                            
000660                               '     DB2 ERROR TABLE     '.               
000670     05  FILLER                PIC X(25) VALUE                            
000680                               '*************************'.               
000690     05  DB2-ERROR-MESSAGE.                                               
000700         10  DB2-ERROR-LEN     PIC S9(4)   COMP VALUE +960.               
000710         10  DB2-ERROR-TEXT    PIC X(960).                                
000720         10  DB2-ERROR-TEXT-REDF REDEFINES DB2-ERROR-TEXT                 
000730                               PIC X(120)  OCCURS 8 TIMES INDEXED         
000740                                           BY DB2-INDEX.                  
000750         10  DB2-ERROR-TEXT-LEN                                           
000760                               PIC S9(9) COMP VALUE +120.                 
000770************************************************************************  
000771*********************** DB2 DATE AND TIME DEFAULTS *********************  
000772************************************************************************  
000773     05  DB2-TIMESTAMP-DEFAULT PIC X(26) VALUE                            
000774                              '0001-01-01-01.01.01.111111'.               
000775     05  DB2-DATE-DEFAULT      PIC X(10) VALUE                            
000776                                              '0001-01-01'.               
000777     05  DB2-TIME-DEFAULT      PIC X(08) VALUE                            
000778                                                '01.01.01'.               
000779************************************************************************  
000780************************************************************************  
059500*                                                                 05970019
059600*-----------------------------------------------------------------05980019
059700*        L I N K A G E   S E C T I O N                           *05990019
059800*-----------------------------------------------------------------06000019
059900 LINKAGE SECTION.                                                 06010019
060000 COPY IOPCB.                                                      06020019
000010*                                                                         
000020*    IO PCB                                                               
000030*                                                                         
000040 01  IO-PCB.                                                              
000050     05  IO-LTERM-NAME           PIC X(8).                                
000060     05  IO-RESERVED-FOR-DLI     PIC XX.                                  
000070     05  IO-RET-STATUS           PIC XX.                                  
000080         88  IO-CALL-SUCCESSFUL       VALUE '  '.                         
000090         88  IO-NO-MORE-MESSAGES      VALUE 'QC'.                         
000100         88  IO-END-OF-MESSAGE        VALUE 'QD'.                         
000110         88  IO-ISRT-DEST-UNKNOWN     VALUE 'QH'.                         
000120     05  IO-CURRENT-DATE         PIC S9(7)   COMP-3.                      
000130     05  IO-CURRENT-TIME         PIC S9(7)   COMP-3.                      
000140     05  IO-INPUT-SEQUENCE       PIC S9(7)   COMP.                        
000150     05  IO-MOD-NAME             PIC X(8).                                
000160     05  IO-USERID.                                                       
000170         10  IO-LOGONID.                                                  
000180             15  IO-LOCATION     PIC X.                                   
000190             15  IO-MAJOR-AREA   PIC XX.                                  
000200             15  IO-NUMBER       PIC XX.                                  
000210         10  IO-NOT-USED         PIC XXX.                                 
060100 COPY ALT01PCB.                                                   06030019
000010*                                                                         
000020*    ALTERNATE IO PCB NUMBER 1                                            
000030*                                                                         
000040 01  ALT01-IO-PCB.                                                        
000050     05  ALT01-LTERM-NAME           PIC X(8).                             
000060     05  ALT01-RESERVED-FOR-DLI     PIC XX.                               
000070     05  ALT01-RET-STATUS           PIC XX.                               
000080         88  ALT01-CALL-SUCCESSFUL       VALUE '  '.                      
000090     05  ALT01-CURRENT-DATE         PIC S9(7)   COMP-3.                   
000100     05  ALT01-CURRENT-TIME         PIC S9(7)   COMP-3.                   
000110     05  ALT01-INPUT-SEQUENCE       PIC S9(7)   COMP.                     
000120     05  ALT01-MOD-NAME             PIC X(8).                             
000130     05  ALT01-USERID.                                                    
000140         10  ALT01-LOGONID.                                               
000150             15  ALT01-LOCATION     PIC X.                                
000160             15  ALT01-MAJOR-AREA   PIC XX.                               
000170             15  ALT01-NUMBER       PIC XX.                               
000180         10  ALT01-NOT-USED         PIC XXX.                              
060200 COPY H31PCB.                                                     06040019
000010 01  H31-PCB.                                                             
000020     05  H31-DBD-NAME                    PIC X(8).                        
000030     05  H31-SEG-LEVEL                   PIC XX.                          
000040     05  H31-RET-STATUS                  PIC XX.                          
000050         88  H31-CALL-SUCCESSFUL             VALUE '  '.                  
000060         88  H31-SEG-NOT-FOUND               VALUE 'GE'.                  
000070         88  H31-SEG-ALREADY-INSERTED        VALUE 'II'.                  
000080         88  H31-END-OF-DATABASE             VALUE 'GB'.                  
000090         88  H31-CROSSED-HIER-BOUNDRY        VALUE 'GA'.                  
000100         88  H31-DIFF-SEG-TYPE-RETURNED      VALUE 'GK'.                  
000110     05  H31-PROC-OPTIONS                PIC X(4).                        
000120     05  H31-RESERVED-FOR-DLI            PIC S9(5)   COMP.                
000130     05  H31-SEG-NAME                    PIC X(8).                        
000140     05  H31-LENGTH-KFBA                 PIC S9(5)   COMP.                
000150     05  H31-NO-OF-SEN-SEGS              PIC S9(5)   COMP.                
000160     05  H31-KFBA.                                                        
000170         10  H311HEAT                    PIC X(9).                        
000180         10  H312INGT                    PIC XXX.                         
060300 COPY LFDPP031.                                                   06050019
000010*                                                                         
000020*    THIS PCB IS USED TO PROCESS THE WORK D/B HLFDPP03                    
000030*                                                                         
000040*                                                                         
000050 01  LFDPP031.                                                            
000060     05  LFDPP031-NAME               PIC X(8).                            
000070     05  LFDPP031-SEGMENT-LEVEL      PIC XX.                              
000080     05  LFDPP031-STATUS-CODE        PIC XX.                              
000090     05  LFDPP031-PROC-OPTION        PIC X(4).                            
000100     05  LFDPP031-RESERVED-DLI       PIC S9(5)   COMP.                    
000110     05  LFDPP031-SEG-NAME-FB        PIC X(8).                            
000120     05  LFDPP031-LEN-FB-KEY         PIC S9(5)   COMP.                    
000130     05  LFDPP031-NO-SEN-SEGS        PIC S9(5)   COMP.                    
000140     05  LFDPP031-KEY-FB-AREA.                                            
000150         10  LFDPP031-KEY-FB-ASEG    PIC X(16).                           
000160         10  LFDPP031-KEY-FB-OTHER   PIC X(16).                           
060500 COPY HLF13E01.                                                   06060019
000100 01  H13P1PCB.                                                            
000200     03  H13P1PCB-NAME                 PIC X(8).                          
000300     03  H13P1PCB-SEGMENT-LEVEL        PIC XX.                            
000400     03  H13P1PCB-STATUS-CODE          PIC XX.                            
000500         88  H13P1PCB-CALL-SUCCESSFUL      VALUE '  '.                    
000600         88  H13P1PCB-SEG-NOT-FOUND        VALUE 'GE'.                    
000700         88  H13P1PCB-SEG-ALREADY-INSERTED VALUE 'II'.                    
000800         88  H13P1PCB-END-OF-DATABASE      VALUE 'GB'.                    
000900         88  H13P1PCB-CROSSED-HIER-BOUNDRY VALUE 'GA'.                    
001000         88  H13P1PCB-DIFF-SEG-SAME-LEVEL  VALUE 'GK'.                    
001100     03  H13P1PCB-PROC-OPTION          PIC X(4).                          
001200     03  H13P1PCB-RESERVED-DLI         PIC S9(5)   COMP.                  
001300     03  H13P1PCB-SEGMENT-NAME-FB      PIC X(8).                          
001400     03  H13P1PCB-LENGTH-FB-KEY        PIC S9(5)   COMP.                  
001500     03  H13P1PCB-NO-SEN-SEGS          PIC S9(5)   COMP.                  
001600     03  H13P1PCB-KEY-FB-AREA.                                            
001700         05  H13P1PCB-ORDER-KFB        PIC X(9).                          
001800         05  H13P1PCB-SPLIT-APPL-KFB.                                     
001900             07  H13P1PCB-SPLIT-KFB    PIC X(6).                          
002000             07  H13P1PCB-APPL-KFB     PIC X(14).                         
002100             07  FILLER                PIC X.                             
002200         05  H13P1PCB-SPLIT-SO-KFB                 REDEFINES              
002300             H13P1PCB-SPLIT-APPL-KFB.                                     
002400             07  H13P1PCB-SPLIT-KFB    PIC X(6).                          
002500             07  H13P1PCB-SO-KFB       PIC X(5).                          
002600             07  H13P1PCB-HEAT-KFB     PIC X(9).                          
002700             07  FILLER                PIC X.                             
002800         05  H13P1PCB-MEMBER-KFB                   REDEFINES              
002900             H13P1PCB-SPLIT-SO-KFB.                                       
003000             07  H13P1PCB-SPLIT-KFB    PIC X(6).                          
003100             07  H13P1PCB-MEMBER-KFB   PIC X(15).                         
060700                                                                  06070019
060800 PROCEDURE DIVISION.                                              06080019
060900     ENTRY 'DLITCBL' USING IO-PCB                                 06090019
061000                           ALT01-IO-PCB                           06100019
061100                           H31-PCB                                06110019
061200                           LFDPP031                               06120019
061400                           H13P1PCB.                              06130019
061500                                                                  06140019
061600     PERFORM INITIALIZE-PARAGRAPH.                                06150019
061700                                                                  06160019
061800     SET FILE-NOT-DONE TO TRUE                                    06170019
061900     PERFORM READ-SLAB-FILE                                       06180019
062000                                                                  06190019
062100     PERFORM PROCESS-RECORDS                                      06200019
062200             UNTIL FILE-DONE.                                     06210019
062300                                                                  06220019
062400*    PROCESS SLABS FROM PREVIOUS RUN THAT DID NOT HAVE GRADE      06230019
062500     PERFORM PROCESS-NO-GRADE-RECORDS.                            06240019
062600                                                                  06250019
062700     IF DUP-SLAB-EMAIL                                            06260019
062800        PERFORM C100-SEND-HLFGNFAX                                06270019
062900     END-IF.                                                      06280019
063000                                                                  06290019
063100     PERFORM PROCESS-ADD-MISC-TGSI101.                            06300019
063200                                                                  06310019
063300     CLOSE SLAB-OUTFILE                                           06320019
063400           SLAB-FILE                                              06330019
063500           HIST-FILE                                              06340019
063600           PMHIST-FILE                                            06350019
063700           SLABOW-FILE                                            06360019
063800           NOGRD-FILE                                             06370019
063900           FGNOW-FILE                                             06380019
064000           OUT-PRE-ASN-FILE                                       06390019
064100                                                                  06400019
064200     IF TEST-SYSTEM                                               06410019
064300        CONTINUE                                                  06420019
064400     ELSE                                                         06430019
064500        OPEN  OUTPUT SLAB-FILE                                    06440019
064600        CLOSE SLAB-FILE                                           06450019
064700     END-IF.                                                      06460019
064800     GOBACK.                                                      06470019
064900                                                                  06480019
065000*---------------------------------------------------------------  06490019
065100*---------------------------------------------------------------  06500019
<USER> <GROUP>.                                            06510019
065300     CALL 'IMSID' USING WORK-SYSTEM-ID.                           06520019
065400                                                                  06530019
065500     ACCEPT SAVE-DATE FROM DATE                                   06540019
065600     ACCEPT SAVE-TIME FROM TIME                                   06550019
065700                                                                  06560019
065800     MOVE SPACES TO HISTORY-FORMAT                                06570019
065900                    WS-SCARF-IND.                                 06580019
066000                                                                  06590019
066100     MOVE SAVE-DATE-YY-MM        TO WS-DATE-1-YYMM                06600019
066200     MOVE WS-DATE-1-YY           TO WS-GET-YEAR                   06610019
066300     CALL 'GETCENT' USING WS-GET-DATE                             06620019
066400     MOVE WS-GET-CENT            TO WS-DATE-1-CC                  06630019
066500     MOVE SPACES                 TO EMAIL-DETAIL-INFO             06640019
066600     MOVE ZEROS                  TO EMAIL-TEXT-SUB                06650019
066700                                                                  06660019
066800     OPEN INPUT SLAB-FILE                                         06670019
066900                NOGRD-FILE                                        06680019
067000                                                                  06690019
067100     OPEN OUTPUT HIST-FILE                                        06700019
067200                 PMHIST-FILE                                      06710019
067300                 SLABOW-FILE                                      06720019
067400                 SLAB-OUTFILE                                     06730019
067500                 FGNOW-FILE                                       06740019
067600                 OUT-PRE-ASN-FILE.                                06750019
067700                                                                  06760019
067800*---------------------------------------------------------------  06770019
067900*---------------------------------------------------------------  06780019
<USER> <GROUP>.                                                 06790019
068100     PERFORM DETERMINE-PLANT                                      06800019
068210                                                                  06810019
069110*    REMOVE RECORDS NOT FORMATTED CORRECTLY                       06820019
068300     MOVE ZEROS TO BAD-REC                                        06830019
068310     INSPECT K0D-HEAT      TALLYING BAD-REC FOR ALL '-'           06840019
069620     INSPECT K0D-PO-NUMBER TALLYING BAD-REC FOR ALL 'SCRAP'       06850019
068313                                                                  06860019
068320     IF BAD-REC > 0                                               06870019
068321        PERFORM READ-SLAB-FILE                                    06880019
068330     ELSE                                                         06890019
068331        PERFORM CHECK-THICKNESS                                   06900019
068340        PERFORM PROCESS-SLAB-RECORDS                              06910019
068350     END-IF.                                                      06920019
069100                                                                  06930019
069110*---------------------------------------------------------------  06940019
069120*---------------------------------------------------------------  06950019
<USER> <GROUP>.                                            06960019
069140     EVALUATE TRUE                                                06970019
069161     WHEN K0D-YRCD IS ALPHABETIC                                  06980019
069162*      YR CODE IS ALPHA THEN SLAB ALREADY BH FORMAT               06990019
069163*      IE SPARROW PT, MANUALS, LEX, COATES, MEXICO                07000019
069170        PERFORM FIND-HEAT-PREVIOUS-H311                           07010019
069180     WHEN OTHER                                                   07020019
069190        PERFORM CONVERT-TO-BH-SLAB                                07030019
069191     END-EVALUATE.                                                07040019
069192                                                                  07050019
069400     MOVE 'N'    TO SW-STOCK-SLAB                                 07060019
069500                                                                  07070019
069510*-      AUTO APPLY SLABS VIA HHMSIB05 OR SET AS STOCK             07080019
069600     IF K0D-MTS = 'Y' OR                                          07090019
069610        RETURN-SCARF-SLAB                                         07100019
069611                                                                  07110019
069620         IF RETURN-SCARF-SLAB                                     07120019
069631            MOVE 'Y'     TO K0D-MTS                               07130019
069631            MOVE ZEROS   TO ACC-IHE ACC-IHW                       07140019
069620            INSPECT K0D-PO-NUMBER TALLYING ACC-IHE                07150019
069620                    FOR ALL '09999'                               07160019
069620            INSPECT K0D-PO-NUMBER TALLYING ACC-IHW                07170019
069620                    FOR ALL '07777'                               07180019
069620            IF ACC-IHE > 0                                        07190019
069630               MOVE 'YH87109999 010000' TO K0D-PO-NUMBER          07200019
069640            END-IF                                                07210019
069620            IF ACC-IHW > 0                                        07220019
069630               MOVE 'YH87107777 010000' TO K0D-PO-NUMBER          07230019
069640            END-IF                                                07240019
069640         END-IF                                                   07250019
069650                                                                  07260019
069700         PERFORM 1110-GET-ORDER-AND-SPLIT                         07270019
069800         EVALUATE TRUE                                            07280019
069900         WHEN ORDER-SPLIT-NOT-FOUND                               07290019
069910              MOVE 'Y'    TO SW-STOCK-SLAB                        07300019
069920              MOVE SPACES TO K0D-MTS                              07310019
070000              PERFORM SEND-EDI-ERROR-FGN-SLABS                    07320019
070100         WHEN B1301-SHIPPED-COMPLETE OR                           07330019
070200              B1301-SPLIT-CANCELLED                               07340019
070500              PERFORM SEND-EDI-ERROR-FGN-SLABS                    07350019
070700              MOVE 'Y'    TO SW-STOCK-SLAB                        07360019
070701              MOVE SPACES TO K0D-MTS                              07370019
070800         END-EVALUATE                                             07380019
070900     END-IF                                                       07390019
071000                                                                  07400019
071100     EVALUATE TRUE                                                07410019
072300     WHEN C-DELETE                                                07420019
072400*       PLANT Q USED TO DELETE A SLAB FROM ACCT TABLE             07430019
072500        PERFORM DELETE-SLAB-TGSI101                               07440019
072600     WHEN OTHER                                                   07450019
072700*       CHG PLATE MFST TO ALL BE THE SAME                         07460019
072800        IF K0D-PLATE-160                                          07470019
072900           MOVE '99999'  TO K0D-MFST                              07480019
073000        END-IF                                                    07490019
073100                                                                  07500019
073200        IF K0D-DATE IS NUMERIC                                    07510019
073300           MOVE K0D-DATE TO WS-SHIP-DATE                          07520019
073400        END-IF                                                    07530019
073500        MOVE SAVE-DATE   TO K0D-DATE                              07540019
073600                                                                  07550019
073700        IF GRADE-FOUND OR K0D-STYPE = 'A'                         07560019
073900           IF K0D-STRIP                                           07570019
074000               PERFORM CHECK-TAPER                                07580019
074100               PERFORM CHECK-STRIP-DOWNGRADE                      07590019
074200           ELSE                                                   07600019
074400               PERFORM CHECK-FOR-DOWNGRADE                        07610019
074500           END-IF                                                 07620019
074600           PERFORM FORMAT-HIST-HEATSLAB                           07630019
074700           PERFORM FORMAT-HIST-SLAB-RECORDS                       07640019
074800           PERFORM FORMAT-OUTPUT-SLAB                             07650019
074900        ELSE                                                      07660019
075000           ADD 1 TO NOGRD-CNT                                     07670019
075100           MOVE HLF11K0D TO NOGRD-ENTRY(NOGRD-CNT)                07680019
075200        END-IF                                                    07690019
075300     END-EVALUATE                                                 07700019
075400                                                                  07710019
075500     PERFORM READ-SLAB-FILE.                                      07720019
075600                                                                  07730019
075700*---------------------------------------------------------------  07740019
075800*-   CONVERT TO BH SLAB ID FORMAT FROM NON BH PLANT SLAB ID       07750019
075900*---------------------------------------------------------------  07760019
<USER> <GROUP>.                                              07770019
076100                                                                  07780019
076200     MOVE K0D-HEAT TO K0D-FGN-HEAT                                07790019
076300                      K01-NON-BH-HEAT                             07800019
076400                                                                  07810019
076500     IF C-MEX                                                     07820019
076600        MOVE K0D-HEAT (7:3)   TO K01-NON-BH-SLAB                  07830019
076700     ELSE                                                         07840019
076800        MOVE HLF11K0D (71:5)  TO K01-NON-BH-SLAB                  07850019
076900     END-IF.                                                      07860019
077000                                                                  07870019
077100     CALL 'HSLFSM01' USING HSLFSK01.                              07880019
077200     MOVE K01-BH-SLAB  TO K0D-HEAT-ING-CUT.                       07890019
077300                                                                  07900019
077400*    FIND GRADE/QTR LETTER FROM PREVIOUS HEAT SEGMENT             07910019
077500     PERFORM FIND-HEAT-PREVIOUS-H311                              07920019
077600                                                                  07930019
077700*-   IHE SLAB NUM SCHEME NO LONGER GUARANTEE UNIQUE INGOT NUM     07940019
077800     IF K0D-STRIP                                                 07950019
077900        PERFORM GET-NEXT-INGOT                                    07960019
078000        MOVE WS-DSEG-NEXT-INGOT TO K0D-ING                        07970019
078100     END-IF.                                                      07980019
078200                                                                  07990019
078210*---------------------------------------------------------------  08000019
078220* USE GRADE AND QTR LETTER FROM PREVIOUS H311                     08010019
078230*---------------------------------------------------------------  08020019
<USER> <GROUP>.                                         08030019
078250     MOVE K0D-STEEL-GRADE       TO HIST-FGN-GRADE                 08040019
078260     MOVE K0D-HEAT              TO H311-KEY-VALUE                 08050019
078270     MOVE '('                   TO H311-LEFT-PAREN                08060019
078280     PERFORM CALL-GU-H311                                         08070019
078290     IF IMS-CALL-SUCCESSFUL                                       08080019
078291        SET GRADE-FOUND         TO TRUE                           08090019
078292        MOVE H311-GRADE-TAP     TO K0D-STEEL-GRADE                08100019
078293     ELSE                                                         08110019
078294        SET NO-GRADE-FOUND      TO TRUE                           08120019
078295        PERFORM CHECK-HEAT-PREV-QTR                               08130019
078296     END-IF.                                                      08140019
078297                                                                  08150019
078298*---------------------------------------------------------------  08160019
078299* IF H311 NOT FND CHECK HEAT EXISTS FROM PREVIOUS QTR             08170019
078300*---------------------------------------------------------------  08180019
<USER> <GROUP>.                                             08190019
078302     EVALUATE TRUE                                                08200019
078303     WHEN K01-BH-HEAT-YEAR = 'A'                                  08210019
078304          MOVE 'Z'TO H311-KEY-VALUE (4:1)                         08220019
078305     WHEN K01-BH-HEAT-YEAR = 'B'                                  08230019
078306          MOVE 'A'TO H311-KEY-VALUE (4:1)                         08240019
078307     WHEN K01-BH-HEAT-YEAR = 'C'                                  08250019
078308          MOVE 'B'TO H311-KEY-VALUE (4:1)                         08260019
078309     WHEN K01-BH-HEAT-YEAR = 'D'                                  08270019
078310          MOVE 'C'TO H311-KEY-VALUE (4:1)                         08280019
078311     WHEN K01-BH-HEAT-YEAR = 'E'                                  08290019
078312          MOVE 'D'TO H311-KEY-VALUE (4:1)                         08300019
078313     WHEN K01-BH-HEAT-YEAR = 'H'                                  08310019
078314          MOVE 'E'TO H311-KEY-VALUE (4:1)                         08320019
078315     WHEN K01-BH-HEAT-YEAR = 'J'                                  08330019
078316          MOVE 'H'TO H311-KEY-VALUE (4:1)                         08340019
078317     WHEN K01-BH-HEAT-YEAR = 'K'                                  08350019
078318          MOVE 'J'TO H311-KEY-VALUE (4:1)                         08360019
078319     WHEN K01-BH-HEAT-YEAR = 'L'                                  08370019
078320          MOVE 'K'TO H311-KEY-VALUE (4:1)                         08380019
078321     WHEN K01-BH-HEAT-YEAR = 'M'                                  08390019
078322          MOVE 'L'TO H311-KEY-VALUE (4:1)                         08400019
078323     WHEN K01-BH-HEAT-YEAR = 'N'                                  08410019
078324          MOVE 'M'TO H311-KEY-VALUE (4:1)                         08420019
078325     WHEN K01-BH-HEAT-YEAR = 'P'                                  08430019
078326          MOVE 'N'TO H311-KEY-VALUE (4:1)                         08440019
078327     WHEN K01-BH-HEAT-YEAR = 'S'                                  08450019
078328          MOVE 'P'TO H311-KEY-VALUE (4:1)                         08460019
078329     WHEN K01-BH-HEAT-YEAR = 'T'                                  08470019
078330          MOVE 'S'TO H311-KEY-VALUE (4:1)                         08480019
078331     WHEN K01-BH-HEAT-YEAR = 'U'                                  08490019
078332          MOVE 'T'TO H311-KEY-VALUE (4:1)                         08500019
078333     WHEN K01-BH-HEAT-YEAR = 'V'                                  08510019
078334          MOVE 'U'TO H311-KEY-VALUE (4:1)                         08520019
078335     WHEN K01-BH-HEAT-YEAR = 'W'                                  08530019
078336          MOVE 'V'TO H311-KEY-VALUE (4:1)                         08540019
078337     WHEN K01-BH-HEAT-YEAR = 'X'                                  08550019
078338          MOVE 'W'TO H311-KEY-VALUE (4:1)                         08560019
078339     WHEN K01-BH-HEAT-YEAR = 'Y'                                  08570019
078340          MOVE 'X'TO H311-KEY-VALUE (4:1)                         08580019
078341     WHEN K01-BH-HEAT-YEAR = 'Z'                                  08590019
078342          MOVE 'Y'TO H311-KEY-VALUE (4:1)                         08600019
078343     END-EVALUATE                                                 08610019
078344                                                                  08620019
078345     PERFORM CALL-GU-H311                                         08630019
078346     IF IMS-CALL-SUCCESSFUL                                       08640019
078347        SET GRADE-FOUND     TO TRUE                               08650019
078348        MOVE H311-GRADE-TAP TO K0D-STEEL-GRADE                    08660019
078349        MOVE H311-KEY-VALUE TO K0D-HEAT                           08670019
078350                               K01-BH-SLAB-HEAT                   08680019
078351     END-IF.                                                      08690019
078352                                                                  08700019
078360*-----------------------------------------------------------------08710019
078400*- HEATS ARE SAVED ON THE WORK DB SO THE                          08720019
078500*- THE INGOT NUMBER CAN BE INCREMENTED TO AVOID DUP SLABS         08730019
078600*-----------------------------------------------------------------08740019
078700 GET-NEXT-INGOT.                                                  08750019
078800     MOVE LENGTH OF WS-DSEG        TO WS-DSEG-LEN                 08760019
078900     MOVE WS-ASEG-KEY              TO ADP03SS1-KEY-VALUE          08770019
079000     MOVE K01-BH-SLAB-HEAT         TO DDP03SS1-KEY-VALUE          08780019
079100                                      WS-DSEG-HEAT                08790019
079200     MOVE SPACES                   TO WS-DSEG-TAGS                08800019
079300     MOVE IMS-LEFT-PAREN           TO ADP03SS1-QUALIFIER          08810019
079400                                      DDP03SS1-QUALIFIER          08820019
079500     MOVE IMS-GHU                  TO IMS-CALL-FUNCTION           08830019
079600     PERFORM INOUT240-CALL-CBLTDLI                                08840019
079700                                                                  08850019
079800     MOVE WS-DATE-1-CCYYMM TO WS-DSEG-LAST-DATE (1:6)             08860019
079900     MOVE SAVE-DATE-DD     TO WS-DSEG-LAST-DATE (7:2)             08870019
080000                                                                  08880019
080100     EVALUATE TRUE                                                08890019
080200        WHEN IMS-CALL-SUCCESSFUL                                  08900019
080300             ADD +1 TO WS-DSEG-NEXT-INGOT                         08910019
080400             PERFORM F70-REPLACE-WRKDB-HEAT                       08920019
080500                                                                  08930019
080600        WHEN IMS-SEG-NOT-FOUND                                    08940019
080700             MOVE 01 TO WS-DSEG-NEXT-INGOT                        08950019
080800             PERFORM F70-ADD-WRKDB-HEAT                           08960019
080900                                                                  08970019
081000        WHEN OTHER                                                08980019
081100             MOVE ABEND-4007 TO ABEND-CODE                        08990019
081200             PERFORM 9999-CALL-ABEND                              09000019
081300     END-EVALUATE.                                                09010019
081400                                                                  09020019
081500*-----------------------------------------------------------------09030019
081510*-----------------------------------------------------------------09040019
081600 F70-ADD-WRKDB-HEAT.                                              09050019
081700     MOVE LENGTH OF WS-DSEG         TO WS-DSEG-LEN                09060019
081800     MOVE IMS-LEFT-PAREN            TO ADP03SS1-QUALIFIER         09070019
081900     MOVE IMS-UNQUALIFIER           TO DDP03SS1-QUALIFIER         09080019
082000     MOVE IMS-ISRT                  TO IMS-CALL-FUNCTION          09090019
082100     PERFORM INOUT240-CALL-CBLTDLI.                               09100019
082200                                                                  09110019
082400*-----------------------------------------------------------------09120019
082410*-----------------------------------------------------------------09130019
082500 F70-REPLACE-WRKDB-HEAT.                                          09140019
082600     MOVE LENGTH OF WS-DSEG        TO WS-DSEG-LEN                 09150019
082700     MOVE IMS-LEFT-PAREN           TO ADP03SS1-QUALIFIER          09160019
082800     MOVE IMS-UNQUALIFIER          TO DDP03SS1-QUALIFIER          09170019
082900     MOVE K01-BH-SLAB-HEAT         TO WS-DSEG-HEAT                09180019
083000     MOVE IMS-REPL                 TO IMS-CALL-FUNCTION           09190019
083100     PERFORM INOUT220-CALL-CBLTDLI.                               09200019
083200                                                                  09210019
083210*-----------------------------------------------------------------09220019
083220*-----------------------------------------------------------------09230019
083400 CHECK-TAPER.                                                     09240019
083500                                                                  09250019
083600     MOVE ZEROS  TO WS-TAPER                                      09260019
083700     MOVE SPACES TO HIST-TAPER                                    09270019
083800                                                                  09280019
083900     IF K0D-WID2 NOT NUMERIC OR                                   09290019
084000        K0D-WID2 = ZEROS                                          09300019
084100        CONTINUE                                                  09310019
084200     ELSE                                                         09320019
084300        IF K0D-WID2 > K0D-WID                                     09330019
084400           COMPUTE WS-TAPER = K0D-WID2 - K0D-WID                  09340019
084500        END-IF                                                    09350019
084600        IF K0D-WID > K0D-WID2                                     09360019
084700           COMPUTE WS-TAPER = K0D-WID - K0D-WID2                  09370019
084800        END-IF                                                    09380019
084900        IF WS-TAPER > 1.2                                         09390019
085000           MOVE 'TL'   TO HIST-TAPER                              09400019
085100        END-IF                                                    09410019
085200     END-IF.                                                      09420019
085300                                                                  09430019
085310*-----------------------------------------------------------------09440019
085320*-----------------------------------------------------------------09450019
085400 CHECK-STRIP-DOWNGRADE.                                           09460019
085500                                                                  09470019
085600     MOVE SPACES                 TO CALL-11M54-SW.                09480019
085700     EVALUATE TRUE                                                09490019
085800     WHEN C-CLEV                                                  09500019
085900        MOVE K0D-QUAL-CODE       TO WS-CLEV-COND-CODE             09510019
086000                                    K0D-COND-CODE                 09520019
086100        IF CLEV-DG-COND-CODE                                      09530019
086200           MOVE 'Y'              TO CALL-11M54-SW                 09540019
086300        END-IF                                                    09550019
086400     WHEN C-IN-WEST OR C-IN-EAST                                  09560019
086500        MOVE K0D-COND-CODE       TO WS-IH-COND-CODE               09570019
086600        IF IH-DG-COND-CODE                                        09580019
086700           MOVE 'Y'              TO CALL-11M54-SW                 09590019
086800        END-IF                                                    09600019
086900     END-EVALUATE.                                                09610019
087000                                                                  09620019
087100* FOR IH EAST, IF GRADE IS 'A 963', LEAVE IT ALONE                09630019
087200* PER QA'S SQL THAT ALL DOWNGRADE LOGIC HERE IS BASED ON          09640019
087300     IF C-IN-EAST                                                 09650019
087400        IF  (K0D-STL-GRD-1 = 'A')                                 09660019
087500        AND (K0D-STL-GRADE-NUMERIC = '963')                       09670019
087600            MOVE SPACES          TO CALL-11M54-SW                 09680019
087700        END-IF                                                    09690019
087800     END-IF.                                                      09700019
087900                                                                  09710019
088000* IF GRADE IS ' A 999', LEAVE IT ALONE                            09720019
088100* PER QA'S SQL THAT ALL DOWNGRADE LOGIC HERE IS BASED ON          09730019
088200     IF  (K0D-STL-GRD-1 = 'A')                                    09740019
088300     AND (K0D-STL-GRADE-NUMERIC = '999')                          09750019
088400         MOVE SPACES          TO CALL-11M54-SW                    09760019
088500     END-IF.                                                      09770019
088600                                                                  09780019
088700     IF CALL-11M54                                                09790019
088800        PERFORM STRIP-DOWNGRADE                                   09800019
088900     END-IF.                                                      09810019
089000                                                                  09820019
089100 STRIP-DOWNGRADE.                                                 09830019
089200                                                                  09840019
089300     MOVE K0D-HEAT-ING-CUT       TO K0F-HSC.                      09850019
089400     MOVE K0D-STEEL-GRADE        TO K0F-CUR-SLAB-GRADE.           09860019
089500     MOVE SPACE                  TO K0F-NEW-HEAT-GRADE            09870019
089600                                    K0F-OLD-HEAT-GRADE.           09880019
089700*****CLEVELAND CONDITION CODE IS 2 POSITIONS IN A DIFFERENT       09890019
089800*****FIELD OF THE INPUT FILE.                                     09900019
089900     IF CLEVELAND                                                 09910019
090000        MOVE K0D-QUAL-CODE       TO K0F-CDE-FGN-SLAB-COND         09920019
090100     ELSE                                                         09930019
090200        MOVE K0D-FGN-COND-CODE   TO K0F-CDE-FGN-SLAB-COND         09940019
090300     END-IF.                                                      09950019
090400     MOVE K01-NON-BH-HEAT        TO K0F-NON-BH-HEAT.              09960019
090500     MOVE K01-NON-BH-SLAB        TO K0F-NON-BH-SLAB.              09970019
090600     MOVE SPACES                 TO K0F-OUTPUTS.                  09980019
090700     MOVE WORK-CALLING-PROGRAM   TO K0F-CALLING-PGM.              09990019
090800                                                                  10000019
090900     CALL 'HLF11M54' USING HLF11K0F.                              10010019
091000                                                                  10020019
091100     MOVE SPACES                 TO K0D-TRANS-IND.                10030019
091200     IF K0F-ERROR-MSG = SPACES                                    10040019
091300        MOVE K0F-NEW-SLAB-GRADE  TO K0D-STEEL-GRADE               10050019
091400        MOVE K0D-STEEL-GRADE     TO HIST-FGN-GRADE                10060019
091500     ELSE                                                         10070019
091600*K0D-TRANS-IND = FS-INDICATOR IN HSLFSP04                         10080019
091700*SETTING IT TO 'H' WILL PUT AN 'NT' MET REASON ON THE SLAB        10090019
091800*SINCE THE DOWNGRADE WAS NOT FOUND IN 11M54                       10100019
091900*THIS WILL ALERT QA THAT THE DOWNGRADE LOOKUP WAS UNSUCCESSFUL    10110019
092000        MOVE 'H'                 TO K0D-TRANS-IND                 10120019
092100     END-IF.                                                      10130019
092300                                                                  10140019
092400*---------------------------------------------------------------  10150019
092500*-BASED ON A CONDITION CODE OF THE SLAB THE SLAB MAY NEED TO BE   10160019
092600*-DOWNGRADED. TSQA101 TABLE IS USED TO DETERMINE IF THE           10170019
092700*-DOWNGRADED IS A MAJOR(3), INTERMED(2), OR MINOR(1)              10180019
092800*-TABLE IS MAINTAIN VIA HLFGNF23                                  10190019
092900*---------------------------------------------------------------  10200019
<USER> <GROUP>.                                             10210019
093100                                                                  10220019
093200      MOVE ZEROS  TO WS-TAPER                                     10230019
093300      MOVE SPACES TO HIST-TAPER                                   10240019
093400                                                                  10250019
093500      IF K0D-WID2 NOT NUMERIC OR                                  10260019
093600         K0D-WID2 = ZEROS                                         10270019
093700         CONTINUE                                                 10280019
093800      ELSE                                                        10290019
093900         IF K0D-WID2 > K0D-WID                                    10300019
094000            COMPUTE WS-TAPER = K0D-WID2 - K0D-WID                 10310019
094100         END-IF                                                   10320019
094200         IF K0D-WID > K0D-WID2                                    10330019
094300            COMPUTE WS-TAPER = K0D-WID - K0D-WID2                 10340019
094400         END-IF                                                   10350019
094500         IF WS-TAPER > 1.2                                        10360019
094600            MOVE 'TL'   TO HIST-TAPER                             10370019
094700         END-IF                                                   10380019
094800      END-IF                                                      10390019
094900                                                                  10400019
095000      MOVE SPACE TO SQA101-IND-LEVEL                              10410019
095100      MOVE K0D-STL-GRADE-NUMERIC TO WS-GRADE-CHECK                10420019
095200                                                                  10430019
095300      EVALUATE TRUE                                               10440019
095400      WHEN C-CLEV                                                 10450019
095500         MOVE K0D-QUAL-CODE TO K0D-COND-CODE                      10460019
095600         EVALUATE TRUE                                            10470019
095700         WHEN K0D-COND-CODE = '31' OR '32'                        10480019
095800               MOVE '1' TO SQA101-IND-LEVEL                       10490019
095900         WHEN K0D-COND-CODE = '42'                                10500019
096000               MOVE '2' TO SQA101-IND-LEVEL                       10510019
096100         WHEN K0D-COND-CODE = '52'                                10520019
096200               MOVE '3' TO SQA101-IND-LEVEL                       10530019
096300         END-EVALUATE                                             10540019
096400      WHEN C-IN-EAST                                              10550019
096500         EVALUATE TRUE                                            10560019
096600*        WHEN K0D-COND-CODE = '40' AND WS-GRADE-TYPE-1            10570019
096700*              MOVE '1' TO SQA101-IND-LEVEL                       10580019
096800         WHEN K0D-COND-CODE = '50'                                10590019
096900               MOVE '1' TO SQA101-IND-LEVEL                       10600019
097000         WHEN K0D-COND-CODE = '60' OR '70'                        10610019
097100               MOVE '3' TO SQA101-IND-LEVEL                       10620019
097200         END-EVALUATE                                             10630019
097300      WHEN C-IN-WEST                                              10640019
097400*        WEST IS OBSOLETE                                         10650019
097500         EVALUATE TRUE                                            10660019
097600         WHEN K0D-COND-CODE = '6D' OR '6H'                        10670019
097700               MOVE '1' TO SQA101-IND-LEVEL                       10680019
097800         WHEN K0D-COND-CODE = '7D' OR '7H' OR                     10690019
097900                              '8D' OR '8H'                        10700019
098000               MOVE '2' TO SQA101-IND-LEVEL                       10710019
098100         WHEN K0D-COND-CODE = '9D' OR '9H'                        10720019
098200               MOVE '3' TO SQA101-IND-LEVEL                       10730019
098300         END-EVALUATE                                             10740019
098400      END-EVALUATE                                                10750019
098500                                                                  10760019
098600      IF SQA101-IND-LEVEL > SPACES                                10770019
098700         MOVE K0D-STL-GRD-1         TO SQA101-CDE-GRADE (1:1)     10780019
098800         MOVE K0D-STL-GRADE-NUMERIC TO SQA101-CDE-GRADE (2:3)     10790019
098900         EXEC SQL                                                 10800019
099000             SELECT CDE_DG_GRADE                                  10810019
099100             INTO   :SQA101-CDE-DG-GRADE                          10820019
099200             FROM   DHSQA1.TSQA101                                10830019
099300               WHERE  CDE_GRADE     = :SQA101-CDE-GRADE           10840019
099400                 AND  IND_LEVEL     = :SQA101-IND-LEVEL           10850019
099500         END-EXEC                                                 10860019
099600         MOVE SQLCODE TO DB2-SQLCODE                              10870019
099700         IF DB2-SUCCESSFUL                                        10880019
099800            MOVE SQA101-CDE-DG-GRADE(1:1) TO K0D-STL-GRD-1        10890019
099900            MOVE SQA101-CDE-DG-GRADE(2:3) TO                      10900019
100000                                    K0D-STL-GRADE-NUMERIC         10910019
100100            MOVE K0D-STEEL-GRADE TO HIST-FGN-GRADE                10920019
100200         ELSE                                                     10930019
100300            MOVE SPACES TO K0D-COND-CODE                          10940019
100400         END-IF                                                   10950019
100500      END-IF.                                                     10960019
100600                                                                  10970019
100700*---------------------------------------------------------------  10980019
100800*---------------------------------------------------------------  10990019
<USER> <GROUP>.                                              11000019
101000                                                                  11010019
101100*      USES "P" TO UPDATE SLAB STATUS TO "CW"                     11020019
101200*      ELSE " " TO UPDATE SLAB STATUS TO "OW" IN HLSFSP04         11030019
101300       IF C-MEX                                                   11040019
101400          IF K0D-MOLD-TYPE = 'CW'                                 11050019
101500             MOVE 'P'     TO K0D-TRANS-IND                        11060019
101600          ELSE                                                    11070019
101700             MOVE SPACES  TO K0D-TRANS-IND                        11080019
101800          END-IF                                                  11090019
101900          MOVE SPACES     TO K0D-MOLD-TYPE                        11100019
102000       END-IF                                                     11110019
102100                                                                  11120019
102200*      CHECK COND CODE WHEN UNLOAD HLF11C30 SET STATUS TL         11130019
102300       IF HIST-TAPER = 'TL'                                       11140019
102400          MOVE '99'  TO K0D-COND-CODE                             11150019
102500       END-IF.                                                    11160019
102600                                                                  11170019
102700       EVALUATE TRUE                                              11180019
102800         WHEN C-MANUAL OR C-LEX OR C-SPARROWS                     11190019
102900              CONTINUE                                            11200019
103000         WHEN (C-IN-EAST OR C-CLEV) AND                           11210019
103100               K0D-PRE-ASN          AND                           11220019
103200               K0D-PLATE-160                                      11230019
103300                PERFORM FORMAT-PRE-ASN                            11240019
103400         WHEN OTHER                                               11250019
103500              PERFORM FORMAT-SLAB-TGSI101                         11260019
103800              EVALUATE TRUE                                       11270019
103810                WHEN C-COATES                                     11280019
103820*               COATES DUPS CAN NOT BE DETERMINED                 11290019
103900                  MOVE 0 TO CNT-ROWS                              11300019
103910                WHEN K0D-PLATE-160 AND C-CLEV                     11310019
103911*                 PLATE DUPS FOR CLEV NOT CHECKED BECAUSE         11320019
103912*                 OF PRE ASN SENT THEN SHIP ASN                   11330019
103930                  MOVE 0 TO CNT-ROWS                              11340019
104000                WHEN OTHER                                        11350019
104100                  PERFORM CHECK-SLAB-EXISTS                       11360019
104210              END-EVALUATE                                        11370019
104300                                                                  11380019
104400              IF CNT-ROWS = 0                                     11390019
104500                  PERFORM INSERT-SLAB-TGSI101                     11400019
104600                  IF (C-IN-EAST OR C-CLEV) AND                    11410019
104700                      K0D-PLATE-160                               11420019
104800                      PERFORM WRITE-SLAB-OW-REC                   11430019
104900                      PERFORM WRITE-FGN-OW-REC                    11440019
105000                  END-IF                                          11450019
105100              ELSE                                                11460019
105200                  PERFORM C55-SEND-DUP-EMAIL                      11470019
105300              END-IF                                              11480019
105400       END-EVALUATE.                                              11490019
105500                                                                  11500019
105600       IF CNT-ROWS = 0                                            11510019
105700          MOVE HLF11K0D  TO SLAB-OUTREC                           11520019
105800          WRITE SLAB-OUTREC                                       11530019
105900          IF K0D-STRIP                                            11540019
106000             PERFORM WRITE-HIST-REC                               11550019
106100          ELSE                                                    11560019
106200             PERFORM WRITE-PMHIST-REC                             11570019
106300          END-IF                                                  11580019
106400       END-IF.                                                    11590019
106500                                                                  11600019
106600*---------------------------------------------------------------  11610019
106700*---------------------------------------------------------------  11620019
<USER> <GROUP>.                                                  11630019
106900*      USES "P" TO UPDATE SLAB STATUS TO "CW" IN HLSFSP04         11640019
107000       MOVE 'P' TO K0D-TRANS-IND                                  11650019
107100*SP9R0783 ON PRE-ASN WRITE RECORD FOR HPLSIB69 PRE-ASN UPDATE     11660019
107200*         SPACES IN K0D-ID-SLAB-ORD REPRESENTS ORDERS BEFORE      11670019
107300*         THE INSTALL OF SLAB-ORDERING.                           11680019
107400*         OTHERWISE, WRITE A PRE-ASN RECORD FOR HPLSIB69.         11690019
107500       PERFORM PROCESS-TUNDISH                                    11700019
107600                                                                  11710019
107700       IF K0D-ID-SLAB-ORD = SPACES                                11720019
107800          CONTINUE                                                11730019
107900       ELSE                                                       11740019
108000          IF K0D-ID-SLAB-ORD NOT NUMERIC                          11750019
108100             CONTINUE                                             11760019
108200          ELSE                                                    11770019
108300             MOVE K0D-ID-SLAB-ORD   TO PRE-ASN-SLAB-ORD           11780019
108400             MOVE K0D-HEAT          TO PRE-ASN-HEAT               11790019
108500             MOVE K0D-ING           TO PRE-ASN-INGOT              11800019
108600             MOVE K0D-CUT           TO PRE-ASN-CUT                11810019
108700             PERFORM WRITE-OUT-PRE-ASN-REC                        11820019
108800          END-IF                                                  11830019
108900       END-IF.                                                    11840019
109000                                                                  11850019
109100*---------------------------------------------------------------  11860019
109200*---------------------------------------------------------------  11870019
<USER> <GROUP>.                                               11880019
109400                                                                  11890019
109500* IF THE VALUE IN CNT-ROWS IS GREATER THAN ZERO, THEN A ROW       11900019
109600* ALREADY EXISTS IN TABLE TGSI101 WITH THE CURRENT FOREIGN SLAB   11910019
109700* ID.  SINCE THE ID IS NOT A KEY IN THE TABLE, WE CAN NOT RELY ON 11920019
109800* DB2 RETURNING A NEGATIVE VALUE WHEN INSERTING.                  11930019
109900                                                                  11940019
110000     MOVE ZEROS                  TO CNT-ROWS                      11950019
110100                                                                  11960019
110200     EXEC SQL                                                     11970019
110300       SELECT COUNT(*)                                            11980019
110400       INTO   :CNT-ROWS                                           11990019
110500       FROM   DHGSI1.TGSI101                                      12000019
110600       WHERE  ID_FGN_SLAB_NUM = :GSI101-ID-FGN-SLAB-NUM           12010019
110700     END-EXEC                                                     12020019
110800                                                                  12030019
110900     MOVE SQLCODE                  TO DB2-SQLCODE                 12040019
111000     IF DB2-SUCCESSFUL                                            12050019
111100     OR DB2-NOT-FOUND                                             12060019
111200         CONTINUE                                                 12070019
111300     ELSE                                                         12080019
111400         MOVE ABEND-4033           TO ABEND-CODE                  12090019
111500         PERFORM 8888-CALL-SQL-ABEND                              12100019
111600     END-IF.                                                      12110019
111700                                                                  12120019
111800*---------------------------------------------------------------  12130019
111900*---------------------------------------------------------------  12140019
<USER> <GROUP>.                                                 12150019
112100*  CHECK TUNDISH INDICATOR                                        12160019
112200* '1' = FIRST SLAB ON TUNDISH                                     12170019
112300* '2' = MIDDLE OF TUNDISH                                         12180019
112400*  3' = LAST SLAB ON TUNDISH                                      12190019
112500                                                                  12200019
112600     MOVE SPACES TO GSI103-CDE-BH-GRADE-4                         12210019
112700                                                                  12220019
112800     IF K0D-FIRST-SLAB OR                                         12230019
112900        K0D-LAST-SLAB                                             12240019
113000        MOVE 'G'   TO K0D-STL-GRD-7                               12250019
113100     END-IF                                                       12260019
113200                                                                  12270019
113300     IF K0D-MIDDLE-SLAB                                           12280019
113400        IF K0D-STL-GRD-1 = 'A' OR 'B'                             12290019
113500           MOVE 'A' TO GSI103-CDE-BH-GRADE-4 (1:1)                12300019
113600        END-IF                                                    12310019
113700        IF K0D-STL-GRD-1 = 'G' OR 'H'                             12320019
113800           MOVE 'H' TO GSI103-CDE-BH-GRADE-4 (1:1)                12330019
113900        END-IF                                                    12340019
114000        MOVE K0D-STL-GRADE-NUMERIC TO GSI103-CDE-BH-GRADE-4 (2:3) 12350019
114100                                                                  12360019
114200*  IF GRADE IS IN TABLE THEN ALTER THE 6TH & 7TH POSITION         12370019
114300          EXEC SQL                                                12380019
114400           SELECT CDE_BH_GRADE_4                                  12390019
114500             INTO   :GSI103-CDE-BH-GRADE-4                        12400019
114600             FROM   DHGSI1.TGSI103                                12410019
114700             WHERE  CDE_BH_GRADE_4 = :GSI103-CDE-BH-GRADE-4       12420019
114800          END-EXEC                                                12430019
114900        MOVE SQLCODE TO DB2-SQLCODE                               12440019
115000        IF DB2-SUCCESSFUL                                         12450019
115100           MOVE 'UG' TO K0D-STL-GRD-6-7                           12460019
115200        END-IF                                                    12470019
115300     END-IF.                                                      12480019
115400                                                                  12490019
115500*---------------------------------------------------------------  12500019
115600*---------------------------------------------------------------  12510019
<USER> <GROUP>.                                            12520019
115800                                                                  12530019
116000       MOVE SPACES               TO HIST-FGN-SLAB-ID              12540019
116100                                                                  12550019
116200       EVALUATE TRUE                                              12560019
116300       WHEN C-CLEV                                                12570019
116400          MOVE K0D-FGN-HEAT(1:7) TO HIST-HEAT                     12580019
116500                                    HIST-FGN-SLAB-ID (1:7)        12590019
116600          MOVE HLF11K0D (71:3)   TO HIST-FGN-SLAB-ID (8:3)        12600019
116700       WHEN C-IN-EAST                                             12610019
116800          MOVE K0D-FGN-HEAT(1:6) TO HIST-HEAT                     12620019
116900                                    HIST-FGN-SLAB-ID (1:6)        12630019
117000          MOVE HLF11K0D (71:5)   TO HIST-FGN-SLAB-ID (7:5)        12640019
117100       WHEN C-IN-WEST                                             12650019
117200          MOVE K0D-FGN-HEAT(1:6) TO HIST-HEAT                     12660019
117300                                    HIST-FGN-SLAB-ID (1:6)        12670019
117400          MOVE HLF11K0D (71:5)   TO HIST-FGN-SLAB-ID (7:5)        12680019
117500       WHEN C-MEX                                                 12690019
117600          MOVE K0D-FGN-HEAT      TO HIST-HEAT                     12700019
117700          MOVE K0D-ID-SLAB-ORD   TO HIST-FGN-SLAB-ID              12710019
117800       WHEN OTHER                                                 12720019
117900          MOVE K0D-FGN-HEAT      TO HIST-HEAT                     12730019
118000                                    HIST-FGN-SLAB-ID (1:9)        12740019
118100          MOVE K0D-ING           TO HIST-FGN-SLAB-ID (10:2)       12750019
118200          MOVE K0D-CUT           TO HIST-FGN-SLAB-ID (12:1)       12760019
118300       END-EVALUATE.                                              12770019
118400                                                                  12780019
118500       MOVE SPACES               TO K0D-FGN-HEAT                  12790019
118600       MOVE HIST-HEAT            TO K0D-FGN-HEAT.                 12800019
118700                                                                  12810019
118800 PROCESS-NO-GRADE-RECORDS.                                        12820019
118900*---------------------------------------------------------------  12830019
119000* READ THE SLABS WITH NO GRADE AND TRY TO GET THE GRADE FROM      12840019
119100* H311 HEAT SEGMENT DB -                                          12850019
119200* IF H311 FOUND WRITE TO OUTPUT FILE AND BLANK OUT DATE           12860019
119300* TO REMOVE IT FROM NO GRADE PROCESSING                           12870019
119400*---------------------------------------------------------------  12880019
<USER>                                                                  <GROUP>
119600* PUT SLABS INTO TABLE FOR PROCESSING                             12900019
119700     PERFORM LOAD-NOGRD-TABLE.                                    12910019
119800                                                                  12920019
119900     MOVE 1 TO NOGRD-CNT.                                         12930019
120000     PERFORM UNTIL NOGRD-ENTRY(NOGRD-CNT) = SPACES OR             12940019
120100                   NOGRD-CNT = NOGRD-MAX                          12950019
120200         MOVE NOGRD-ENTRY(NOGRD-CNT) TO HLF11K0D                  12960019
120300         MOVE K0D-HEAT               TO H311-KEY-VALUE            12970019
120400         MOVE '('                    TO H311-LEFT-PAREN           12980019
120500         PERFORM CALL-GU-H311                                     12990019
120600         IF IMS-CALL-SUCCESSFUL                                   13000019
120700            MOVE K0D-STEEL-GRADE    TO HIST-FGN-GRADE             13010019
120800            MOVE H311-GRADE-TAP     TO K0D-STEEL-GRADE            13020019
120900            PERFORM DETERMINE-PLANT                               13030019
121100            IF K0D-STRIP                                          13040019
121200                PERFORM CHECK-TAPER                               13050019
121300                PERFORM CHECK-STRIP-DOWNGRADE                     13060019
121400            ELSE                                                  13070019
121600                PERFORM CHECK-FOR-DOWNGRADE                       13080019
121700            END-IF                                                13090019
121800            PERFORM FORMAT-NOGRD-HISTORY                          13100019
121900            PERFORM FORMAT-OUTPUT-SLAB                            13110019
122000            MOVE SPACES             TO K0D-DATE                   13120019
122100            MOVE HLF11K0D           TO NOGRD-ENTRY(NOGRD-CNT)     13130019
122200         END-IF                                                   13140019
122300         ADD 1 TO NOGRD-CNT                                       13150019
122400     END-PERFORM.                                                 13160019
122500                                                                  13170019
122600* PROCESS THRU TABLE 2ND TIME TO REMOVE SLABS THAT GRADES         13180019
122700* WERE FOUND AND REWRITE NO GRADES BACK TO FILE                   13190019
122800     PERFORM UNLOAD-NOGRD-TABLE.                                  13200019
122900                                                                  13210019
123000*---------------------------------------------------------------  13220019
123100* PROCESS TABLE REMOVING SLABS THAT HAVE SPACES FOR A DATE        13230019
123200* MEANING A GRADE WAS FOUND ON H311, ELSE                         13240019
123300* REWRITE SLABS BACK TO FILE THAT STILL HAVE NO GRADE             13250019
123400*---------------------------------------------------------------  13260019
<USER> <GROUP>.                                              13270019
123600     CLOSE NOGRD-FILE                                             13280019
123700     OPEN OUTPUT NOGRD-FILE                                       13290019
123800     MOVE 1 TO NOGRD-CNT.                                         13300019
123900     PERFORM UNTIL NOGRD-ENTRY(NOGRD-CNT) = SPACES OR             13310019
124000                   NOGRD-CNT = NOGRD-MAX                          13320019
124100         MOVE NOGRD-ENTRY(NOGRD-CNT) TO HLF11K0D                  13330019
124200         IF K0D-DATE > SPACES                                     13340019
124300            PERFORM WRITE-NO-GRADE-REC                            13350019
124400         END-IF                                                   13360019
124500         ADD 1 TO NOGRD-CNT                                       13370019
124600     END-PERFORM.                                                 13380019
124700                                                                  13390019
124800*---------------------------------------------------------------  13400019
124900* READ EXISTING NO GRADE SLABS FROM FILE AND LOAD TO TABLE        13410019
125000*---------------------------------------------------------------  13420019
<USER> <GROUP>.                                                13430019
125200     CLOSE NOGRD-FILE                                             13440019
125300     OPEN INPUT NOGRD-FILE                                        13450019
125400     SET FILE-NOT-DONE TO TRUE.                                   13460019
125500     PERFORM READ-NOGRD-FILE                                      13470019
125600     PERFORM UNTIL FILE-DONE OR                                   13480019
125700                   NOGRD-CNT = NOGRD-MAX                          13490019
125800         ADD 1 TO NOGRD-CNT                                       13500019
125900         MOVE NO-GRADE-REC TO NOGRD-ENTRY (NOGRD-CNT)             13510019
126000         PERFORM READ-NOGRD-FILE                                  13520019
126100     END-PERFORM.                                                 13530019
126200                                                                  13540019
126300*---------------------------------------------------------------  13550019
126400*---------------------------------------------------------------  13560019
<USER> <GROUP>.                                            13570019
126600                                                                  13580019
126800      MOVE SPACES               TO HIST-FGN-SLAB-ID               13590019
126900                                                                  13600019
127000      EVALUATE TRUE                                               13610019
127100        WHEN C-CLEV                                               13620019
127200            MOVE K0D-FGN-HEAT    TO HIST-HEAT                     13630019
127300                                    HIST-FGN-SLAB-ID              13640019
127400            MOVE HLF11K0D (71:3) TO HIST-FGN-SLAB-ID (8:3)        13650019
127500        WHEN C-IN-EAST                                            13660019
127600            MOVE K0D-FGN-HEAT    TO HIST-HEAT                     13670019
127700                                    HIST-FGN-SLAB-ID              13680019
127800            MOVE HLF11K0D (71:5) TO HIST-FGN-SLAB-ID (7:5)        13690019
127900        WHEN C-IN-WEST                                            13700019
128000            MOVE K0D-FGN-HEAT    TO HIST-HEAT                     13710019
128100                                    HIST-FGN-SLAB-ID              13720019
128200            MOVE HLF11K0D (71:5) TO HIST-FGN-SLAB-ID (7:5)        13730019
128300        WHEN OTHER                                                13740019
128400            MOVE K0D-FGN-HEAT    TO HIST-HEAT                     13750019
128500                                    HIST-FGN-SLAB-ID              13760019
128600            MOVE K0D-ING         TO HIST-FGN-SLAB-ID (10:2)       13770019
128700            MOVE K0D-CUT         TO HIST-FGN-SLAB-ID (12:1)       13780019
128800      END-EVALUATE.                                               13790019
128900                                                                  13800019
129000*  FORMAT REMAINING HIST DATA                                     13810019
129100      PERFORM FORMAT-HIST-SLAB-RECORDS.                           13820019
129200                                                                  13830019
129300*---------------------------------------------------------------  13840019
129400*---------------------------------------------------------------  13850019
<USER> <GROUP>.                                        13860019
129600                                                                  13870019
129700     MOVE WS-DATE-1-CC         TO HIST-CC                         13880019
129800     MOVE WS-DATE-1-YY         TO HIST-YY                         13890019
129900     MOVE SAVE-DATE-DD         TO HIST-DD                         13900019
130000     MOVE WS-DATE-1-MM         TO HIST-MM                         13910019
130100     MOVE K0D-MFST             TO HIST-MANIFEST                   13920019
130200     MOVE K0D-SO-NUM           TO HIST-SO-NUM                     13930019
130300     MOVE K0D-HEAT             TO HIST-BH-HEAT                    13940019
130400     MOVE K0D-ING              TO HIST-BH-INGOT                   13950019
130500     MOVE K0D-CUT              TO HIST-BH-CUT                     13960019
130600     MOVE K0D-THK              TO HIST-THICK                      13970019
130700     MOVE K0D-WT               TO HIST-WEIGHT                     13980019
130800     MOVE K0D-STEEL-GRADE      TO HIST-BH-GRADE                   13990019
130900     MOVE K0D-PO-NUMBER        TO HIST-PO-NUMBER.                 14000019
131000     MOVE K0D-COND-CODE        TO HIST-COND-CODE.                 14010019
131100*SP6R6270 - FOR COATESVILLE SLAB SWAP LENGTH FOR WIDTH            14020019
131200     IF C-COATES AND K0D-PLATE-160                                14030019
131300        MOVE K0D-WID           TO HIST-LENGTH                     14040019
131400        MOVE K0D-LEN           TO HIST-WIDTH                      14050019
131500     ELSE                                                         14060019
131600        MOVE K0D-WID           TO HIST-WIDTH                      14070019
131700        MOVE K0D-LEN           TO HIST-LENGTH                     14080019
131800     END-IF                                                       14090019
131900                                                                  14100019
132000     IF C-CLEV                                                    14110019
132100        MOVE K0D-CARNO         TO HIST-CARNO                      14120019
132200     ELSE                                                         14130019
132300        MOVE K0D-RRID          TO HIST-CARNO (1:4)                14140019
132400        MOVE K0D-CARNO         TO HIST-CARNO (5:6)                14150019
132500     END-IF                                                       14160019
132600                                                                  14170019
132700     IF K0D-STRIP                                                 14180019
132800        MOVE 'S'               TO HIST-MILL                       14190019
132900     ELSE                                                         14200019
133000        MOVE 'P'               TO HIST-MILL                       14210019
133100     END-IF.                                                      14220019
133200                                                                  14230019
133300*---------------------------------------------------------------  14240019
133400*---------------------------------------------------------------  14250019
<USER> <GROUP>.                                             14260019
133600     MOVE SPACES TO DCLTGSI101.                                   14270019
133700     INITIALIZE     DCLTGSI101.                                   14280019
133800                                                                  14290019
133900     IF WS-SHIP-DATE IS NUMERIC                                   14300019
134000         MOVE WS-SHIP-YY   TO GSI101-DTE-SHIP (3:2)               14310019
134100         MOVE WS-SHIP-MM   TO GSI101-DTE-SHIP (6:2)               14320019
134200         MOVE WS-SHIP-DD   TO GSI101-DTE-SHIP (9:2)               14330019
134300     ELSE                                                         14340019
134400         MOVE K0D-YY       TO GSI101-DTE-SHIP (3:2)               14350019
134500         MOVE K0D-MM       TO GSI101-DTE-SHIP (6:2)               14360019
134600         MOVE K0D-DD       TO GSI101-DTE-SHIP (9:2)               14370019
134700     END-IF                                                       14380019
134800     MOVE WS-DATE-1-CC     TO GSI101-DTE-SHIP (1:2)               14390019
134900     MOVE '-'              TO GSI101-DTE-SHIP (5:1)               14400019
135000                              GSI101-DTE-SHIP (8:1)               14410019
135100                                                                  14420019
135200     MOVE K0D-FGN-MFST         TO GSI101-ID-MANIFEST              14430019
135300     MOVE K0D-WID              TO GSI101-DIM-WID-SLAB             14440019
135400     MOVE K0D-THK              TO GSI101-DIM-THK-SLAB             14450019
135500     MOVE K0D-LEN              TO GSI101-DIM-LEN-SLAB             14460019
135600     MOVE K0D-WT               TO GSI101-WGT-SLAB-LBS             14470019
135700     MOVE K0D-STEEL-GRADE      TO GSI101-CDE-BH-GRADE             14480019
135800     MOVE K0D-RRID             TO GSI101-ID-VEHICLE               14490019
135900                                  GSI101-ID-CARRIER               14500019
136000     MOVE K0D-CARNO            TO GSI101-ID-VEHICLE-NUM           14510019
136100     MOVE K0D-PO-NUMBER        TO GSI101-CDE-PO-NUM               14520019
136200     MOVE HIST-HEAT            TO GSI101-CDE-FGN-HEAT             14530019
136300     MOVE HIST-BH-HEAT-ING-CUT TO GSI101-ID-BH-SLAB-NUM           14540019
136400     MOVE HIST-FGN-GRADE       TO GSI101-CDE-FGN-GRADE            14550019
136500     MOVE HIST-FGN-SLAB-ID     TO GSI101-ID-FGN-SLAB-NUM          14560019
136600     MOVE HIST-PLANT           TO GSI101-NAM-PLANT-FROM           14570019
136700     MOVE K0D-RECV-PLT-ORDER-1 TO GSI101-ID-SRC-PLANT-ORD-1       14580019
136800     MOVE K0D-RECV-PLT-ORDER-2 TO GSI101-ID-SRC-PLANT-ORD-2       14590019
136900     MOVE K0D-COND-CODE        TO GSI101-CDE-SLAB-COND            14600019
137100     MOVE K0D-FGN-COND-CODE    TO GSI101-CDE-FGN-SLAB-COND        14610019
137300                                                                  14620019
137500     MOVE K0D-ID-SLAB-ORD      TO GSI101-ID-SLAB-ORD              14630019
137600     MOVE ZEROS                TO GSI101-ID-MOTHER-SLAB-ORD       14640019
137700     MOVE K0D-IND-SLAB-TUNDISH TO GSI101-IND-SLAB-TUNDISH         14650019
137800*                                                                 14660019
137900     IF K0D-STRIP                                                 14670019
138000*    REMOVE IFS BAY NO LONGER REQUIRED                            14680019
138100*       IF RETURN-SCARF-SLAB                                      14690019
138200*          THIS PUTS SLAB IN BAY LOC 'IFS'                        14700019
138300*          R INDICATES SLAB IS TO BE SCARFED AND THEN             14710019
138400*          RETURNED TO SOURCE PLANT                               14720019
138500*          MOVE 'R'            TO GSI101-CDE-PRODUCT              14730019
138600*                                 K0D-TRANS-IND                   14740019
138700*       ELSE                                                      14750019
138800           MOVE 'S'            TO GSI101-CDE-PRODUCT              14760019
138900*       END-IF                                                    14770019
139000     ELSE                                                         14780019
139100        MOVE 'P'               TO GSI101-CDE-PRODUCT              14790019
139200     END-IF                                                       14800019
139300                                                                  14810019
139400     IF K0D-SHIP-METHOD > SPACES                                  14820019
139500        MOVE K0D-SHIP-METHOD TO GSI101-CDE-SHIP-MODE              14830019
139600     ELSE                                                         14840019
139700        IF C-CLEV                                                 14850019
139800           MOVE '2'          TO GSI101-CDE-SHIP-MODE              14860019
139900        ELSE                                                      14870019
140000           MOVE '1'          TO GSI101-CDE-SHIP-MODE              14880019
140100        END-IF                                                    14890019
140200     END-IF                                                       14900019
140300                                                                  14910019
140400*       AUTO SHIPMENT HPLSOB15 BECAUSE EAST/WEST DID NOT          14920019
140500*       SEND AN OFFICAL ASN SHIPMENT TRANS                        14930019
140600     IF K0D-SHIPPED-ASN                                           14940019
140700        MOVE 'UL'               TO GSI101-CDE-STATUS              14950019
140800        MOVE GSI101-DTE-SHIP    TO GSI101-DTE-UNLOAD              14960019
140900                                   GSI101-DTE-FRT-PAYMENT         14970019
141000     ELSE                                                         14980019
141100        IF K0D-TRANS-IND = 'P'                                    14990019
141200           MOVE 'CW'            TO GSI101-CDE-STATUS              15000019
141300        ELSE                                                      15010019
141400           MOVE 'OW'            TO GSI101-CDE-STATUS              15020019
141500        END-IF                                                    15030019
141600        MOVE DB2-DATE-DEFAULT   TO GSI101-DTE-FRT-PAYMENT         15040019
141700                                   GSI101-DTE-UNLOAD              15050019
141800     END-IF                                                       15060019
141900                                                                  15070019
142000*LF6R6427 - SET THE TAG TO "S" TO INDICATE STOCK SLAB             15080019
142200     IF STOCK-SLAB                                                15090019
142300        MOVE 'S'                TO GSI101-SW-APP-TO-BH-ORD        15100019
142400     ELSE                                                         15110019
142500        MOVE K0D-MTS            TO GSI101-SW-APP-TO-BH-ORD        15120019
142600     END-IF.                                                      15130019
142700                                                                  15140019
142900     IF K0D-STRIP AND                                             15150019
143000        K0F-ERROR-MSG = SPACES                                    15160019
143100        IF K0F-NEW-SLAB-LEVEL NOT EQUAL SPACES                    15170019
143200           MOVE K0F-NEW-SLAB-LEVEL TO                             15180019
143300                GSI101-CDE-QUALITY-LEVEL                          15190019
143400        END-IF                                                    15200019
143500     END-IF.                                                      15210019
143700                                                                  15220019
143800*---------------------------------------------------------------  15230019
143900*---------------------------------------------------------------  15240019
<USER> <GROUP>.                                               15250019
144100     MOVE K0D-HEAT (1:1)  TO H251-HEAT-NUMBER (1:1)               15260019
144200     MOVE K0D-HEAT (3:6)  TO H251-HEAT-NUMBER (2:6)               15270019
144300     MOVE K0D-HEAT (2:1)  TO H252-STRAND-ID                       15280019
144400     MOVE K0D-ING         TO H252-INGOT-ID                        15290019
144500     MOVE K0D-CUT         TO H253-CUT                             15300019
144600     MOVE 'SH'            TO H253-SLAB-STATUS                     15310019
144800     MOVE K0D-ID-SLAB-ORD TO H203-ID-SLAB-ORD                     15320019
144900*                                                                 15330019
145000     WRITE SLABOW-REC FROM HSLABADD.                              15340019
145100                                                                  15350019
145200*---------------------------------------------------------------  15360019
145300*---------------------------------------------------------------  15370019
<USER> <GROUP>.                                                15380019
145500     INITIALIZE HDPMS172                                          15390019
145600     MOVE K0D-HEAT          TO H172-HEAT                          15400019
145700     MOVE K0D-ING           TO H172-ING                           15410019
145800     MOVE K0D-CUT           TO H172-CUT                           15420019
145900     MOVE 'OW'              TO H172-UL-LOC                        15430019
146000     MOVE '000000'          TO H172-UL-DATE                       15440019
146100     MOVE K0D-WID           TO H172-UL-WID                        15450019
146200     MOVE K0D-THK           TO H172-UL-THK                        15460019
146300     MOVE K0D-LEN           TO H172-UL-LEN                        15470019
146400     MOVE K0D-WT            TO H172-UL-WGT                        15480019
146500     WRITE FGNOW-REC FROM HDPMS172.                               15490019
146600                                                                  15500019
146700*---------------------------------------------------------------  15510019
146800*---------------------------------------------------------------  15520019
<USER> <GROUP>.                                                 15530019
147000                                                                  15540019
147100*---    PLANT CODE USED TO INDICATE FROM IHE AND TO BE SCARFED    15550019
147200*---    AND RETURNED                                              15560019
147400*       USE H ONLY TO PREFIX SLAB WITH 5 (OLD WEST INDENTIFIER)   15570019
147000                                                                  15580019
065800     MOVE SPACES TO WS-SCARF-IND.                                 15590019
147000                                                                  15600019
147300     IF K0D-FROM-PLANT = 'H'                                      15610019
147500        MOVE K0D-FROM-PLANT   TO K01-PLANT                        15620019
147600*       CHANGE PLANT TO EAST SO ALL OTHER LOGIC IS EAST PROCESS   15630019
147700        MOVE 'I'              TO K0D-FROM-PLANT                   15640019
147800        SET RETURN-SCARF-SLAB TO TRUE                             15650019
147900     ELSE                                                         15660019
148000        MOVE K0D-FROM-PLANT   TO K01-PLANT                        15670019
148100     END-IF                                                       15680019
148200                                                                  15690019
148300     MOVE K0D-FROM-PLANT TO WS-PLANT                              15700019
148400                                                                  15710019
148500     IF K0D-RRID = 'LEX '                                         15720019
148600        SET C-LEX TO TRUE                                         15730019
148700     END-IF                                                       15740019
148800                                                                  15750019
148900     EVALUATE TRUE                                                15760019
149000      WHEN C-CLEV                                                 15770019
149100         MOVE 'CLEVEL'               TO HIST-PLANT                15780019
149200      WHEN C-IN-EAST                                              15790019
149300         MOVE 'INEAST'               TO HIST-PLANT                15800019
149400      WHEN C-IN-WEST                                              15810019
149500         MOVE 'INWEST'               TO HIST-PLANT                15820019
149600      WHEN C-MANUAL                                               15830019
149700         MOVE 'MANUAL'               TO HIST-PLANT                15840019
149800      WHEN C-COATES                                               15850019
149900         MOVE 'COATES'               TO HIST-PLANT                15860019
150000      WHEN C-SPARROWS                                             15870019
150100         MOVE 'SPOINT'               TO HIST-PLANT                15880019
150200      WHEN C-MEX                                                  15890019
150300         MOVE 'IMEXSA'               TO HIST-PLANT                15900019
150400      WHEN C-LEX                                                  15910019
150500         MOVE 'LEX   '               TO HIST-PLANT                15920019
150600      WHEN OTHER                                                  15930019
150700         MOVE K0D-FROM-PLANT         TO HIST-PLANT                15940019
150800     END-EVALUATE.                                                15950019
150900                                                                  15960019
151000*---------------------------------------------------------------  15970019
151100*---------------------------------------------------------------  15980019
<USER> <GROUP>.                                                 15990019
151300     EVALUATE TRUE                                                16000019
151400        WHEN C-CLEV AND K0D-THK = 0                               16010019
151500             MOVE 09.0  TO K0D-THK                                16020019
151600        WHEN C-IN-EAST AND K0D-THK = 0                            16030019
151700             MOVE 09.2  TO K0D-THK                                16040019
151800        WHEN C-IN-WEST AND K0D-THK = 0                            16050019
151900             MOVE 10.0  TO K0D-THK                                16060019
152000        WHEN C-COATES                                             16070019
152100             EVALUATE TRUE                                        16080019
152200             WHEN K0D-THK = ZEROS                                 16090019
152201                  MOVE 8.6  TO K0D-THK                            16100019
152210             WHEN K0D-THK = 12.0                                  16110019
152300                  MOVE 11.7 TO K0D-THK                            16120019
152410             WHEN K0D-THK < 8.6                                   16130019
152411                  CONTINUE                                        16140019
152430             WHEN OTHER                                           16150019
152500                  MOVE 8.6  TO K0D-THK                            16160019
152610             END-EVALUATE                                         16170019
152700        WHEN C-MEX                                                16180019
152800             MOVE 9.7       TO K0D-THK                            16190019
152900     END-EVALUATE.                                                16200019
153000                                                                  16210019
153100*:--------------------------------------------------------------  16220019
153200*:- CREATE LOG RECORD FOR MISC PLANTS MEX,LEX,SPPT                16230019
153300*:--------------------------------------------------------------  16240019
<USER> <GROUP>.                                        16250019
153500     CLOSE SLAB-OUTFILE.                                          16260019
153600     OPEN INPUT SLAB-OUTFILE.                                     16270019
153700     SET FILE-NOT-DONE TO TRUE                                    16280019
153800     PERFORM READ-SLABOUT-FILE                                    16290019
153900                                                                  16300019
154000     PERFORM UNTIL FILE-DONE                                      16310019
154100*LF6R6112 - INITIALIZE FIELD                                      16320019
154200         MOVE SPACES               TO HIST-FGN-SLAB-ID            16330019
154300         PERFORM DETERMINE-PLANT                                  16340019
154400         IF C-MANUAL OR C-LEX OR C-SPARROWS                       16350019
154500            MOVE K0D-HEAT (1:4)     TO HIST-HEAT (1:4)            16360019
154600            MOVE K0D-HEAT (5:1)     TO HIST-HEAT (9:1)            16370019
154700            MOVE K0D-HEAT (6:4)     TO HIST-HEAT (5:4)            16380019
154800            MOVE HIST-HEAT          TO HIST-FGN-SLAB-ID           16390019
154900            MOVE K0D-ING            TO HIST-FGN-SLAB-ID (10:2)    16400019
155000            MOVE K0D-CUT            TO HIST-FGN-SLAB-ID (12:1)    16410019
155100            MOVE HIST-FGN-SLAB-ID   TO HIST-BH-HEAT-ING-CUT       16420019
155200            MOVE K0D-STEEL-GRADE    TO HIST-FGN-GRADE             16430019
155300            PERFORM FORMAT-SLAB-TGSI101                           16440019
155400            PERFORM CHECK-SLAB-EXISTS                             16450019
155500            IF CNT-ROWS = 0                                       16460019
155600               PERFORM INSERT-SLAB-TGSI101                        16470019
155700            ELSE                                                  16480019
155800               PERFORM C55-SEND-DUP-EMAIL                         16490019
155900            END-IF                                                16500019
156000         END-IF                                                   16510019
156100         PERFORM READ-SLABOUT-FILE                                16520019
156200     END-PERFORM.                                                 16530019
156300                                                                  16540019
156500 SEND-EDI-ERROR-FGN-SLABS.                                        16550019
156600                                                                  16560019
156700     MOVE LIT-Y                     TO SW-EDI-ERROR-PRESENT       16570019
156800     IF EMAIL-TEXT-SUB = ZERO                                     16580019
157000         MOVE 'FOOTPRINT SLABS ADDED TO STOCK' TO EMAIL-SUBJECT   16590019
157200         MOVE '{{TO '               TO EMAIL-HEADER-LINE(4) (1:5) 16600019
157300         IF TEST-SYSTEM                                           16610019
157310             MOVE '<EMAIL>'               16620019
157400                                    TO EMAIL-HEADER-LINE(4) (6:72)16630019
157600         ELSE                                                     16640019
157700             MOVE '<EMAIL>'     16650019
157800                                    TO EMAIL-HEADER-LINE(4) (6:72)16660019
157900         END-IF                                                   16670019
157910                                                                  16680019
158000         MOVE ' }}'                 TO EMAIL-HEADER-LINE(4) (78:3)16690019
158100         MOVE LIT-EMAIL-FROM        TO EMAIL-FROM                 16700019
158200         ADD +1                  TO EMAIL-TEXT-SUB                16710019
158300         MOVE 'FRGN SLAB    '                                     16720019
158400           TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (1:13)              16730019
158500         MOVE 'PLT '                                              16740019
158600           TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (14:4)              16750019
158700         MOVE 'BH SLAB ID   '                                     16760019
158800           TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (19:13)             16770019
158900         MOVE 'BH ORDER       '                                   16780019
159000           TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (34:15)             16790019
159100         MOVE 'MANIFEST    '                                      16800019
159200           TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (55:12)             16810019
159300         MOVE 'MESSAGE     '                                      16820019
159400           TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (68:12)             16830019
159500         ADD +1                  TO EMAIL-TEXT-SUB                16840019
159600         MOVE SPACES                                              16850019
159700           TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB)                     16860019
159800     END-IF                                                       16870019
159900                                                                  16880019
160000     MOVE SPACES                       TO WS-MESSAGE              16890019
160100     EVALUATE TRUE                                                16900019
160200       WHEN ORDER-SPLIT-NOT-FOUND                                 16910019
160300         MOVE 'ORDER NOT FOUND'        TO WS-MESSAGE              16920019
160400       WHEN B1301-SHIPPED-COMPLETE                                16930019
160500         MOVE 'SHIP CMPLT ORDR'        TO WS-MESSAGE              16940019
160600       WHEN B1301-SPLIT-CANCELLED                                 16950019
160700         MOVE 'CANCELLED ORDER'        TO WS-MESSAGE              16960019
160800     END-EVALUATE                                                 16970019
160900                                                                  16980019
161000*- 999 IS USED TO INDICATE EXCEEDED EMAIL LINE LIMIT              16990019
161100     IF EMAIL-TEXT-SUB > 190                                      17000019
161200        IF EML-TEXT-SUB = 999                                     17010019
161300           CONTINUE                                               17020019
161400        ELSE                                                      17030019
161500           ADD +1   TO EMAIL-TEXT-SUB                             17040019
161600           MOVE 'EMAIL LINES EXCEEDED'                            17050019
161700                    TO EMAIL-TEXT-LINE(EML-TEXT-SUB) (1:)         17060019
161800           MOVE 999 TO EMAIL-TEXT-SUB                             17070019
161900        END-IF                                                    17080019
162000     ELSE                                                         17090019
162100        PERFORM FORMAT-HIST-HEATSLAB                              17100019
162200        ADD +1 TO EMAIL-TEXT-SUB                                  17110019
162300        MOVE HIST-FGN-SLAB-ID                                     17120019
162400             TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (1:12)            17130019
162500        MOVE K0D-FROM-PLANT                                       17140019
162600             TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (14:4)            17150019
162700        MOVE K0D-HEAT-ING-CUT                                     17160019
162800             TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (19:12)           17170019
162900        MOVE K0D-PO-NUMBER (3:15)                                 17180019
163000             TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (33:15)           17190019
163100        MOVE K0D-FGN-MFST                                         17200019
163200             TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (49:15)           17210019
163300        MOVE WS-MESSAGE                                           17220019
163400             TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB) (64:15)           17230019
163500     END-IF.                                                      17240019
163600                                                                  17250019
163700 ISRT-EDI-ERROR-EMAIL.                                            17260019
163800                                                                  17270019
163900*- 999 IS USED TO INDICATE EXCEEDED EMAIL LINE LIMIT              17280019
164000     IF EMAIL-TEXT-SUB = 999                                      17290019
164100        MOVE 190 TO EMAIL-TEXT-SUB                                17300019
164200     END-IF                                                       17310019
164300                                                                  17320019
164400     ADD +5               TO EMAIL-TEXT-SUB                       17330019
164500     MOVE 'THIS EMAIL IS GENERATED BY:'                           17340019
164600                          TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB)      17350019
164700     ADD +1               TO EMAIL-TEXT-SUB                       17360019
164800     MOVE 'JOB HSLFSIHB - PROGRAM HSLFSB16'                       17370019
164900                          TO EMAIL-TEXT-LINE(EMAIL-TEXT-SUB)      17380019
165000                                                                  17390019
165100     SET KML-SEND-EMAIL TO TRUE                                   17400019
165200                                                                  17410019
165300     MOVE LENGTH OF HLFGNKML-MSG-IN TO EMAIL-TRANS-LL             17420019
165400                                                                  17430019
165500     CALL 'CBLTDLI' USING IMS-PURG                                17440019
165600                          ALT01-IO-PCB                            17450019
165700                          HLFGNKML-MSG-IN                         17460019
165800                                                                  17470019
165900     MOVE ALT01-RET-STATUS TO IMS-CHECK-RETURN-CODE               17480019
166000     IF IMS-CALL-SUCCESSFUL                                       17490019
166100         CONTINUE                                                 17500019
166200     ELSE                                                         17510019
166300         MOVE ABEND-4025 TO ABEND-CODE                            17520019
166400         CALL 'DUMPER' USING ABEND-CODE                           17530019
166500     END-IF.                                                      17540019
166700                                                                  17550019
166800*---------------------------------------------------------        17560019
167000*---------------------------------------------------------        17570019
<USER> <GROUP>.                                              17580019
167200                                                                  17590019
167300     SET DUP-SLAB-EMAIL          TO TRUE                          17600019
167400                                                                  17610019
167800     MOVE '{{TO '                TO EML-HEADER-LINE (4) (1:5)     17620019
167900     MOVE '<EMAIL>'                       17630019
167910                                 TO EML-HEADER-LINE (4) (6:72)    17640019
168000     MOVE ' }}'                  TO EML-HEADER-LINE (4) (78:3)    17650019
168100     MOVE LIT-EMAIL-FROM         TO EML-FROM                      17660019
168200     MOVE LIT-DUP-SUBJECT        TO EML-SUBJECT                   17670019
168300                                                                  17680019
168900*- 999 IS USED TO INDICATE EXCEEDED EMAIL LINE LIMIT              17690019
169000     IF EML-TEXT-SUB > 190                                        17700019
169100        IF EML-TEXT-SUB = 999                                     17710019
169200           CONTINUE                                               17720019
169300        ELSE                                                      17730019
169400           ADD +1   TO EML-TEXT-SUB                               17740019
169500           MOVE 'EMAIL LINES EXCEEDED'                            17750019
169600                    TO EML-TEXT-LINE(EML-TEXT-SUB) (1:)           17760019
169700           MOVE 999 TO EML-TEXT-SUB                               17770019
169800        END-IF                                                    17780019
169900     ELSE                                                         17790019
170000        ADD +1  TO EML-TEXT-SUB                                   17800019
170100        MOVE 'FGN SLAB: '                                         17810019
170200             TO EML-TEXT-LINE(EML-TEXT-SUB) (1:)                  17820019
170300        MOVE GSI101-ID-FGN-SLAB-NUM                               17830019
170400             TO EML-TEXT-LINE(EML-TEXT-SUB) (11:)                 17840019
170500        MOVE 'BH SLAB: '                                          17850019
170600             TO EML-TEXT-LINE(EML-TEXT-SUB) (32:)                 17860019
170700        MOVE GSI101-ID-BH-SLAB-NUM                                17870019
170800             TO EML-TEXT-LINE(EML-TEXT-SUB) (41:)                 17880019
170900        MOVE 'MFST: '                                             17890019
171000             TO EML-TEXT-LINE(EML-TEXT-SUB) (54:)                 17900019
171100        MOVE GSI101-ID-MANIFEST                                   17910019
171200             TO EML-TEXT-LINE(EML-TEXT-SUB) (60:)                 17920019
171300        MOVE GSI101-NAM-PLANT-FROM                                17930019
171400             TO EML-TEXT-LINE(EML-TEXT-SUB) (71:)                 17940019
171500     END-IF.                                                      17950019
171600                                                                  17960019
171700*---------------------------------------------------------        17970019
171800*  SEND-EMAIL                                                     17980019
171900*  THIS CODE WILL SEND A MESSAGE TO HLFGNFAX TRANSACTION.         17990019
172000*  HLGFNFAX DOES THE FAXING AND THE EMAILING                      18000019
172100*  HLGFNFAX COPY IS FOR FAXING, HLFGNKML COPY IS FOR EMAILING     18010019
172200*---------------------------------------------------------        18020019
<USER> <GROUP>.                                              18030019
172400                                                                  18040019
172500*- 999 IS USED TO INDICATE EXCEEDED EMAIL LINE LIMIT              18050019
172600     IF EML-TEXT-SUB = 999                                        18060019
172700        MOVE 190 TO EML-TEXT-SUB                                  18070019
172800     END-IF                                                       18080019
172900                                                                  18090019
173000     ADD +5                  TO EML-TEXT-SUB                      18100019
173100     MOVE 'THIS EMAIL IS GENERATED BY:'                           18110019
173200                             TO EML-TEXT-LINE(EML-TEXT-SUB)       18120019
173300     ADD +1                  TO EML-TEXT-SUB                      18130019
173400     MOVE 'JOB HSLFSRCT - PROGRAM HSLFSB16'                       18140019
173500                             TO EML-TEXT-LINE(EML-TEXT-SUB).      18150019
173600                                                                  18160019
173700     SET KM2-SEND-EMAIL TO TRUE.                                  18170019
173800                                                                  18180019
173900     MOVE LENGTH OF HLFGNKML-EML-IN TO EML-TRANS-LL.              18190019
174000     CALL 'CBLTDLI' USING IMS-PURG                                18200019
174100                          ALT01-IO-PCB                            18210019
174200                          HLFGNKML-EML-IN.                        18220019
174300                                                                  18230019
174400     MOVE ALT01-RET-STATUS  TO IMS-CHECK-RETURN-CODE.             18240019
174500                                                                  18250019
174600     IF IMS-CALL-SUCCESSFUL                                       18260019
174700         CONTINUE                                                 18270019
174800     ELSE                                                         18280019
174900         MOVE ABEND-4005         TO ABEND-CODE                    18290019
175000         PERFORM 9999-CALL-ABEND                                  18300019
175100     END-IF.                                                      18310019
175200                                                                  18320019
175300*---------------------------------------------------------------  18330019
175400*---------------------------------------------------------------  18340019
<USER> <GROUP>.                                             18350019
175600     EXEC SQL                                                     18360019
175700        INSERT INTO DHGSI1.TGSI101                                18370019
175800           (ID_BH_SLAB_NUM,                                       18380019
175900             ID_FGN_SLAB_NUM,                                     18390019
176000             ID_MANIFEST,                                         18400019
176100             DTE_FRT_PAYMENT,                                     18410019
176200             DTE_SHIP,                                            18420019
176300             NAM_PLANT_FROM,                                      18430019
176400             CDE_PRODUCT,                                         18440019
176500             CDE_STATUS,                                          18450019
176600             DIM_WID_SLAB,                                        18460019
176700             DIM_THK_SLAB,                                        18470019
176800             DIM_LEN_SLAB,                                        18480019
176900             WGT_SLAB_LBS,                                        18490019
177000             CDE_BH_GRADE,                                        18500019
177100             CDE_FGN_GRADE,                                       18510019
177200             CDE_FGN_HEAT,                                        18520019
177300             CDE_PO_NUM,                                          18530019
177400             CDE_SHIP_MODE,                                       18540019
177500             ID_CARRIER,                                          18550019
177600             ID_VEHICLE,                                          18560019
177700             ID_VEHICLE_NUM,                                      18570019
177800             TSP_CRTE,                                            18580019
177900             DTE_UNLOAD,                                          18590019
178000             CDE_BAY_LOC,                                         18600019
178100             CDE_SLAB_COND,                                       18610019
178200             ID_SLAB_SEQ_NUM,                                     18620019
178300             ID_SRC_PLANT_ORD_1,                                  18630019
178400             ID_SRC_PLANT_ORD_2,                                  18640019
178500             ID_SLAB_ORD,                                         18650019
178600             ID_MOTHER_SLAB_ORD,                                  18660019
178700             IND_SLAB_TUNDISH,                                    18670019
178900             SW_APP_TO_BH_ORD,                                    18680019
179000*----                                                             18690019
179200             CDE_FGN_SLAB_COND,                                   18700019
179300             CDE_QUALITY_LEVEL)                                   18710019
179400*----                                                             18720019
179500           VALUES                                                 18730019
179600           (:GSI101-ID-BH-SLAB-NUM,                               18740019
179700            :GSI101-ID-FGN-SLAB-NUM,                              18750019
179800            :GSI101-ID-MANIFEST,                                  18760019
179900            :GSI101-DTE-FRT-PAYMENT,                              18770019
180000            :GSI101-DTE-SHIP,                                     18780019
180100            :GSI101-NAM-PLANT-FROM,                               18790019
180200            :GSI101-CDE-PRODUCT,                                  18800019
180300            :GSI101-CDE-STATUS,                                   18810019
180400            :GSI101-DIM-WID-SLAB,                                 18820019
180500            :GSI101-DIM-THK-SLAB,                                 18830019
180600            :GSI101-DIM-LEN-SLAB,                                 18840019
180700            :GSI101-WGT-SLAB-LBS,                                 18850019
180800            :GSI101-CDE-BH-GRADE,                                 18860019
180900            :GSI101-CDE-FGN-GRADE,                                18870019
181000            :GSI101-CDE-FGN-HEAT,                                 18880019
181100            :GSI101-CDE-PO-NUM,                                   18890019
181200            :GSI101-CDE-SHIP-MODE,                                18900019
181300            :GSI101-ID-CARRIER,                                   18910019
181400            :GSI101-ID-VEHICLE,                                   18920019
181500            :GSI101-ID-VEHICLE-NUM,                               18930019
181600             CURRENT TIMESTAMP,                                   18940019
181700            :GSI101-DTE-UNLOAD,                                   18950019
181800            :GSI101-CDE-BAY-LOC,                                  18960019
181900            :GSI101-CDE-SLAB-COND,                                18970019
182000            :GSI101-ID-SLAB-SEQ-NUM,                              18980019
182100            :GSI101-ID-SRC-PLANT-ORD-1,                           18990019
182200            :GSI101-ID-SRC-PLANT-ORD-2,                           19000019
182300            :GSI101-ID-SLAB-ORD,                                  19010019
182400            :GSI101-ID-MOTHER-SLAB-ORD,                           19020019
182500            :GSI101-IND-SLAB-TUNDISH,                             19030019
182700            :GSI101-SW-APP-TO-BH-ORD,                             19040019
182800*----                                                             19050019
183000            :GSI101-CDE-FGN-SLAB-COND,                            19060019
183100            :GSI101-CDE-QUALITY-LEVEL)                            19070019
183200*----                                                             19080019
183300     END-EXEC.                                                    19090019
183400                                                                  19100019
183500         MOVE SQLCODE TO DB2-SQLCODE                              19110019
183600         EVALUATE TRUE                                            19120019
183700           WHEN DB2-SUCCESSFUL                                    19130019
183800              CONTINUE                                            19140019
183900           WHEN MEMBER-EXISTS                                     19150019
184000                CONTINUE                                          19160019
184100           WHEN OTHER                                             19170019
184200             MOVE ABEND-4010 TO ABEND-CODE                        19180019
184300             PERFORM 8888-CALL-SQL-ABEND                          19190019
184400         END-EVALUATE.                                            19200019
184500                                                                  19210019
184600*---------------------------------------------------------------  19220019
184700*---------------------------------------------------------------  19230019
<USER> <GROUP>.                                             19240019
184900      MOVE K0D-HEAT      TO    GSI101-ID-BH-SLAB-NUM (1:9)        19250019
185000      MOVE K0D-ING       TO    GSI101-ID-BH-SLAB-NUM (10:2)       19260019
185100      MOVE K0D-CUT       TO    GSI101-ID-BH-SLAB-NUM (12:1)       19270019
185200      EXEC SQL                                                    19280019
185300         DELETE FROM DHGSI1.TGSI101                               19290019
185400         WHERE                                                    19300019
185500            ID_BH_SLAB_NUM = :GSI101-ID-BH-SLAB-NUM               19310019
185600      END-EXEC.                                                   19320019
185700       EVALUATE TRUE                                              19330019
185800         WHEN DB2-SUCCESSFUL                                      19340019
185900              DISPLAY 'DELETED ' GSI101-ID-BH-SLAB-NUM            19350019
186000         WHEN OTHER                                               19360019
186100              DISPLAY 'ERR DELETED ' GSI101-ID-BH-SLAB-NUM        19370019
186200       END-EVALUATE.                                              19380019
193100                                                                  19390019
193300 1110-GET-ORDER-AND-SPLIT.                                        19400019
193400* A PATH CALL GETTING BOTH A AND B SEGMENTS                       19410019
193500                                                                  19420019
193600     MOVE LIT-D                  TO H131A-COMMAND-CODE.           19430019
193700     MOVE IMS-LEFT-PAREN         TO H131A-QUALIFIER               19440019
193800                                    H131B-QUALIFIER.              19450019
193900                                                                  19460019
194000     MOVE K0D-PO-NUMBER (3:9)                                     19470019
194100       TO H131A-KEY-VALUE                                         19480019
194200     INSPECT K0D-PO-NUMBER (12:6)                                 19490019
194300         REPLACING ALL SPACES BY ZEROES                           19500019
194400     MOVE K0D-PO-NUMBER (12:6)                                    19510019
194500       TO H131B-KEY-VALUE                                         19520019
194600                                                                  19530019
194700     PERFORM GU-HLF1301A-B.                                       19540019
194800                                                                  19550019
194900     MOVE H13P1PCB-STATUS-CODE   TO IMS-CHECK-RETURN-CODE.        19560019
195000     MOVE IMS-NULL               TO H131A-COMMAND-CODE            19570019
195100                                                                  19580019
195200     EVALUATE TRUE                                                19590019
195300       WHEN IMS-CALL-SUCCESSFUL                                   19600019
195400         MOVE LIT-Y              TO SW-ORDER-SPLIT-FOUND          19610019
195500       WHEN IMS-SEG-NOT-FOUND  AND                                19620019
195600            H13P1PCB-SEGMENT-LEVEL = '01'                         19630019
195700         MOVE LIT-N              TO SW-ORDER-SPLIT-FOUND          19640019
195800         INITIALIZE HLF1301B                                      19650019
195900       WHEN IMS-SEG-NOT-FOUND                                     19660019
196000         MOVE LIT-N              TO SW-ORDER-SPLIT-FOUND          19670019
196100         INITIALIZE HLF1301A                                      19680019
196200         INITIALIZE HLF1301B                                      19690019
196300       WHEN OTHER                                                 19700019
196400         MOVE '4003'             TO ABEND-CODE                    19710019
196500         PERFORM 9999-CALL-ABEND                                  19720019
196600     END-EVALUATE.                                                19730019
196800                                                                  19740019
196900 READ-SLAB-FILE.                                                  19750019
197100     MOVE LIT-N                  TO SW-ORDER-SPLIT-FOUND.         19760019
197200     INITIALIZE HLF1301A                                          19770019
197300     INITIALIZE HLF1301B                                          19780019
197400                                                                  19790019
197500     READ SLAB-FILE                                               19800019
197600        AT END                                                    19810019
197700           SET FILE-DONE TO TRUE                                  19820019
197900           IF EDI-ERROR-PRESENT                                   19830019
198000               PERFORM ISRT-EDI-ERROR-EMAIL                       19840019
198100           END-IF                                                 19850019
198200     END-READ.                                                    19860019
198300     MOVE SLAB-INREC TO HLF11K0D.                                 19870019
198400                                                                  19880019
198500 READ-SLABOUT-FILE.                                               19890019
198600     READ SLAB-OUTFILE                                            19900019
198700        AT END                                                    19910019
198800           SET FILE-DONE TO TRUE                                  19920019
198900     END-READ.                                                    19930019
199000     MOVE SLAB-OUTREC TO HLF11K0D.                                19940019
199100                                                                  19950019
199200 READ-NOGRD-FILE.                                                 19960019
199300     READ NOGRD-FILE                                              19970019
199400        AT END                                                    19980019
199500           SET FILE-DONE TO TRUE                                  19990019
199600     END-READ.                                                    20000019
199700                                                                  20010019
199800 WRITE-NO-GRADE-REC.                                              20020019
199900     WRITE NO-GRADE-REC FROM HLF11K0D.                            20030019
200000                                                                  20040019
200100 WRITE-HIST-REC.                                                  20050019
200200     WRITE HISTORY-RECORD FROM HISTORY-FORMAT.                    20060019
200300                                                                  20070019
200400 WRITE-PMHIST-REC.                                                20080019
200500     WRITE PMHIST-RECORD  FROM HISTORY-FORMAT.                    20090019
200600                                                                  20100019
200700 CALL-GU-H311.                                                    20110019
200800     CALL 'CBLTDLI' USING IMS-GU                                  20120019
200900                          H31-PCB                                 20130019
201000                          HMTHA311                                20140019
201100                          H311-SSA.                               20150019
201200     MOVE H31-RET-STATUS TO IMS-CHECK-RETURN-CODE.                20160019
201300                                                                  20170019
201400*-----------------------------------------------------------------20180019
201500*-----------------------------------------------------------------20190019
201600 INOUT220-CALL-CBLTDLI.                                           20200019
201700     CALL 'CBLTDLI' USING IMS-CALL-FUNCTION                       20210019
201800                          LFDPP031                                20220019
201900                          WS-DSEG.                                20230019
202000     MOVE LFDPP031-STATUS-CODE TO IMS-CHECK-RETURN-CODE.          20240019
202100                                                                  20250019
202200*-----------------------------------------------------------------20260019
202300*-----------------------------------------------------------------20270019
202400 INOUT240-CALL-CBLTDLI.                                           20280019
202500     CALL 'CBLTDLI' USING IMS-CALL-FUNCTION                       20290019
202600                          LFDPP031                                20300019
202700                          WS-DSEG                                 20310019
202800                          ADP03SS1-SSA                            20320019
202900                          DDP03SS1-SSA.                           20330019
203000     MOVE LFDPP031-STATUS-CODE  TO IMS-CHECK-RETURN-CODE.         20340019
203100                                                                  20350019
203300 GU-HLF1301A-B.                                                   20360019
203400                                                                  20370019
203500     CALL 'CBLTDLI' USING IMS-GU                                  20380019
203600                          H13P1PCB                                20390019
203700                          HLF1301A                                20400019
203800                          H131ASSA                                20410019
203900                          H131BSSA.                               20420019
204000                                                                  20430019
204200 WRITE-OUT-PRE-ASN-REC.                                           20440019
204300                                                                  20450019
204400     WRITE OUT-PRE-ASN-RECORD      FROM OUT-PRE-ASN-REC.          20460019
204500                                                                  20470019
204600 8888-CALL-SQL-ABEND.                                             20480019
204700                                                                  20490019
204800     INITIALIZE DB2-ERROR-TEXT                                    20500019
204900     MOVE SQLCA                  TO SQLCA-ERROR-AREA              20510019
205000     MOVE SQLCODE                TO SQLCODE4                      20520019
205100     CALL 'DSNTIAR' USING SQLCA                                   20530019
205200                          DB2-ERROR-MESSAGE                       20540019
205300                          DB2-ERROR-TEXT-LEN                      20550019
205400                                                                  20560019
205500     IF RETURN-CODE = ZERO                                        20570019
205600        CONTINUE                                                  20580019
205700     ELSE                                                         20590019
205800        MOVE RETURN-CODE        TO TIARCODE8                      20600019
205900     END-IF                                                       20610019
206000     PERFORM 9999-CALL-ABEND.                                     20620019
206100                                                                  20630019
206200*---------------------------------------------------------------  20640019
206300*---------------------------------------------------------------  20650019
<USER> <GROUP>.                                                 20660019
206500                                                                  20670019
206600     CALL 'DUMPER' USING ABEND-CODE.                              20680019
