***********************************************************************         
***                                                                 ***         
***            CHEMICAL ANALYSIS - H31 (24-30 MONTHS RETAINMENT)    ***         
***                        6/10/79 - RWT                            ***         
***                                                                 ***         
***            HMTHA315, HMTHA316, AND HMTHA317 HAVE A 30 DAY       ***         
***                        RETIREMENT - JRP                         ***         
***                                                                 ***         
*** 4/19/15 MN ADD CDC EXIT ROUTINE - HMTHA311 ONLY                 ***         
***                                                                 ***         
***********************************************************************         
       DBD     NAME=H31,ACCESS=(HDAM,OSAM),RMNAME=(DFSHDC40,5,16387)            
       DATASET DD1=H31,DEVICE=3380,SIZE=4096                                    
**                                                                              
**             OFFICIAL ANALYSIS FOR HEATS                                      
**                                                                              
       SEGM    NAME=HMTHA311,PARENT=0,BYTES=256,POINTER=T,FREQ=65000,  X        
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                         
       FIELD   NAME=(H311HEAT,SEQ,U),BYTES=9,START=1,TYPE=C                     
       FIELD   NAME=H311YRCD,BYTES=1,START=4,TYPE=C                             
       LCHILD  NAME=(H311INDX,H31X1),PTR=INDX                                   
       XDFLD   NAME=H31X1,SEGMENT=HMTHA311,                            X        
               SRCH=H311HEAT,                                          X        
               EXTRTN=H31X1EXT                                                  
**                                                                              
**             INGOT ANALYSIS                                                   
**                                                                              
       SEGM    NAME=HMTHA312,BYTES=200,POINTER=T,FREQ=1,               X        
               PARENT=((HMTHA311,SNGL))                                         
       FIELD   NAME=(H312INGT,SEQ,U),BYTES=3,START=1,TYPE=C                     
**                                                                              
**             OFFGRADE DISPOSITIONS                                            
**                 NOTE: FREQ IS BASED ON 2000 OFFGRADE HEATS                   
**                                                                              
       SEGM    NAME=HMTHA313,BYTES=50,POINTER=T,FREQ=3,                X        
               PARENT=((HMTHA311,SNGL))                                         
       FIELD   NAME=(H313DISP,SEQ,U),BYTES=1,START=1,TYPE=C                     
**                                                                              
**             OFFGRADE DISPOSITION REMARKS                                     
**                 NOTE: FREQ IS BASED ON 2000 OFFGRADE HEATS                   
**                                                                              
       SEGM    NAME=HMTHA314,BYTES=80,POINTER=T,FREQ=3,                X        
               PARENT=((HMTHA311,SNGL))                                         
**                                                                              
**             SPECTRO ANALYSIS SEGMENT 30 DAY RETIREMENT                       
**                                                                              
       SEGM    NAME=HMTHA315,BYTES=352,POINTER=T,FREQ=15,              X        
               PARENT=((HMTHA311,SNGL))                                         
       FIELD   NAME=(H315TEST,SEQ,U),BYTES=5,START=1,TYPE=C                     
**                                                                              
**             LECO ANALYSIS SEGMENT 30 DAY RETIREMENT                          
**                                                                              
       SEGM    NAME=HMTHA316,BYTES=120,POINTER=T,FREQ=1,               X        
               PARENT=((HMTHA315,SNGL))                                         
**                                                                              
**             SLAG ANALYSIS SEGMENT 30 DAY RETIREMENT                          
**                                                                              
       SEGM    NAME=HMTHA317,BYTES=312,POINTER=T,FREQ=1,               X        
               PARENT=((HMTHA311,SNGL))                                         
**                                                                              
**                                                                              
       DBDGEN                                                                   
       FINISH                                                                   
       END                                                                      
