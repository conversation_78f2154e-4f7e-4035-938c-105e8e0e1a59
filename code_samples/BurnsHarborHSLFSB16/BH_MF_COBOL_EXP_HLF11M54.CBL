000100 IDENTIFICATION DIVISION.                                         00000100
000200 PROGRAM-ID. HLF11M54.                                            00000200
000300 AUTHOR. APRIL NERI.                                              00000300
000400 INSTALLATION. BURNS HARBOR.                                      00000400
000500 DATE-WRITTEN. 10/2017.                                           00000500
000600 DATE-COMPILED.                                                   00000600
000700     EJECT                                                        00000700
000800******************************************************************00000800
      *  THIS PROGRAM IS A CLONE OF HLF11M53 BUT PROCESSES FOREIGN SLABS        
      *  ONLY. THIS PROGRAM USES DB2 TABLE TSQA201, TSQA202 & TSQA205.          
      ******************************************************************        
000900*  P U R P O S E :                                                00000900
000901*  ---------------                                                00000901
000910*  IF A HEAT IS DOWN GRADED AND IF THE CURRENT SLABS IN INV       00000910
001000*  ALREADY HAVE A DOWN GRADE (GRADE) THEN VIA A TABLE FIND        00001000
001100*  THE EQUIVALENT DOWN GRADE LEVEL AND IT'S CORRESPONDING         00001100
001110*  DOWN GRADE (GRADE).                                            00001110
001500*                                                                 00001140
001500* NOTE: DB2 TABLE MAINT SCREEN HLFGNF88                           00001500
001500*                                                                 00001510
001600*  INPUTS ARE :    SLAB ID, CURRENT SLAB INV GRADE, SLAB COND     00001600
001800*                  CODE, NON-BH HEAT AND SLAB ID.                 00001800
001900*  OUTPUTS ARE :   NEW SLAB GRADE                                 00001900
002000******************************************************************00002000
002100*                    C H A N G E  H I S T O R Y                   00002100
002200*                                                                 00002200
002300*  DATE    REQUEST #  INIT.  CHANGE DESCRIPTION                   00002300
002604*                                                                 00002608
002604* 10/03/17 QA7R7593  HISAN   NEW SUBROUTINE TO DETERMINE FOREIGN  00002607
002604*                            SLAB DOWNGRADES.                     00002607
002604*                            REPLACE OBSOLETE DB2 TABLE TSQA101   00002607
002604*                            WITH TABLE TSQA201.                  00002607
002604*                            ADD TABLE TSQA205 FOR FINDING THE    00002608
002604*                            6,7 ALPHAS OF FOREIGN SLABS.         00002608
002604* 06/13/19 QA9R9282  HISAN   ADD EXTRA CHECK TO DETERMINE CLEV    00002607
002604*                            SLABS. SOME IH SLABS WERE INCORRECTLY00002607
002604*                            INCLUDED IN THIS LOGIC CAUSING 6TH   00002607
002604*                            ALPHAS TO BE ASSIGNED WRONG.         00002607
002604*                            ALSO, ADD LOGIC TO USE FOREIGN GRADE 00002607
002604*                            COLUMN AS 6TH ALPHA IN SOME CASES.   00002607
002604*                            SPQA WANTS CERTAIN GRADES TO NOT GO  00002607
002604*                            THROUGH THE 6TH ALPHA LOGIC & THIS   00002607
002604*                            COLUMN ON TSQA202 WAS NEVER USED.    00002607
002604* 07/18/19 QA9R9320  HISAN   CHANGE IHE,IHW PRODUCED FROM LOGIC   00002607
002604*                            TO ACCURATELY DETERMINE PLANT.       00002607
002604*                                                                 00002608
002610******************************************************************00002610
002900     EJECT                                                        00002900
003000 ENVIRONMENT DIVISION.                                            00003000
003100 CONFIGURATION SECTION.                                           00003100
003200 SOURCE-COMPUTER. IBM-370.                                        00003200
003300 OBJECT-COMPUTER. IBM-370.                                        00003300
003400 INPUT-OUTPUT SECTION.                                            00003400
003500 FILE-CONTROL.                                                    00003500
003600 DATA DIVISION.                                                   00003600
003700 FILE SECTION.                                                    00003700
003800 WORKING-STORAGE SECTION.                                         00003800
003900******************************************************************00003900
003910     EXEC SQL INCLUDE TSQA201 END-EXEC.                           00003910
      ******************************************************************        
      * DCLGEN TABLE(DHSQA2.TSQA201)                                   *        
      *        LIBRARY(HLDB2.COBLIB(TSQA201))                          *        
      *        ACTION(REPLACE)                                         *        
      *        LANGUAGE(COBOL)                                         *        
      *        APOST                                                   *        
      * ... IS THE DCLGEN COMMAND THAT MADE THE FOLLOWING STATEMENTS   *        
      ******************************************************************        
           EXEC SQL DECLARE DHSQA2.TSQA201 TABLE                                
           ( CDE_GRADE                      CHAR(7) NOT NULL,                   
             CDE_QUALITY_LEVEL              DECIMAL(2, 0) NOT NULL,             
             CDE_FINAL_GRADE                CHAR(7) NOT NULL,                   
             TSP_DATAWH_CREATED             TIMESTAMP NOT NULL,                 
             TSP_DATAWH_UPDATED             TIMESTAMP NOT NULL                  
           ) END-EXEC.                                                          
      ******************************************************************        
      * COBOL DECLARATION FOR TABLE DHSQA2.TSQA201                     *        
      ******************************************************************        
       01  DCLTSQA201.                                                          
           10 SQA201-CDE-GRADE          PIC X(7).                               
           10 SQA201-CDE-QUALITY-LEVEL  PIC S9(2)V USAGE COMP-3.                
           10 SQA201-CDE-FINAL-GRADE    PIC X(7).                               
           10 SQA201-TSP-DATAWH-CREATED PIC X(26).                              
           10 SQA201-TSP-DATAWH-UPDATED PIC X(26).                              
      ******************************************************************        
      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 5       *        
      ******************************************************************        
003910     EXEC SQL INCLUDE TSQA202 END-EXEC.                           00003910
      ******************************************************************        
      * DCLGEN TABLE(DHSQA2.TSQA202)                                   *        
      *        LIBRARY(HLDB2.COBLIB(TSQA202))                          *        
      *        ACTION(REPLACE)                                         *        
      *        LANGUAGE(COBOL)                                         *        
      *        APOST                                                   *        
      * ... IS THE DCLGEN COMMAND THAT MADE THE FOLLOWING STATEMENTS   *        
      ******************************************************************        
           EXEC SQL DECLARE DHSQA2.TSQA202 TABLE                                
           ( NAM_PRODUCE_FROM               CHAR(10) NOT NULL,                  
             CDE_BH_GRADE                   CHAR(7)  NOT NULL,                  
             CDE_FGN_SLAB_COND              CHAR(2)  NOT NULL,                  
             CDE_QUALITY_LEVEL              DECIMAL(2, 0) NOT NULL,             
             CDE_FGN_GRADE                  CHAR(7)  NOT NULL,                  
             TSP_UPDTE_LAST                 TIMESTAMP NOT NULL,                 
             CDE_UPDTE_LAST_ID              CHAR(8) NOT NULL                    
           ) END-EXEC.                                                          
      ******************************************************************        
      * COBOL DECLARATION FOR TABLE DHSQA2.TSQA202                     *        
      ******************************************************************        
       01  DCLTSQA202.                                                          
           10 SQA202-NAM-PRODUCE-FROM  PIC X(10).                               
           10 SQA202-CDE-BH-GRADE      PIC X(7).                                
           10 SQA202-CDE-FGN-SLAB-COND PIC X(2).                                
           10 SQA202-CDE-QUALITY-LEVEL PIC S9(2)V USAGE COMP-3.                 
           10 SQA202-CDE-FGN-GRADE     PIC X(7).                                
           10 SQA202-TSP-UPDTE-LAST    PIC X(26).                               
           10 SQA202-CDE-UPDTE-LAST-ID PIC X(8).                                
      ******************************************************************        
      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 7       *        
      ******************************************************************        
003910     EXEC SQL INCLUDE TSQA205 END-EXEC.                           00003910
      ******************************************************************        
      * DCLGEN TABLE(DHSQA2.TSQA205)                                   *        
      *        LIBRARY(HLDB2.COBLIB(TSQA205))                          *        
      *        LANGUAGE(COBOL)                                         *        
      *        NAMES(SQA205-)                                          *        
      *        APOST                                                   *        
      *        COLSUFFIX(YES)                                          *        
      * ... IS THE DCLGEN COMMAND THAT MADE THE FOLLOWING STATEMENTS   *        
      ******************************************************************        
           EXEC SQL DECLARE DHSQA2.TSQA205 TABLE                                
           ( CDE_FGN_SLAB_POS2              CHAR(1)   NOT NULL,                 
             CDE_FGN_SLAB_POS8              CHAR(1)   NOT NULL,                 
             CDE_GRD_ALPHA6                 CHAR(1)   NOT NULL,                 
             CDE_GRD_ALPHA7                 CHAR(1)   NOT NULL,                 
             TSP_UPDTE_LAST                 TIMESTAMP NOT NULL,                 
             CDE_UPDTE_LAST_ID              CHAR(8)   NOT NULL                  
           ) END-EXEC.                                                          
      ******************************************************************        
      * COBOL DECLARATION FOR TABLE DHSQA2.TSQA205                     *        
      ******************************************************************        
       01  DCLTSQA205.                                                          
           10 SQA205-CDE-FGN-SLAB-POS2 PIC X(1).                                
           10 SQA205-CDE-FGN-SLAB-POS8 PIC X(1).                                
           10 SQA205-CDE-GRD-ALPHA6    PIC X(1).                                
           10 SQA205-CDE-GRD-ALPHA7    PIC X(1).                                
           10 SQA205-TSP-UPDTE-LAST    PIC X(26).                               
           10 SQA205-CDE-UPDTE-LAST-ID PIC X(8).                                
      ******************************************************************        
      * THE NUMBER OF COLUMNS DESCRIBED BY THIS DECLARATION IS 6       *        
      ******************************************************************        
003910*    EXEC SQL INCLUDE TSQA203 END-EXEC.                           00003910
003910*    EXEC SQL INCLUDE TSQA204 END-EXEC.                           00003910
003920     EXEC SQL INCLUDE SQLCA   END-EXEC.                           00003920
************************************************                        *****   
* SQL INCLUDE FOR SQLCA                                                     *   
************************************************                        *****   
*****     EXEC SQL INCLUDE SQLCA  END-EXEC.                                     
        01 SQLCA.                                                               
        05 SQLCAID     PIC X(8).                                                
        05 SQLCABC     PIC S9(9) COMP-4.                                       B
        05 SQLCODE     PIC S9(9) COMP-4.                                       B
        05 SQLERRM.                                                            B
           49 SQLERRML PIC S9(4) COMP-4.                                       B
           49 SQLERRMC PIC X(70).                                              B
        05 SQLERRP     PIC X(8).                                               B
        05 SQLERRD     OCCURS 6 TIMES                                          B
                       PIC S9(9) COMP-4.                                        
        05 SQLWARN.                                                            B
           10 SQLWARN0 PIC X.                                                  B
           10 SQLWARN1 PIC X.                                                  B
           10 SQLWARN2 PIC X.                                                  B
           10 SQLWARN3 PIC X.                                                  B
           10 SQLWARN4 PIC X.                                                  B
           10 SQLWARN5 PIC X.                                                  B
           10 SQLWARN6 PIC X.                                                  B
           10 SQLWARN7 PIC X.                                                  B
        05 SQLEXT      PIC X(8).                                               B
            77 SQL-NULL      PIC S9(9) COMP-4 VALUE +0.                         
            77 SQL-INIT-FLAG PIC S9(4) COMP-4 VALUE +0.                         
             88 SQL-INIT-DONE VALUE +1.                                         
     01 SQL-PLIST6.                                                             
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2621440.                     
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +50.                          
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                   
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                   
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                   
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                           
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                    
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                           
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                           
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +223.                         
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +3.                          B
     01 SQL-PLIST7.                                                            B
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2621440.                    B
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +45.                         B
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                  B
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                  B
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                  B
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                          B
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                   B
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +235.                        B
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +5.                          B
     01 SQL-PLIST8.                                                            B
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2623488.                    B
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +30.                         B
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                  B
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                  B
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                  B
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                          B
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                   B
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +248.                        B
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +4.                          B
        05 SQL-AVAR-LIST8.                                                     B
           10 SQL-AVAR-SIZE  PIC S9(9) COMP-4 VALUE +292.                      B
           10 SQL-AVAR-DESCS.                                                  B
              15 SQL-AVAR-TYPE1 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN1  PIC S9(4) COMP-4 VALUE +8.                     B
           10 SQL-AVAR-ADDRS.                                                  B
              15 SQL-AVAR-ADDR1 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND1  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE2 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN2  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR2 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND2  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE3 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN3  PIC S9(4) COMP-4 VALUE +12.                    B
              15 SQL-AVAR-ADDR3 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND3  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE4 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN4  PIC S9(4) COMP-4 VALUE +6.                     B
              15 SQL-AVAR-ADDR4 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND4  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE5 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN5  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR5 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND5  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE6 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN6  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR6 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND6  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE7 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN7  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR7 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND7  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE8 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN8  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR8 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND8  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE9 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN9  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR9 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND9  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE10 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN10  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR10 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND10  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE11 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN11  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR11 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND11  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE12 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN12  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR12 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND12  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE13 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN13  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR13 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND13  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE14 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN14  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR14 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND14  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE15 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN15  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR15 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND15  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE16 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN16  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR16 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND16  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE17 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN17  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR17 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND17  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE18 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN18  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR18 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND18  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE19 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN19  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR19 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND19  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE20 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN20  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR20 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND20  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE21 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN21  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR21 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND21  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE22 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN22  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR22 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND22  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE23 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN23  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR23 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND23  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE24 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN24  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR24 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND24  PIC S9(9) COMP-4.                             B
     01 SQL-PLIST9.                                                            B
        05 SQL-PLIST-CON   PIC S9(9) COMP-4 VALUE +2623488.                    B
        05 SQL-CALLTYPE    PIC S9(4) COMP-4 VALUE +30.                         B
        05 SQL-PROG-NAME   PIC X(8)         VALUE 'HDPDB201'.                  B
        05 SQL-TIMESTAMP-1 PIC S9(9) COMP-4 VALUE +344583371.                  B
        05 SQL-TIMESTAMP-2 PIC S9(9) COMP-4 VALUE +148158384.                  B
        05 SQL-SECTION     PIC S9(4) COMP-4 VALUE +1.                          B
        05 SQL-CODEPTR     PIC S9(9) COMP-4.                                   B
        05 SQL-VPARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-APARMPTR    PIC S9(9) COMP-4 VALUE +0.                          B
        05 SQL-STMT-NUM    PIC S9(4) COMP-4 VALUE +292.                        B
        05 SQL-STMT-TYPE   PIC S9(4) COMP-4 VALUE +4.                          B
        05 SQL-AVAR-LIST9.                                                     B
           10 SQL-AVAR-SIZE  PIC S9(9) COMP-4 VALUE +292.                      B
           10 SQL-AVAR-DESCS.                                                  B
              15 SQL-AVAR-TYPE1 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN1  PIC S9(4) COMP-4 VALUE +8.                     B
           10 SQL-AVAR-ADDRS.                                                  B
              15 SQL-AVAR-ADDR1 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND1  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE2 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN2  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR2 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND2  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE3 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN3  PIC S9(4) COMP-4 VALUE +12.                    B
              15 SQL-AVAR-ADDR3 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND3  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE4 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN4  PIC S9(4) COMP-4 VALUE +6.                     B
              15 SQL-AVAR-ADDR4 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND4  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE5 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN5  PIC S9(4) COMP-4 VALUE +8.                     B
              15 SQL-AVAR-ADDR5 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND5  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE6 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN6  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR6 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND6  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE7 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN7  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR7 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND7  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE8 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN8  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR8 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND8  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE9 PIC S9(4) COMP-4 VALUE +452.                   B
              15 SQL-AVAR-LEN9  PIC S9(4) COMP-4 VALUE +1.                     B
              15 SQL-AVAR-ADDR9 PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-IND9  PIC S9(9) COMP-4.                              B
              15 SQL-AVAR-TYPE10 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN10  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR10 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND10  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE11 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN11  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR11 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND11  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE12 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN12  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR12 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND12  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE13 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN13  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR13 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND13  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE14 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN14  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR14 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND14  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE15 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN15  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR15 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND15  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE16 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN16  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR16 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND16  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE17 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN17  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR17 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND17  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE18 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN18  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR18 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND18  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE19 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN19  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR19 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND19  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE20 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN20  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR20 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND20  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE21 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN21  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR21 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND21  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE22 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN22  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR22 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND22  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE23 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN23  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR23 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND23  PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-TYPE24 PIC S9(4) COMP-4 VALUE +452.                  B
              15 SQL-AVAR-LEN24  PIC S9(4) COMP-4 VALUE +1.                    B
              15 SQL-AVAR-ADDR24 PIC S9(9) COMP-4.                             B
              15 SQL-AVAR-IND24  PIC S9(9) COMP-4.                             B
003930     EXEC SQL INCLUDE DB2AREA END-EXEC.                           00003930
000010************************************************************************  
000020**                                                                     *  
000030**      ****    ****     ***         ***    ****    *****    ***       *  
000040**      *   *   *   *   *   *       *   *   *   *   *       *   *      *  
000050**      *   *   ****       *  ****  *****   * *     ***     *****      *  
000060**      *   *   *   *    *          *   *   *  *    *       *   *      *  
000070**      ****    ****    *****       *   *   *   *   *****   *   *      *  
000080**                                                                     *  
000090***************************** DB2-AREA *********************************  
000100************************************************************************  
000110 01  DB2-AREA.                                                            
000120************************************************************************  
000130************************* SQL ERROR AREA *******************************  
000140************************************************************************  
000150     05  FILLER                PIC X(25) VALUE                            
000160                               '*************************'.               
000170     05  FILLER                PIC X(25) VALUE                            
000180                               '      SQL ERROR AREA     '.               
000190     05  FILLER                PIC X(25) VALUE                            
000200                               '*************************'.               
000210     05  SQLCA-ERROR-AREA      PIC X(100)  VALUE SPACES.                  
000220************************************************************************  
000230********************** DB2 ERROR MESSAGES ******************************  
000240************************************************************************  
000250     05  FILLER                PIC X(25) VALUE                            
000260                               '*************************'.               
000270     05  FILLER                PIC X(25) VALUE                            
000280                               '    DB2 ERROR MESSAGES   '.               
000290     05  FILLER                PIC X(25) VALUE                            
000300                               '*************************'.               
000310     05  DB2-ERROR-MESSAGES.                                              
000320         10  DSN8504.                                                     
000330             15  FILLER        PIC X(37)   VALUE                          
000340                 'DSN8504E - SQL ERROR, RETURN CODE IS:'.                 
000350             15  SQLCODE4      PIC -(10).                                 
000360         10  DSN8507.                                                     
000370             15  FILLER        PIC X(27)   VALUE                          
000380                 'DSN8507I - RECORD NOT FOUND'.                           
000390         10  DSN8508.                                                     
000400             15  FILLER        PIC X(25)   VALUE                          
000410                 'DSN8508E - ERROR DETECTED'.                             
000420             15  FILLER        PIC X(28)   VALUE                          
000430                 ' BY MESSAGE FORMAT ROUTINE, '.                          
000440             15  FILLER        PIC X(16)   VALUE                          
000450                 'RETURN CODE IS: '.                                      
000460             15  TIARCODE8     PIC -(10).                                 
000470************************************************************************  
000480************************DB2 CHECK RETURN CODE***************************  
000490************************************************************************  
000500     05  FILLER                PIC X(25) VALUE                            
000510                               '*************************'.               
000520     05  FILLER                PIC X(25) VALUE                            
000530                               '  DB2 CHECK RETURN CODE  '.               
000540     05  FILLER                PIC X(25) VALUE                            
000550                               '*************************'.               
000560     05  DB2-SQLCODE           PIC S9(9) COMP-4.                          
000570         88  DB2-SUCCESSFUL    VALUE +000.                                
000580         88  DB2-NOT-FOUND     VALUE +100.                                
000590         88  MEMBER-EXISTS     VALUE -803.                                
000591         88  DB2-MULTIPLE-ROWS-FOUND                                      
000592                               VALUE -811.                                
000600************************************************************************  
000610************************DB2 ERROR TABLE ********************************  
000620************************************************************************  
000630     05  FILLER                PIC X(25) VALUE                            
000640                               '*************************'.               
000650     05  FILLER                PIC X(25) VALUE                            
000660                               '     DB2 ERROR TABLE     '.               
000670     05  FILLER                PIC X(25) VALUE                            
000680                               '*************************'.               
000690     05  DB2-ERROR-MESSAGE.                                               
000700         10  DB2-ERROR-LEN     PIC S9(4)   COMP VALUE +960.               
000710         10  DB2-ERROR-TEXT    PIC X(960).                                
000720         10  DB2-ERROR-TEXT-REDF REDEFINES DB2-ERROR-TEXT                 
000730                               PIC X(120)  OCCURS 8 TIMES INDEXED         
000740                                           BY DB2-INDEX.                  
000750         10  DB2-ERROR-TEXT-LEN                                           
000760                               PIC S9(9) COMP VALUE +120.                 
000770************************************************************************  
000771*********************** DB2 DATE AND TIME DEFAULTS *********************  
000772************************************************************************  
000773     05  DB2-TIMESTAMP-DEFAULT PIC X(26) VALUE                            
000774                              '0001-01-01-01.01.01.111111'.               
000775     05  DB2-DATE-DEFAULT      PIC X(10) VALUE                            
000776                                              '0001-01-01'.               
000777     05  DB2-TIME-DEFAULT      PIC X(08) VALUE                            
000778                                                '01.01.01'.               
000779************************************************************************  
000780************************************************************************  
      *****************************************************************         
      *****************************************************************         
004000 01  MODULE-ID                   PIC X(8)  VALUE 'HLF11M54'.      00004000
004100 01  ABEND-CODE                  PIC 9(4)  VALUE 4000 COMP.       00004100
004100 01  DUMPCD                      PIC 9(4)  VALUE    0 COMP.       00004100
       01  LITERALS.                                                            
           05  LIT-1                   PIC X(01) VALUE '1'.                     
           05  LIT-2                   PIC X(01) VALUE '2'.                     
           05  LIT-3                   PIC X(01) VALUE '3'.                     
           05  LIT-4                   PIC X(01) VALUE '4'.                     
           05  LIT-5                   PIC X(01) VALUE '5'.                     
           05  LIT-6                   PIC X(01) VALUE '6'.                     
           05  LIT-7                   PIC X(01) VALUE '7'.                     
           05  LIT-8                   PIC X(01) VALUE '8'.                     
           05  LIT-CLEVELAND           PIC X(10) VALUE 'CLEVELAND '.            
           05  LIT-IHW-3SP             PIC X(10) VALUE 'IHW 3SP   '.            
           05  LIT-IHE-2SP             PIC X(10) VALUE 'IHE 2SP   '.            
           05  LIT-IHE-4SP             PIC X(10) VALUE 'IHE 4SP   '.            
       01  SAVE-AREA.                                                           
           05  SAVE-STRAND             PIC X(01) VALUE SPACE.                   
004200 01  WS-WORK-AREA.                                                00004200
004201     05  WS-NEW-HEAT.                                             00004201
               10  WS-NEW-HEAT-1       PIC X(1)  VALUE SPACES.                  
               10  WS-NEW-HEAT-2       PIC X(1)  VALUE SPACES.                  
               10  WS-NEW-HEAT-3-5     PIC X(3)  VALUE SPACES.                  
               10  WS-NEW-HEAT-6-7     PIC X(2)  VALUE SPACES.                  
004202     05  WS-OLD-HEAT.                                             00004202
               10  WS-OLD-HEAT-1       PIC X(1)  VALUE SPACES.                  
               10  WS-OLD-HEAT-2       PIC X(1)  VALUE SPACES.                  
               10  WS-OLD-HEAT-3-5     PIC X(3)  VALUE SPACES.                  
               10  WS-OLD-HEAT-6-7     PIC X(2)  VALUE SPACES.                  
004203     05  WS-CUR-SLAB.                                             00004203
               10  WS-CUR-SLAB-1       PIC X(1)  VALUE SPACES.                  
               10  WS-CUR-SLAB-2       PIC X(1)  VALUE SPACES.                  
               10  WS-CUR-SLAB-3-5     PIC X(3)  VALUE SPACES.                  
               10  WS-CUR-SLAB-6-7     PIC X(2)  VALUE SPACES.                  
004204     05  WS-NEW-SLAB             PIC X(7)  VALUE SPACES.          00004204
004206     05  WS-GRADE-LEVEL          PIC X(2)  VALUE SPACES.          00004206
           05  WS-FGN-SLAB-COND.                                                
               10  WS-FGN-SLAB-COND-1  PIC X     VALUE SPACES.                  
               10  WS-FGN-SLAB-COND-2  PIC X     VALUE SPACES.                  
               10  WS-FGN-SLAB-COND-3  PIC X     VALUE SPACES.                  
               10  WS-FGN-SLAB-COND-4  PIC X     VALUE SPACES.                  
004207     05  WS-PRIME-CHECK          PIC X(1)  VALUE SPACES.          00004207
004208         88  NEW-GRADE-PRIME     VALUE 'Y'.                       00004208
004209         88  NEW-GRADE-NOT-PRIME VALUE 'N'.                       00004209
004210     05  WS-DOWN-CHECK           PIC X(1)  VALUE SPACES.          00004210
004220         88  CUR-GRADE-DOWN      VALUE 'Y'.                       00004220
004230         88  CUR-GRADE-NOT-DOWN  VALUE 'N'.                       00004230
004300 COPY HLFRPIMS.                                                   00004300
000010 01  IMS-WORK-MODULE.                                                     
000020     05 MODULE-RETURN-CODE.                                               
000030        10  MODULE-ERR-NBR              PIC 9999    VALUE ZERO.           
000040******************************************************************        
000050****         I M S   C O M M A N D   F U N C T I O N S        ****        
000060******************************************************************        
000070     05  IMS-CALL-FUNCTION              PIC X(4)    VALUE '    '.         
000080     05  IMS-GU                         PIC X(4)    VALUE 'GU  '.         
000090     05  IMS-GN                         PIC X(4)    VALUE 'GN  '.         
000100     05  IMS-GNP                        PIC X(4)    VALUE 'GNP '.         
000110     05  IMS-GHU                        PIC X(4)    VALUE 'GHU '.         
000120     05  IMS-GHN                        PIC X(4)    VALUE 'GHN '.         
000130     05  IMS-GHNP                       PIC X(4)    VALUE 'GHNP'.         
000140     05  IMS-ISRT                       PIC X(4)    VALUE 'ISRT'.         
000150     05  IMS-REPL                       PIC X(4)    VALUE 'REPL'.         
000160     05  IMS-DLET                       PIC X(4)    VALUE 'DLET'.         
000170     05  IMS-CHKP                       PIC X(4)    VALUE 'CHKP'.         
000180     05  IMS-PURG                       PIC X(4)    VALUE 'PURG'.         
000190     05  IMS-ROLL                       PIC X(4)    VALUE 'ROLL'.         
000200     05  IMS-CHNG                       PIC X(4)    VALUE 'CHNG'.         
000210     05  IMS-PARM-COUNT                 PIC S9(7)   COMP.                 
000220******************************************************************        
000230****         I M S   S S A   T O O L S                        ****        
000240******************************************************************        
000250     05  IMS-UNQUALIFIER                PIC X    VALUE ' '.               
000260     05  IMS-LEFT-PAREN                 PIC X    VALUE '('.               
000270     05  IMS-RIGHT-PAREN                PIC X    VALUE ')'.               
000280     05  IMS-NULL                       PIC X    VALUE '-'.               
000290     05  IMS-AMPERSAND                  PIC X    VALUE '&'.               
000300     05  IMS-ASTERIK                    PIC X    VALUE '*'.               
000310     05  IMS-PATH                       PIC X    VALUE 'D'.               
000320******************************************************************        
000330****         I M S   K E Y  O P E R A T O R S                 ****        
000340******************************************************************        
000350     05  IMS-EQUAL                      PIC XX   VALUE ' ='.              
000360     05  IMS-LESS-THAN                  PIC XX   VALUE ' <'.              
000370     05  IMS-GREATER-THAN               PIC XX   VALUE ' >'.              
000380******************************************************************        
000390*                                                                *        
000400*            I M S   R E T U R N   C O D E S                     *        
000410*                                                                *        
000420******************************************************************        
000430     05  IMS-CHECK-RETURN-CODE          PIC XX   VALUE SPACE.             
000440         88  IMS-CALL-SUCCESSFUL                 VALUE '  '.              
000450         88  IMS-SEG-NOT-FOUND                   VALUE 'GE'.              
000460         88  IMS-SEG-ALREADY-INSERTED            VALUE 'II'.              
000470         88  IMS-END-OF-DATABASE                 VALUE 'GB'.              
000480         88  IMS-END-OF-MESSAGE                  VALUE 'QD'.              
000490         88  IMS-NO-MORE-MESSAGES                VALUE 'QC'.              
000500         88  IMS-ISRT-DEST-UNKNOWN               VALUE 'QH'.              
000510******************************************************************        
000520****         I M S   F O R M A T   A T T R I B U T E S        ****        
000530******************************************************************        
000540     05  IMS-NON-PROT-HILITE            PIC S999  VALUE +136 COMP.        
000550     05  IMS-NON-PROT-LOLITE            PIC S999  VALUE +128 COMP.        
000560     05  IMS-PROT                       PIC S999  VALUE +241 COMP.        
000570     05  IMS-UNPROTECT                  PIC S999  VALUE +193 COMP.        
000580     05  IMS-NUM-MOD-HILITE             PIC S999  VALUE +217 COMP.        
000590     05  IMS-MOD-PROT                   PIC S999  VALUE +225 COMP.        
034700                                                                  00034700
034710 01  FILLER                      PIC X(38)                        00034710
034800               VALUE '****END OF WORKING STORAGE SECTION****'.    00034800
034900 LINKAGE SECTION.                                                 00034900
034910 COPY HLF11K0F.                                                   00034910
000100 01  HLF11K0F.                                                    00000100
000200     05  K0F-CALLING-PGM                  PIC X(08).              00000200
000300     05  K0F-INPUTS.                                              00000300
000400         10  K0F-OLD-HEAT-GRADE           PIC X(7).               00000400
000500         10  K0F-NEW-HEAT-GRADE           PIC X(7).               00000500
000600         10  K0F-CUR-SLAB-GRADE           PIC X(7).               00000600
000700         10  K0F-HSC.                                             00000700
000800             15  K0F-HEAT                 PIC X(9).               00000800
000900             15  K0F-SLAB                 PIC X(2).               00000900
001000             15  K0F-CUT                  PIC X(1).               00001000
001100         10  K0F-CDE-FGN-SLAB-COND        PIC X(4).               00001100
001101         10  K0F-NON-BH-HEAT              PIC X(20).              00001101
001110         10  K0F-NON-BH-SLAB              PIC X(20).              00001110
001200     05  K0F-OUTPUTS.                                             00001200
001300         10  K0F-NEW-SLAB-LEVEL           PIC X(2).               00001300
001400         10  K0F-NEW-SLAB-GRADE           PIC X(7).               00001400
001500         10  K0F-NAM-PRODUCE-FROM         PIC X(10).              00001500
001600         10  K0F-ERROR-MSG.                                       00001600
001700             15  K0F-ERROR-TXT            PIC X(30).              00001700
001800             15  K0F-ERROR-DATA           PIC X(21).              00001800
036000                                                                  00036000
036100 PROCEDURE DIVISION USING HLF11K0F.                               00036100
036200                                                                  00036200
036300 A0-MAINLINE.                                                     00036300

036324     MOVE SPACES                   TO K0F-OUTPUTS                 00036325
                                            WS-OLD-HEAT                         
                                            WS-CUR-SLAB                         
                                            WS-NEW-HEAT.                        

           PERFORM PROCESS-NON-BH-DOWNGRADE.                                    

036510     GOBACK.                                                      00036510
036505                                                                  00036505
036600                                                                  00036600
036900*:--------------------------------------------------------------  00036900
037200*:--------------------------------------------------------------  00037200

       PROCESS-NON-BH-DOWNGRADE.                                                

           INSPECT K0F-CUR-SLAB-GRADE REPLACING ALL LOW-VALUES BY SPACES        
036323     MOVE K0F-CUR-SLAB-GRADE (1:1) TO WS-CUR-SLAB-1               00036323
036323     MOVE '_'                      TO WS-CUR-SLAB-2               00036323
036324     MOVE K0F-CUR-SLAB-GRADE (3:3) TO WS-CUR-SLAB-3-5             00036324
036324     MOVE '__'                     TO WS-CUR-SLAB-6-7             00036324

           INSPECT K0F-CDE-FGN-SLAB-COND                                        
               REPLACING ALL LOW-VALUES BY SPACES                               

           IF K0F-NEW-SLAB-LEVEL = SPACES                                       
               PERFORM DETERMINE-NON-BH-DG-LEVEL                                
           ELSE                                                                 
               MOVE K0F-NEW-SLAB-LEVEL TO WS-GRADE-LEVEL                        
           END-IF                                                               
           IF WS-GRADE-LEVEL = SPACES OR ZEROS                          00036410
              DISPLAY 'QL = 0 OR SPACE, CHANGED TO 01'                          
              MOVE '01'                TO WS-GRADE-LEVEL                        
           END-IF                                                       00036410

036410     IF WS-GRADE-LEVEL = SPACES                                   00036410
              IF K0F-ERROR-TXT = SPACES                                         
036420           MOVE 'DOWN GRADE LEVEL ERR' TO K0F-ERROR-TXT           00036420
              END-IF                                                            
036421        MOVE K0F-INPUTS             TO K0F-ERROR-DATA             00036421
036430        MOVE SPACES                 TO K0F-NEW-SLAB-GRADE         00036430
036440     ELSE                                                         00036440
              MOVE WS-CUR-SLAB            TO SQA201-CDE-GRADE                   
              PERFORM DETERMINE-NEW-GRADE                                       
036442        IF WS-NEW-SLAB = SPACES                                   00036442
036443           MOVE 'DOWN GRADE NOT FND' TO K0F-ERROR-TXT             00036443
036444           MOVE K0F-INPUTS           TO K0F-ERROR-DATA            00036444
036445           MOVE SPACES               TO K0F-NEW-SLAB-GRADE        00036445
036446        ELSE                                                      00036446
                 PERFORM DETERMINE-SLAB-GRADE-2                                 
      **** SET CLEVELAND SLABS (1ST POS = 9) 6TH ALPHA TO 'C',          00037502
      **** DON'T GO THROUGH OTHER LOGIC FOR THEM.                       00037502
      *QA9R9282                                                         00037502
                 IF K0F-NON-BH-HEAT (1:1) = '9'                         00037502
                 AND K0F-NON-BH-SLAB (1:1) NOT = '-'                    00037502
                    MOVE 'C'           TO WS-NEW-SLAB (6:1)             00036447
                 ELSE                                                   00036323
                    IF SQA202-CDE-FGN-GRADE > SPACES                       0003 
                       MOVE SQA202-CDE-FGN-GRADE                           0003 
                                          TO WS-NEW-SLAB (6:1)             0003 
                    ELSE                                                   0003 
                       PERFORM DETERMINE-SLAB-GRADE-6                           
                    END-IF                                                  0003
                    IF  WS-CUR-SLAB-1  = 'A'                                    
                    AND WS-CUR-SLAB-3-5 = '800'                                 
                       PERFORM DETERMINE-SLAB-GRADE-7                           
                    END-IF                                                      
                 END-IF                                                 00036323
      *          IF K0F-NON-BH-HEAT (1:1) = '9'                         00037502
      *             MOVE 'C'           TO WS-NEW-SLAB (6:1)             00036447
      *          ELSE                                                   00036323
      *             PERFORM DETERMINE-SLAB-GRADE-6                              
      *             IF  WS-CUR-SLAB-1  = 'A'                                0003
      *             AND WS-CUR-SLAB-3-5 = '800'                             0003
      *                 PERFORM DETERMINE-SLAB-GRADE-7                          
      *             END-IF                                                  0003
      *          END-IF                                                 00036323
      *QA9R9282 END                                                        0003 
                                                                        00036323
                 MOVE WS-NEW-SLAB      TO K0F-NEW-SLAB-GRADE            00036447
                 IF K0F-NEW-SLAB-GRADE (6:1) NOT > SPACES                       
                    MOVE K0F-CUR-SLAB-GRADE (6:1) TO                            
                                          K0F-NEW-SLAB-GRADE (6:1)              
                 END-IF                                                         
                 IF K0F-NEW-SLAB-GRADE (7:1) NOT > SPACES                       
                    MOVE K0F-CUR-SLAB-GRADE (7:1) TO                            
                                          K0F-NEW-SLAB-GRADE (7:1)              
                 END-IF                                                         
              END-IF                                                    00036461
           END-IF.                                                      00036470

       DETERMINE-NON-BH-DG-LEVEL.                                               

           PERFORM DETERMINE-PRODUCED-FROM                                      
           IF K0F-NAM-PRODUCE-FROM > SPACES                                     
               PERFORM SELECT-FOR-LEVEL                                         
           ELSE                                                                 
036492         MOVE K0F-INPUTS           TO K0F-ERROR-DATA              00036492
036493         MOVE 'BAD PRODUCED FROM ' TO K0F-ERROR-TXT               00036493
           END-IF.                                                              

       DETERMINE-PRODUCED-FROM.                                                 

           MOVE SPACES                  TO K0F-NAM-PRODUCE-FROM                 
           EVALUATE TRUE                                                        
             WHEN K0F-HEAT = SPACES                                             
                 CONTINUE                                                       
             WHEN K0F-HEAT (1:1) = LIT-7                                        
                 MOVE LIT-CLEVELAND     TO K0F-NAM-PRODUCE-FROM                 
      *QA9R9320 IHW HEAT PREFIX NO LONGER '5',                             0003 
      *         USE 3RD POS TO DISTINGUISH IHW FROM IHE                    0003 
      *      WHEN K0F-HEAT (1:1) = LIT-5                                        
      *          MOVE LIT-IHW-3SP       TO K0F-NAM-PRODUCE-FROM                 
             WHEN K0F-HEAT (1:1) = LIT-1 AND                                    
                 (K0F-HEAT (3:1) = LIT-3 OR LIT-4)                              
                 MOVE LIT-IHW-3SP       TO K0F-NAM-PRODUCE-FROM                 
      *QA9R9320 END                                                        0003 
      **** IHE 2SP CLOSED, ANY HEAT POS '1' = IHE 4SP                           
      ****** WHEN K0F-HEAT (1:1) = LIT-1 AND                                    
      *          (K0F-HEAT (3:1) = LIT-1 OR LIT-2)                              
      *          MOVE LIT-IHE-2SP       TO K0F-NAM-PRODUCE-FROM                 
      *      WHEN K0F-HEAT (1:1) = LIT-1 AND                                    
      ******     (K0F-HEAT (3:1) = LIT-5 OR LIT-6)                              
      *QA9R9320 USE 3RD POS TO DISTINGUISH IHE FROM IHW                    0003 
      *      WHEN K0F-HEAT (1:1) = LIT-1                                        
             WHEN K0F-HEAT (1:1) = LIT-1 AND                                    
                 (K0F-HEAT (3:1) = LIT-5 OR LIT-6)                              
                 MOVE LIT-IHE-4SP       TO K0F-NAM-PRODUCE-FROM                 
      *QA9R9320 END                                                        0003 
           END-EVALUATE.                                                        

       SELECT-FOR-LEVEL.                                                        

      **** CLEVELAND HAS 2 CHAR CONDITION CODE                          00036310
      **** FOR CLEV, WE ONLY CARE ABOUT THE CONDITION CODE              00036310
      **** REGARDLESS OF GRADE, SO MOVE 'XXXXXXX' TO GRADE              00036310
      **** TSQA202 TABLE IS SET UP THIS WAY FOR CLEV ONLY               00036310
           IF K0F-NAM-PRODUCE-FROM = 'CLEVELAND'                                
036310        MOVE K0F-CDE-FGN-SLAB-COND (1:2)                          00036310
036310                                 TO SQA202-CDE-FGN-SLAB-COND      00036310
              MOVE ALL 'X'             TO SQA202-CDE-BH-GRADE                   
           ELSE                                                                 
036310        MOVE K0F-CDE-FGN-SLAB-COND (2:1)                          00036310
036310                                 TO SQA202-CDE-FGN-SLAB-COND      00036310
              MOVE WS-CUR-SLAB         TO SQA202-CDE-BH-GRADE                   
           END-IF                                                               
036310**** MOVE K0F-CDE-FGN-SLAB-COND (2:1)                             00036310
036310****   TO WS-FGN-SLAB-COND-2                                      00036310
           MOVE SPACES                 TO WS-GRADE-LEVEL                        
037502     MOVE K0F-NAM-PRODUCE-FROM   TO SQA202-NAM-PRODUCE-FROM       00037502
      **** MOVE WS-FGN-SLAB-COND-2     TO SQA202-CDE-FGN-SLAB-COND              

      *QA9R9282 ADD FOREIGN GRADE COLUMN TO SELECT (6TH ALPHA)             0003 
039420     EXEC SQL                                                     00039420
039430         SELECT CDE_QUALITY_LEVEL, CDE_FGN_GRADE                  00039430
039440         INTO   :SQA202-CDE-QUALITY-LEVEL,                        00039440
                      :SQA202-CDE-FGN-GRADE                             00039440
039450         FROM   DHSQA2.TSQA202                                    00039450
004496           WHERE  NAM_PRODUCE_FROM = :SQA202-NAM-PRODUCE-FROM     00004496
                   AND  CDE_BH_GRADE LIKE :SQA202-CDE-BH-GRADE                  
                   AND  CDE_FGN_SLAB_COND = :SQA202-CDE-FGN-SLAB-COND           
004497        ORDER BY NAM_PRODUCE_FROM, CDE_BH_GRADE, CDE_FGN_SLAB_COND00004497
039480     END-EXEC.                                                    00039480
039490     MOVE SQLCODE                      TO DB2-SQLCODE             00039490
039490     DISPLAY 'TSQA202 SQLCODE = '         DB2-SQLCODE             00039490
037502     DISPLAY 'NAM-PRODUCE-FROM = ' SQA202-NAM-PRODUCE-FROM        00037502
039491                                                                  00039491
039492     IF  DB2-SUCCESSFUL                                           00039492
               MOVE SQA202-CDE-QUALITY-LEVEL TO WS-GRADE-LEVEL                  
                                                K0F-NEW-SLAB-LEVEL              
039496     ELSE                                                         00039496
039497         MOVE SPACES TO WS-NEW-SLAB                               00039497
                              WS-GRADE-LEVEL                                    
036492         MOVE K0F-INPUTS           TO K0F-ERROR-DATA              00036492
036493         MOVE 'NO QUAL LEVEL FND ' TO K0F-ERROR-TXT               00036493
039498     END-IF.                                                      00039498

       DETERMINE-SLAB-GRADE-2.                                                  

      *IF NO NEW HEAT GRADE 2ND ALPHA, USE CURRENT HEAT GRADE'S                 
           IF WS-NEW-SLAB (2:1) = SPACE                                         
               MOVE K0F-CUR-SLAB-GRADE (2:1) TO WS-NEW-SLAB (2:1)               
           END-IF.                                                              

       DETERMINE-SLAB-GRADE-6.                                                  

           MOVE K0F-NON-BH-HEAT (2:1)  TO SQA205-CDE-FGN-SLAB-POS2.     00037502
           MOVE K0F-NON-BH-SLAB (2:1)  TO SQA205-CDE-FGN-SLAB-POS8.     00036310

           EXEC SQL                                                     00039420
               SELECT CDE_GRD_ALPHA6                                    00039430
               INTO   :SQA205-CDE-GRD-ALPHA6                            00039440
               FROM   DHSQA2.TSQA205                                    00039450
                 WHERE  CDE_FGN_SLAB_POS2 = :SQA205-CDE-FGN-SLAB-POS2   00004496
                   AND  CDE_FGN_SLAB_POS8 = :SQA205-CDE-FGN-SLAB-POS8           
               ORDER BY CDE_FGN_SLAB_POS2,                              00004497
                        CDE_FGN_SLAB_POS8 ASC                           00004497
           END-EXEC.                                                    00039480
           MOVE SQLCODE                TO DB2-SQLCODE.                  00039490
           DISPLAY 'TSQA205 ALPHA 6 SQLCODE = ' DB2-SQLCODE             00039490
                                                                        00039491
           IF  DB2-SUCCESSFUL                                           00039492
               MOVE SQA205-CDE-GRD-ALPHA6                                       
                                       TO WS-NEW-SLAB (6:1)                     
                                          K0F-NEW-SLAB-GRADE (6:1)              
           END-IF.                                                      00039498
037502**** MOVE K0F-NAM-PRODUCE-FROM    TO SQA203-NAM-PRODUCE-FROM      00037502
036310*    MOVE K0F-CDE-FGN-SLAB-COND (3:1)                             00036310
036310*      TO SQA203-CDE-FGN-SLAB-COND3                               00036310
      *                                                                         
039420*    EXEC SQL                                                     00039420
039430*        SELECT CDE_GRADE_6                                       00039430
039440*        INTO   :SQA203-CDE-GRADE-6                               00039440
039450*        FROM   DHSQA2.TSQA203                                    00039450
004496*          WHERE  NAM_PRODUCE_FROM = :SQA203-NAM-PRODUCE-FROM     00004496
      *            AND  CDE_FGN_SLAB_COND3 = :SQA203-CDE-FGN-SLAB-COND3         
004497*        ORDER BY NAM_PRODUCE_FROM,                               00004497
004497*                 CDE_FGN_SLAB_COND3                              00004497
039480*    END-EXEC.                                                    00039480
039490*    MOVE SQLCODE                      TO DB2-SQLCODE             00039490
039491*                                                                 00039491
039492*    IF  DB2-SUCCESSFUL                                           00039492
      *        MOVE SQA203-CDE-GRADE-6                                          
      *                                      TO WS-NEW-SLAB (6:1)               
      *                                         K0F-NEW-SLAB-GRADE (6:1)        
039498**** END-IF.                                                      00039498

       DETERMINE-SLAB-GRADE-7.                                                  

           MOVE K0F-NON-BH-HEAT (2:1)  TO SQA205-CDE-FGN-SLAB-POS2.     00037502
           MOVE K0F-NON-BH-SLAB (2:1)  TO SQA205-CDE-FGN-SLAB-POS8.     00036310

           EXEC SQL                                                     00039420
               SELECT CDE_GRD_ALPHA7                                    00039430
               INTO   :SQA205-CDE-GRD-ALPHA7                            00039440
               FROM   DHSQA2.TSQA205                                    00039450
                 WHERE  CDE_FGN_SLAB_POS2 = :SQA205-CDE-FGN-SLAB-POS2   00004496
                   AND  CDE_FGN_SLAB_POS8 = :SQA205-CDE-FGN-SLAB-POS8           
               ORDER BY CDE_FGN_SLAB_POS2,                              00004497
                        CDE_FGN_SLAB_POS8 ASC                           00004497
           END-EXEC.                                                    00039480
           MOVE SQLCODE                TO DB2-SQLCODE.                  00039490
           DISPLAY 'TSQA205 ALPHA 7 SQLCODE = ' DB2-SQLCODE             00039490
                                                                        00039491
           IF  DB2-SUCCESSFUL                                           00039492
               MOVE SQA205-CDE-GRD-ALPHA7                                       
                                       TO WS-NEW-SLAB (7:1)                     
                                          K0F-NEW-SLAB-GRADE (7:1)              
           END-IF.                                                      00039498
      * USES THE 1ST AND 4TH POSITIONS OF THE CONDITION CODE TO FIND THE        
      * 7TH POSITION OF GRADE                                                   
036310**** MOVE K0F-CDE-FGN-SLAB-COND (1:1)                             00036310
      *      TO SQA204-CDE-FGN-SLAB-COND1                                       
036310*    MOVE K0F-CDE-FGN-SLAB-COND (4:1)                             00036310
      *      TO SQA204-CDE-FGN-SLAB-COND4                                       
      *                                                                         
039420*    EXEC SQL                                                     00039420
039430*        SELECT CDE_GRADE_7                                       00039430
039440*        INTO   :SQA204-CDE-GRADE-7                               00039440
039450*        FROM   DHSQA2.TSQA204                                    00039450
      *          WHERE  CDE_FGN_SLAB_COND1 =                                    
      *                 :SQA204-CDE-FGN-SLAB-COND1                              
      *            AND  CDE_FGN_SLAB_COND4 =                                    
      *                 :SQA204-CDE-FGN-SLAB-COND4                              
004497*        ORDER BY CDE_FGN_SLAB_COND1,                             00004497
004497*                 CDE_FGN_SLAB_COND4                              00004497
039480*    END-EXEC.                                                    00039480
039490*    MOVE SQLCODE                      TO DB2-SQLCODE             00039490
039491*                                                                 00039491
039492*    IF  DB2-SUCCESSFUL                                           00039492
      *        MOVE SQA204-CDE-GRADE-7       TO WS-NEW-SLAB (7:1)               
      *                                         K0F-NEW-SLAB-GRADE (7:1)        
039498**** END-IF.                                                      00039498

038304                                                                  00038304
038305*---------------------------------------------------------------  00038305
038310*---------------------------------------------------------------  00038310
<USER> <GROUP>.                                             00038400
038610                                                                  00038610
           INSPECT WS-GRADE-LEVEL REPLACING ALL LOW-VALUES BY ZEROES            
           INSPECT WS-GRADE-LEVEL REPLACING ALL SPACES     BY ZEROES            
039412     MOVE WS-GRADE-LEVEL               TO SQA201-CDE-QUALITY-LEVEL00039412
039420     EXEC SQL                                                     00039420
039430         SELECT CDE_FINAL_GRADE                                   00039430
039440         INTO   :SQA201-CDE-FINAL-GRADE                           00039440
039450         FROM   DHSQA2.TSQA201                                    00039450
039460           WHERE  CDE_GRADE LIKE :SQA201-CDE-GRADE                00039460
039470             AND  CDE_QUALITY_LEVEL = :SQA201-CDE-QUALITY-LEVEL   00039470
                 ORDER BY CDE_GRADE, CDE_QUALITY_LEVEL ASC                      
039480     END-EXEC.                                                    00039480
039490     MOVE SQLCODE                      TO DB2-SQLCODE             00039490
           DISPLAY 'TSQA201 SQLCODE = ' DB2-SQLCODE                     00039490
039491                                                                  00039491
039492     IF  DB2-SUCCESSFUL                                           00039492
               MOVE SQA201-CDE-FINAL-GRADE   TO WS-NEW-SLAB                     
039496     ELSE                                                         00039496
039497         MOVE SPACES TO WS-NEW-SLAB                               00039497
039498     END-IF.                                                      00039498
039499                                                                  00039499
039500                                                                  00039500
039510*---------------------------------------------------------------  00039510
039600*---------------------------------------------------------------  00039600
<USER>                                                                  <GROUP>
076912 Z99-ERROR-ROUTINE.                                               00076912
076913     INITIALIZE DB2-ERROR-TEXT                                    00076913
076920     MOVE SQLCA                  TO SQLCA-ERROR-AREA              00076920
076930     MOVE SQLCODE                TO SQLCODE4                      00076930
076940     CALL 'DSNTIAR' USING SQLCA                                   00076940
076950                          DB2-ERROR-MESSAGE                       00076950
076960                          DB2-ERROR-TEXT-LEN                      00076960
076970                                                                  00076970
076980     IF RETURN-CODE = ZERO                                        00076980
076990          CONTINUE                                                00076990
076991     ELSE                                                         00076991
076992          MOVE RETURN-CODE        TO TIARCODE8                    00076992
076993     END-IF                                                       00076993
076994                                                                  00076994
077100     CALL 'DUMPER' USING ABEND-CODE.                              00077100

040300 DUMPER.                                                          00040300
040400*---------------------------------------------------------------  00040400
040700*---------------------------------------------------------------  00040700
<USER>     <GROUP> 'DUMPER' USING DUMPCD.                                  00040800
040900                                                                  00040900
