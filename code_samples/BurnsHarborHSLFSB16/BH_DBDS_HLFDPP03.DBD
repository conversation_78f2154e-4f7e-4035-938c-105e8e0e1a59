*                                                                       00000010
*   9/20/15 MN ADD CDC EXIT ROUTINE - HLFDP03A & HLFDP03D               00000010
*   1/17/16 MN ADD CDC EXIT ROUTINE - HLFDP03C                          00000010
*                                                                       00000010
*              ***********************************************          00000010
*              *     LFRP WORK DATA BASE - NUMBER 2          *          00000020
*              *                                             *          00000030
*              *     ALL SEGMENTS IN THIS DATA BASE ARE      *          00000040
*              *   VARIABLE LENGTH.  ALL SEGMENTS, EXCEPT    *          00000050
*              *   FOR THE "B" SEGMENT, HAVE KEYS.           *          00000060
*              *                                             *          00000070
*              ***********************************************          00000080
       DBD     NAME=HLFDPP03,ACCESS=(HDAM,OSAM),                       X00000090
               RMNAME=(DFSHDC40,2,997,18000)                            00000100
       DATASET DD1=HLFDPP03,DEVICE=3380,SIZE=6144                       00000110
*                                                                       00000120
*              ROOT SEGMENT - MIN LENGTH 18, MAX LENGTH 2048            00000130
*                                                                       00000140
       SEGM    NAME=HLFDP03A,PARENT=0,BYTES=(2048,18),POINTER=TWIN,    X00000150
               FREQ=1500,                                              X00000160
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                 00000160
       FIELD   NAME=(ADP03KEY,SEQ,U),BYTES=16,START=3,TYPE=C            00000170
*                                                                       00000180
*              'B' SEGMENT - UNKEYED - MIN LENGTH 4, MAX LENGTH 2048    00000190
*                                                                       00000200
       SEGM    NAME=HLFDP03B,PARENT=((HLFDP03A,DBLE)),FREQ=7,          X00000210
               POINTER=TWINBWD,BYTES=(2048,4)                           00000220
*                                                                       00000230
*              'C' SEGMENT - MIN LENGTH 18, MAX LENGTH 2048             00000240
*                                                                       00000250
       SEGM    NAME=HLFDP03C,PARENT=((HLFDP03A,SNGL)),FREQ=17,         X00000260
               POINTER=TWINBWD,BYTES=(2048,18),                        X00000270
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                 00000160
       FIELD   NAME=(CDP03KEY,SEQ,U),BYTES=16,START=3,TYPE=C            00000280
       FIELD   NAME=CDP03ORD,BYTES=9,START=3,TYPE=C                     00000290
*                                                                       00000300
*              'D' SEGMENT - MIN LENGTH 10, MAX LENGTH 1024             00000310
*                                                                       00000320
       SEGM    NAME=HLFDP03D,PARENT=((HLFDP03A,SNGL)),FREQ=3,          X00000330
               POINTER=TWINBWD,BYTES=(2048,10),                        X00000340
               EXIT=(*,KEY,DATA,NOPATH,(NOCASCADE),LOG)                 00000340
       FIELD   NAME=(DDP03KEY,SEQ,U),BYTES=8,START=3,TYPE=C             00000350
       DBDGEN                                                           00000360
       FINISH                                                           00000370
       END                                                              00000380
